(()=>{var e={887:(e,r,t)=>{"use strict";function o(e,r=!1){const t=e.length;let o=0,a="",c=0,l=16,p=0,u=0,g=0,d=0,m=0;function h(r,t){let s=0,n=0;for(;s<r||!t;){let r=e.charCodeAt(o);if(r>=48&&r<=57)n=16*n+r-48;else if(r>=65&&r<=70)n=16*n+r-65+10;else{if(!(r>=97&&r<=102))break;n=16*n+r-97+10}o++,s++}return s<r&&(n=-1),n}function f(){if(a="",m=0,c=o,u=p,d=g,o>=t)return c=t,l=17;let r=e.charCodeAt(o);if(s(r)){do{o++,a+=String.fromCharCode(r),r=e.charCodeAt(o)}while(s(r));return l=15}if(n(r))return o++,a+=String.fromCharCode(r),13===r&&10===e.charCodeAt(o)&&(o++,a+="\n"),p++,g=o,l=14;switch(r){case 123:return o++,l=1;case 125:return o++,l=2;case 91:return o++,l=3;case 93:return o++,l=4;case 58:return o++,l=6;case 44:return o++,l=5;case 34:return o++,a=function(){let r="",s=o;for(;;){if(o>=t){r+=e.substring(s,o),m=2;break}const i=e.charCodeAt(o);if(34===i){r+=e.substring(s,o),o++;break}if(92!==i){if(i>=0&&i<=31){if(n(i)){r+=e.substring(s,o),m=2;break}m=6}o++}else{if(r+=e.substring(s,o),o++,o>=t){m=2;break}switch(e.charCodeAt(o++)){case 34:r+='"';break;case 92:r+="\\";break;case 47:r+="/";break;case 98:r+="\b";break;case 102:r+="\f";break;case 110:r+="\n";break;case 114:r+="\r";break;case 116:r+="\t";break;case 117:const e=h(4,!0);e>=0?r+=String.fromCharCode(e):m=4;break;default:m=5}s=o}}return r}(),l=10;case 47:const s=o-1;if(47===e.charCodeAt(o+1)){for(o+=2;o<t&&!n(e.charCodeAt(o));)o++;return a=e.substring(s,o),l=12}if(42===e.charCodeAt(o+1)){o+=2;const r=t-1;let i=!1;for(;o<r;){const r=e.charCodeAt(o);if(42===r&&47===e.charCodeAt(o+1)){o+=2,i=!0;break}o++,n(r)&&(13===r&&10===e.charCodeAt(o)&&o++,p++,g=o)}return i||(o++,m=1),a=e.substring(s,o),l=13}return a+=String.fromCharCode(r),o++,l=16;case 45:if(a+=String.fromCharCode(r),o++,o===t||!i(e.charCodeAt(o)))return l=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return a+=function(){let r=o;if(48===e.charCodeAt(o))o++;else for(o++;o<e.length&&i(e.charCodeAt(o));)o++;if(o<e.length&&46===e.charCodeAt(o)){if(o++,!(o<e.length&&i(e.charCodeAt(o))))return m=3,e.substring(r,o);for(o++;o<e.length&&i(e.charCodeAt(o));)o++}let t=o;if(o<e.length&&(69===e.charCodeAt(o)||101===e.charCodeAt(o)))if(o++,(o<e.length&&43===e.charCodeAt(o)||45===e.charCodeAt(o))&&o++,o<e.length&&i(e.charCodeAt(o))){for(o++;o<e.length&&i(e.charCodeAt(o));)o++;t=o}else m=3;return e.substring(r,t)}(),l=11;default:for(;o<t&&T(r);)o++,r=e.charCodeAt(o);if(c!==o){switch(a=e.substring(c,o),a){case"true":return l=8;case"false":return l=9;case"null":return l=7}return l=16}return a+=String.fromCharCode(r),o++,l=16}}function T(e){if(s(e)||n(e))return!1;switch(e){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return!1}return!0}return{setPosition:function(e){o=e,a="",c=0,l=16,m=0},getPosition:()=>o,scan:r?function(){let e;do{e=f()}while(e>=12&&e<=15);return e}:f,getToken:()=>l,getTokenValue:()=>a,getTokenOffset:()=>c,getTokenLength:()=>o-c,getTokenStartLine:()=>u,getTokenStartCharacter:()=>c-d,getTokenError:()=>m}}function s(e){return 32===e||9===e}function n(e){return 10===e||13===e}function i(e){return e>=48&&e<=57}var a,c;function l(e,r,t){let s,n,i,a,c;if(r){for(a=r.offset,c=a+r.length,i=a;i>0&&!u(e,i-1);)i--;let o=c;for(;o<e.length&&!u(e,o);)o++;n=e.substring(i,o),s=function(e,r){let t=0,o=0;const s=r.tabSize||4;for(;t<e.length;){let r=e.charAt(t);if(" "===r)o++;else{if("\t"!==r)break;o+=s}t++}return Math.floor(o/s)}(n,t)}else n=e,s=0,i=0,a=0,c=e.length;const l=function(e,r){for(let e=0;e<r.length;e++){const t=r.charAt(e);if("\r"===t)return e+1<r.length&&"\n"===r.charAt(e+1)?"\r\n":"\r";if("\n"===t)return"\n"}return e&&e.eol||"\n"}(t,e);let g,d=0,m=0;g=t.insertSpaces?p(" ",t.tabSize||4):"\t";let h=o(n,!1),f=!1;function T(){return d>1?p(l,d)+p(g,s+m):l+p(g,s+m)}function E(){let e=h.scan();for(d=0;15===e||14===e;)14===e&&t.keepLines?d+=1:14===e&&(d=1),e=h.scan();return f=16===e||0!==h.getTokenError(),e}const b=[];function w(t,o,s){f||r&&!(o<c&&s>a)||e.substring(o,s)===t||b.push({offset:o,length:s-o,content:t})}let _=E();if(t.keepLines&&d>0&&w(p(l,d),0,0),17!==_){let e=h.getTokenOffset()+i;w(p(g,s),i,e)}for(;17!==_;){let e=h.getTokenOffset()+h.getTokenLength()+i,r=E(),o="",s=!1;for(;0===d&&(12===r||13===r);)w(" ",e,h.getTokenOffset()+i),e=h.getTokenOffset()+h.getTokenLength()+i,s=12===r,o=s?T():"",r=E();if(2===r)1!==_&&m--,t.keepLines&&d>0||!t.keepLines&&1!==_?o=T():t.keepLines&&(o=" ");else if(4===r)3!==_&&m--,t.keepLines&&d>0||!t.keepLines&&3!==_?o=T():t.keepLines&&(o=" ");else{switch(_){case 3:case 1:m++,o=t.keepLines&&d>0||!t.keepLines?T():" ";break;case 5:o=t.keepLines&&d>0||!t.keepLines?T():" ";break;case 12:o=T();break;case 13:d>0?o=T():s||(o=" ");break;case 6:t.keepLines&&d>0?o=T():s||(o=" ");break;case 10:t.keepLines&&d>0?o=T():6!==r||s||(o="");break;case 7:case 8:case 9:case 11:case 2:case 4:t.keepLines&&d>0?o=T():12!==r&&13!==r||s?5!==r&&17!==r&&(f=!0):o=" ";break;case 16:f=!0}d>0&&(12===r||13===r)&&(o=T())}17===r&&(o=t.keepLines&&d>0?T():t.insertFinalNewline?l:""),w(o,e,h.getTokenOffset()+i),_=r}return b}function p(e,r){let t="";for(let o=0;o<r;o++)t+=e;return t}function u(e,r){return-1!=="\r\n".indexOf(e.charAt(r))}function g(e,r=[],t=c.DEFAULT){let o={type:"array",offset:-1,length:-1,children:[],parent:void 0};function s(e){"property"===o.type&&(o.length=e-o.offset,o=o.parent)}function n(e){return o.children.push(e),e}m(e,{onObjectBegin:e=>{o=n({type:"object",offset:e,length:-1,parent:o,children:[]})},onObjectProperty:(e,r,t)=>{o=n({type:"property",offset:r,length:-1,parent:o,children:[]}),o.children.push({type:"string",value:e,offset:r,length:t,parent:o})},onObjectEnd:(e,r)=>{s(e+r),o.length=e+r-o.offset,o=o.parent,s(e+r)},onArrayBegin:(e,r)=>{o=n({type:"array",offset:e,length:-1,parent:o,children:[]})},onArrayEnd:(e,r)=>{o.length=e+r-o.offset,o=o.parent,s(e+r)},onLiteralValue:(e,r,t)=>{n({type:h(e),offset:r,length:t,parent:o,value:e}),s(r+t)},onSeparator:(e,r,t)=>{"property"===o.type&&(":"===e?o.colonOffset=r:","===e&&s(r))},onError:(e,t,o)=>{r.push({error:e,offset:t,length:o})}},t);const i=o.children[0];return i&&delete i.parent,i}function d(e,r){if(!e)return;let t=e;for(let e of r)if("string"==typeof e){if("object"!==t.type||!Array.isArray(t.children))return;let r=!1;for(const o of t.children)if(Array.isArray(o.children)&&o.children[0].value===e&&2===o.children.length){t=o.children[1],r=!0;break}if(!r)return}else{const r=e;if("array"!==t.type||r<0||!Array.isArray(t.children)||r>=t.children.length)return;t=t.children[r]}return t}function m(e,r,t=c.DEFAULT){const s=o(e,!1),n=[];function i(e){return e?()=>e(s.getTokenOffset(),s.getTokenLength(),s.getTokenStartLine(),s.getTokenStartCharacter()):()=>!0}function a(e){return e?()=>e(s.getTokenOffset(),s.getTokenLength(),s.getTokenStartLine(),s.getTokenStartCharacter(),(()=>n.slice())):()=>!0}function l(e){return e?r=>e(r,s.getTokenOffset(),s.getTokenLength(),s.getTokenStartLine(),s.getTokenStartCharacter()):()=>!0}function p(e){return e?r=>e(r,s.getTokenOffset(),s.getTokenLength(),s.getTokenStartLine(),s.getTokenStartCharacter(),(()=>n.slice())):()=>!0}const u=a(r.onObjectBegin),g=p(r.onObjectProperty),d=i(r.onObjectEnd),m=a(r.onArrayBegin),h=i(r.onArrayEnd),f=p(r.onLiteralValue),T=l(r.onSeparator),E=i(r.onComment),b=l(r.onError),w=t&&t.disallowComments,_=t&&t.allowTrailingComma;function y(){for(;;){const e=s.scan();switch(s.getTokenError()){case 4:k(14);break;case 5:k(15);break;case 3:k(13);break;case 1:w||k(11);break;case 2:k(12);break;case 6:k(16)}switch(e){case 12:case 13:w?k(10):E();break;case 16:k(1);break;case 15:case 14:break;default:return e}}}function k(e,r=[],t=[]){if(b(e),r.length+t.length>0){let e=s.getToken();for(;17!==e;){if(-1!==r.indexOf(e)){y();break}if(-1!==t.indexOf(e))break;e=y()}}}function v(e){const r=s.getTokenValue();return e?f(r):(g(r),n.push(r)),y(),!0}return y(),17===s.getToken()?!!t.allowEmptyContent||(k(4,[],[]),!1):function e(){switch(s.getToken()){case 3:return function(){m(),y();let r=!0,t=!1;for(;4!==s.getToken()&&17!==s.getToken();){if(5===s.getToken()){if(t||k(4,[],[]),T(","),y(),4===s.getToken()&&_)break}else t&&k(6,[],[]);r?(n.push(0),r=!1):n[n.length-1]++,e()||k(4,[],[4,5]),t=!0}return h(),r||n.pop(),4!==s.getToken()?k(8,[4],[]):y(),!0}();case 1:return function(){u(),y();let r=!1;for(;2!==s.getToken()&&17!==s.getToken();){if(5===s.getToken()){if(r||k(4,[],[]),T(","),y(),2===s.getToken()&&_)break}else r&&k(6,[],[]);(10!==s.getToken()?(k(3,[],[2,5]),0):(v(!1),6===s.getToken()?(T(":"),y(),e()||k(4,[],[2,5])):k(5,[],[2,5]),n.pop(),1))||k(4,[],[2,5]),r=!0}return d(),2!==s.getToken()?k(7,[2],[]):y(),!0}();case 10:return v(!0);default:return function(){switch(s.getToken()){case 11:const e=s.getTokenValue();let r=Number(e);isNaN(r)&&(k(2),r=0),f(r);break;case 7:f(null);break;case 8:f(!0);break;case 9:f(!1);break;default:return!1}return y(),!0}()}}()?(17!==s.getToken()&&k(9,[],[]),!0):(k(4,[],[]),!1)}function h(e){switch(typeof e){case"boolean":return"boolean";case"number":return"number";case"string":return"string";case"object":return e?Array.isArray(e)?"array":"object":"null";default:return"null"}}function f(e,r,t){if(!t.formattingOptions)return[r];let o=T(e,r),s=r.offset,n=r.offset+r.content.length;if(0===r.length||0===r.content.length){for(;s>0&&!u(o,s-1);)s--;for(;n<o.length&&!u(o,n);)n++}const i=l(o,{offset:s,length:n-s},{...t.formattingOptions,keepLines:!1});for(let e=i.length-1;e>=0;e--){const r=i[e];o=T(o,r),s=Math.min(s,r.offset),n=Math.max(n,r.offset+r.length),n+=r.content.length-r.length}return[{offset:s,length:e.length-(o.length-n)-s,content:o.substring(s,n)}]}function T(e,r){return e.substring(0,r.offset)+r.content+e.substring(r.offset+r.length)}t.r(r),t.d(r,{ParseErrorCode:()=>C,ScanError:()=>b,SyntaxKind:()=>w,applyEdits:()=>D,createScanner:()=>E,findNodeAtLocation:()=>v,findNodeAtOffset:()=>P,format:()=>R,getLocation:()=>_,getNodePath:()=>O,getNodeValue:()=>G,modify:()=>U,parse:()=>y,parseTree:()=>k,printParseErrorCode:()=>F,stripComments:()=>S,visit:()=>A}),function(e){e[e.lineFeed=10]="lineFeed",e[e.carriageReturn=13]="carriageReturn",e[e.space=32]="space",e[e._0=48]="_0",e[e._1=49]="_1",e[e._2=50]="_2",e[e._3=51]="_3",e[e._4=52]="_4",e[e._5=53]="_5",e[e._6=54]="_6",e[e._7=55]="_7",e[e._8=56]="_8",e[e._9=57]="_9",e[e.a=97]="a",e[e.b=98]="b",e[e.c=99]="c",e[e.d=100]="d",e[e.e=101]="e",e[e.f=102]="f",e[e.g=103]="g",e[e.h=104]="h",e[e.i=105]="i",e[e.j=106]="j",e[e.k=107]="k",e[e.l=108]="l",e[e.m=109]="m",e[e.n=110]="n",e[e.o=111]="o",e[e.p=112]="p",e[e.q=113]="q",e[e.r=114]="r",e[e.s=115]="s",e[e.t=116]="t",e[e.u=117]="u",e[e.v=118]="v",e[e.w=119]="w",e[e.x=120]="x",e[e.y=121]="y",e[e.z=122]="z",e[e.A=65]="A",e[e.B=66]="B",e[e.C=67]="C",e[e.D=68]="D",e[e.E=69]="E",e[e.F=70]="F",e[e.G=71]="G",e[e.H=72]="H",e[e.I=73]="I",e[e.J=74]="J",e[e.K=75]="K",e[e.L=76]="L",e[e.M=77]="M",e[e.N=78]="N",e[e.O=79]="O",e[e.P=80]="P",e[e.Q=81]="Q",e[e.R=82]="R",e[e.S=83]="S",e[e.T=84]="T",e[e.U=85]="U",e[e.V=86]="V",e[e.W=87]="W",e[e.X=88]="X",e[e.Y=89]="Y",e[e.Z=90]="Z",e[e.asterisk=42]="asterisk",e[e.backslash=92]="backslash",e[e.closeBrace=125]="closeBrace",e[e.closeBracket=93]="closeBracket",e[e.colon=58]="colon",e[e.comma=44]="comma",e[e.dot=46]="dot",e[e.doubleQuote=34]="doubleQuote",e[e.minus=45]="minus",e[e.openBrace=123]="openBrace",e[e.openBracket=91]="openBracket",e[e.plus=43]="plus",e[e.slash=47]="slash",e[e.formFeed=12]="formFeed",e[e.tab=9]="tab"}(a||(a={})),function(e){e.DEFAULT={allowTrailingComma:!1}}(c||(c={}));const E=o;var b,w;!function(e){e[e.None=0]="None",e[e.UnexpectedEndOfComment=1]="UnexpectedEndOfComment",e[e.UnexpectedEndOfString=2]="UnexpectedEndOfString",e[e.UnexpectedEndOfNumber=3]="UnexpectedEndOfNumber",e[e.InvalidUnicode=4]="InvalidUnicode",e[e.InvalidEscapeCharacter=5]="InvalidEscapeCharacter",e[e.InvalidCharacter=6]="InvalidCharacter"}(b||(b={})),function(e){e[e.OpenBraceToken=1]="OpenBraceToken",e[e.CloseBraceToken=2]="CloseBraceToken",e[e.OpenBracketToken=3]="OpenBracketToken",e[e.CloseBracketToken=4]="CloseBracketToken",e[e.CommaToken=5]="CommaToken",e[e.ColonToken=6]="ColonToken",e[e.NullKeyword=7]="NullKeyword",e[e.TrueKeyword=8]="TrueKeyword",e[e.FalseKeyword=9]="FalseKeyword",e[e.StringLiteral=10]="StringLiteral",e[e.NumericLiteral=11]="NumericLiteral",e[e.LineCommentTrivia=12]="LineCommentTrivia",e[e.BlockCommentTrivia=13]="BlockCommentTrivia",e[e.LineBreakTrivia=14]="LineBreakTrivia",e[e.Trivia=15]="Trivia",e[e.Unknown=16]="Unknown",e[e.EOF=17]="EOF"}(w||(w={}));const _=function(e,r){const t=[],o=new Object;let s;const n={value:{},offset:0,length:0,type:"object",parent:void 0};let i=!1;function a(e,r,t,o){n.value=e,n.offset=r,n.length=t,n.type=o,n.colonOffset=void 0,s=n}try{m(e,{onObjectBegin:(e,n)=>{if(r<=e)throw o;s=void 0,i=r>e,t.push("")},onObjectProperty:(e,s,n)=>{if(r<s)throw o;if(a(e,s,n,"property"),t[t.length-1]=e,r<=s+n)throw o},onObjectEnd:(e,n)=>{if(r<=e)throw o;s=void 0,t.pop()},onArrayBegin:(e,n)=>{if(r<=e)throw o;s=void 0,t.push(0)},onArrayEnd:(e,n)=>{if(r<=e)throw o;s=void 0,t.pop()},onLiteralValue:(e,t,s)=>{if(r<t)throw o;if(a(e,t,s,h(e)),r<=t+s)throw o},onSeparator:(e,n,a)=>{if(r<=n)throw o;if(":"===e&&s&&"property"===s.type)s.colonOffset=n,i=!1,s=void 0;else if(","===e){const e=t[t.length-1];"number"==typeof e?t[t.length-1]=e+1:(i=!0,t[t.length-1]=""),s=void 0}}})}catch(e){if(e!==o)throw e}return{path:t,previousNode:s,isAtPropertyKey:i,matches:e=>{let r=0;for(let o=0;r<e.length&&o<t.length;o++)if(e[r]===t[o]||"*"===e[r])r++;else if("**"!==e[r])return!1;return r===e.length}}},y=function(e,r=[],t=c.DEFAULT){let o=null,s=[];const n=[];function i(e){Array.isArray(s)?s.push(e):null!==o&&(s[o]=e)}return m(e,{onObjectBegin:()=>{const e={};i(e),n.push(s),s=e,o=null},onObjectProperty:e=>{o=e},onObjectEnd:()=>{s=n.pop()},onArrayBegin:()=>{const e=[];i(e),n.push(s),s=e,o=null},onArrayEnd:()=>{s=n.pop()},onLiteralValue:i,onError:(e,t,o)=>{r.push({error:e,offset:t,length:o})}},t),s[0]},k=g,v=d,P=function e(r,t,o=!1){if(function(e,r,t=!1){return r>=e.offset&&r<e.offset+e.length||t&&r===e.offset+e.length}(r,t,o)){const s=r.children;if(Array.isArray(s))for(let r=0;r<s.length&&s[r].offset<=t;r++){const n=e(s[r],t,o);if(n)return n}return r}},O=function e(r){if(!r.parent||!r.parent.children)return[];const t=e(r.parent);if("property"===r.parent.type){const e=r.parent.children[0].value;t.push(e)}else if("array"===r.parent.type){const e=r.parent.children.indexOf(r);-1!==e&&t.push(e)}return t},G=function e(r){switch(r.type){case"array":return r.children.map(e);case"object":const t=Object.create(null);for(let o of r.children){const r=o.children[1];r&&(t[o.children[0].value]=e(r))}return t;case"null":case"string":case"number":case"boolean":return r.value;default:return}},A=m,S=function(e,r){let t,s,n=o(e),i=[],a=0;do{switch(s=n.getPosition(),t=n.scan(),t){case 12:case 13:case 17:a!==s&&i.push(e.substring(a,s)),void 0!==r&&i.push(n.getTokenValue().replace(/[^\r\n]/g,r)),a=n.getPosition()}}while(17!==t);return i.join("")};var C;function F(e){switch(e){case 1:return"InvalidSymbol";case 2:return"InvalidNumberFormat";case 3:return"PropertyNameExpected";case 4:return"ValueExpected";case 5:return"ColonExpected";case 6:return"CommaExpected";case 7:return"CloseBraceExpected";case 8:return"CloseBracketExpected";case 9:return"EndOfFileExpected";case 10:return"InvalidCommentToken";case 11:return"UnexpectedEndOfComment";case 12:return"UnexpectedEndOfString";case 13:return"UnexpectedEndOfNumber";case 14:return"InvalidUnicode";case 15:return"InvalidEscapeCharacter";case 16:return"InvalidCharacter"}return"<unknown ParseErrorCode>"}function R(e,r,t){return l(e,r,t)}function U(e,r,t,o){return function(e,r,t,o){const s=r.slice(),n=g(e,[]);let i,a;for(;s.length>0&&(a=s.pop(),i=d(n,s),void 0===i&&void 0!==t);)t="string"==typeof a?{[a]:t}:[t];if(i){if("object"===i.type&&"string"==typeof a&&Array.isArray(i.children)){const r=d(i,[a]);if(void 0!==r){if(void 0===t){if(!r.parent)throw new Error("Malformed AST");const t=i.children.indexOf(r.parent);let s,n=r.parent.offset+r.parent.length;if(t>0){let e=i.children[t-1];s=e.offset+e.length}else s=i.offset+1,i.children.length>1&&(n=i.children[1].offset);return f(e,{offset:s,length:n-s,content:""},o)}return f(e,{offset:r.offset,length:r.length,content:JSON.stringify(t)},o)}{if(void 0===t)return[];const r=`${JSON.stringify(a)}: ${JSON.stringify(t)}`,s=o.getInsertionIndex?o.getInsertionIndex(i.children.map((e=>e.children[0].value))):i.children.length;let n;if(s>0){let e=i.children[s-1];n={offset:e.offset+e.length,length:0,content:","+r}}else n=0===i.children.length?{offset:i.offset+1,length:0,content:r}:{offset:i.offset+1,length:0,content:r+","};return f(e,n,o)}}if("array"===i.type&&"number"==typeof a&&Array.isArray(i.children)){const r=a;if(-1===r){const r=`${JSON.stringify(t)}`;let s;if(0===i.children.length)s={offset:i.offset+1,length:0,content:r};else{const e=i.children[i.children.length-1];s={offset:e.offset+e.length,length:0,content:","+r}}return f(e,s,o)}if(void 0===t&&i.children.length>=0){const r=a,t=i.children[r];let s;if(1===i.children.length)s={offset:i.offset+1,length:i.length-2,content:""};else if(i.children.length-1===r){let e=i.children[r-1],t=e.offset+e.length;s={offset:t,length:i.offset+i.length-2-t,content:""}}else s={offset:t.offset,length:i.children[r+1].offset-t.offset,content:""};return f(e,s,o)}if(void 0!==t){let r;const s=`${JSON.stringify(t)}`;if(!o.isArrayInsertion&&i.children.length>a){const e=i.children[a];r={offset:e.offset,length:e.length,content:s}}else if(0===i.children.length||0===a)r={offset:i.offset+1,length:0,content:0===i.children.length?s:s+","};else{const e=a>i.children.length?i.children.length:a,t=i.children[e-1];r={offset:t.offset+t.length,length:0,content:","+s}}return f(e,r,o)}throw new Error(`Can not ${void 0===t?"remove":o.isArrayInsertion?"insert":"modify"} Array index ${r} as length is not sufficient`)}throw new Error(`Can not add ${"number"!=typeof a?"index":"property"} to parent of type ${i.type}`)}if(void 0===t)throw new Error("Can not delete in empty document");return f(e,{offset:n?n.offset:0,length:n?n.length:0,content:JSON.stringify(t)},o)}(e,r,t,o)}function D(e,r){let t=r.slice(0).sort(((e,r)=>{const t=e.offset-r.offset;return 0===t?e.length-r.length:t})),o=e.length;for(let r=t.length-1;r>=0;r--){let s=t[r];if(!(s.offset+s.length<=o))throw new Error("Overlapping edit");e=T(e,s),o=s.offset}return e}!function(e){e[e.InvalidSymbol=1]="InvalidSymbol",e[e.InvalidNumberFormat=2]="InvalidNumberFormat",e[e.PropertyNameExpected=3]="PropertyNameExpected",e[e.ValueExpected=4]="ValueExpected",e[e.ColonExpected=5]="ColonExpected",e[e.CommaExpected=6]="CommaExpected",e[e.CloseBraceExpected=7]="CloseBraceExpected",e[e.CloseBracketExpected=8]="CloseBracketExpected",e[e.EndOfFileExpected=9]="EndOfFileExpected",e[e.InvalidCommentToken=10]="InvalidCommentToken",e[e.UnexpectedEndOfComment=11]="UnexpectedEndOfComment",e[e.UnexpectedEndOfString=12]="UnexpectedEndOfString",e[e.UnexpectedEndOfNumber=13]="UnexpectedEndOfNumber",e[e.InvalidUnicode=14]="InvalidUnicode",e[e.InvalidEscapeCharacter=15]="InvalidEscapeCharacter",e[e.InvalidCharacter=16]="InvalidCharacter"}(C||(C={}))},803:(e,r,t)=>{e.exports=t(469)},469:(e,r,t)=>{"use strict";t(278);var o,s=t(756),n=t(611),i=t(692),a=t(434),c=(t(613),t(23));function l(e){var r=this;r.options=e||{},r.proxyOptions=r.options.proxy||{},r.maxSockets=r.options.maxSockets||n.Agent.defaultMaxSockets,r.requests=[],r.sockets=[],r.on("free",(function(e,t,o,s){for(var n=u(t,o,s),i=0,a=r.requests.length;i<a;++i){var c=r.requests[i];if(c.host===n.host&&c.port===n.port)return r.requests.splice(i,1),void c.request.onSocket(e)}e.destroy(),r.removeSocket(e)}))}function p(e,r){var t=this;l.prototype.createSocket.call(t,e,(function(o){var n=e.request.getHeader("host"),i=g({},t.options,{socket:o,servername:n?n.replace(/:.*$/,""):e.host}),a=s.connect(0,i);t.sockets[t.sockets.indexOf(o)]=a,r(a)}))}function u(e,r,t){return"string"==typeof e?{host:e,port:r,localAddress:t}:e}function g(e){for(var r=1,t=arguments.length;r<t;++r){var o=arguments[r];if("object"==typeof o)for(var s=Object.keys(o),n=0,i=s.length;n<i;++n){var a=s[n];void 0!==o[a]&&(e[a]=o[a])}}return e}r.httpOverHttp=function(e){var r=new l(e);return r.request=n.request,r},r.httpsOverHttp=function(e){var r=new l(e);return r.request=n.request,r.createSocket=p,r.defaultPort=443,r},r.httpOverHttps=function(e){var r=new l(e);return r.request=i.request,r},r.httpsOverHttps=function(e){var r=new l(e);return r.request=i.request,r.createSocket=p,r.defaultPort=443,r},c.inherits(l,a.EventEmitter),l.prototype.addRequest=function(e,r,t,o){var s=this,n=g({request:e},s.options,u(r,t,o));s.sockets.length>=this.maxSockets?s.requests.push(n):s.createSocket(n,(function(r){function t(){s.emit("free",r,n)}function o(e){s.removeSocket(r),r.removeListener("free",t),r.removeListener("close",o),r.removeListener("agentRemove",o)}r.on("free",t),r.on("close",o),r.on("agentRemove",o),e.onSocket(r)}))},l.prototype.createSocket=function(e,r){var t=this,s={};t.sockets.push(s);var n=g({},t.proxyOptions,{method:"CONNECT",path:e.host+":"+e.port,agent:!1,headers:{host:e.host+":"+e.port}});e.localAddress&&(n.localAddress=e.localAddress),n.proxyAuth&&(n.headers=n.headers||{},n.headers["Proxy-Authorization"]="Basic "+new Buffer(n.proxyAuth).toString("base64")),o("making CONNECT request");var i=t.request(n);function a(n,a,c){var l;return i.removeAllListeners(),a.removeAllListeners(),200!==n.statusCode?(o("tunneling socket could not be established, statusCode=%d",n.statusCode),a.destroy(),(l=new Error("tunneling socket could not be established, statusCode="+n.statusCode)).code="ECONNRESET",e.request.emit("error",l),void t.removeSocket(s)):c.length>0?(o("got illegal response body from proxy"),a.destroy(),(l=new Error("got illegal response body from proxy")).code="ECONNRESET",e.request.emit("error",l),void t.removeSocket(s)):(o("tunneling connection has established"),t.sockets[t.sockets.indexOf(s)]=a,r(a))}i.useChunkedEncodingByDefault=!1,i.once("response",(function(e){e.upgrade=!0})),i.once("upgrade",(function(e,r,t){process.nextTick((function(){a(e,r,t)}))})),i.once("connect",a),i.once("error",(function(r){i.removeAllListeners(),o("tunneling socket could not be established, cause=%s\n",r.message,r.stack);var n=new Error("tunneling socket could not be established, cause="+r.message);n.code="ECONNRESET",e.request.emit("error",n),t.removeSocket(s)})),i.end()},l.prototype.removeSocket=function(e){var r=this.sockets.indexOf(e);if(-1!==r){this.sockets.splice(r,1);var t=this.requests.shift();t&&this.createSocket(t,(function(e){t.request.onSocket(e)}))}},o=process.env.NODE_DEBUG&&/\btunnel\b/.test(process.env.NODE_DEBUG)?function(){var e=Array.prototype.slice.call(arguments);"string"==typeof e[0]?e[0]="TUNNEL: "+e[0]:e.unshift("TUNNEL:"),console.error.apply(console,e)}:function(){},r.debug=o},670:function(e,r,t){"use strict";var o,s=this&&this.__createBinding||(Object.create?function(e,r,t,o){void 0===o&&(o=t);var s=Object.getOwnPropertyDescriptor(r,t);s&&!("get"in s?!r.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return r[t]}}),Object.defineProperty(e,o,s)}:function(e,r,t,o){void 0===o&&(o=t),e[o]=r[t]}),n=this&&this.__setModuleDefault||(Object.create?function(e,r){Object.defineProperty(e,"default",{enumerable:!0,value:r})}:function(e,r){e.default=r}),i=this&&this.__importStar||(o=function(e){return o=Object.getOwnPropertyNames||function(e){var r=[];for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(r[r.length]=t);return r},o(e)},function(e){if(e&&e.__esModule)return e;var r={};if(null!=e)for(var t=o(e),i=0;i<t.length;i++)"default"!==t[i]&&s(r,e,t[i]);return n(r,e),r});Object.defineProperty(r,"__esModule",{value:!0}),r.activate=function(e){e.subscriptions.push(c.languages.registerCompletionItemProvider({language:"jsonc",pattern:"**/settings.json"},{provideCompletionItems:(e,r,t)=>new l.SettingsDocument(e).provideCompletionItems(r,t)})),e.subscriptions.push(c.languages.registerCompletionItemProvider({pattern:"**/extensions.json"},{provideCompletionItems(e,r,t){const o=(0,a.getLocation)(e.getText(),e.offsetAt(r));if("recommendations"===o.path[0]){const t=d(e,o,r),s=(0,a.parse)(e.getText());return(0,p.provideInstalledExtensionProposals)(s&&s.recommendations||[],"",t,!1)}return[]}}),c.languages.registerCompletionItemProvider({pattern:"**/*.code-workspace"},{provideCompletionItems(e,r,t){const o=(0,a.getLocation)(e.getText(),e.offsetAt(r));if("extensions"===o.path[0]&&"recommendations"===o.path[1]){const t=d(e,o,r),s=(0,a.parse)(e.getText()).extensions;return(0,p.provideInstalledExtensionProposals)(s&&s.recommendations||[],"",t,!1)}return[]}})),e.subscriptions.push(u("**/launch.json")),e.subscriptions.push(u("**/tasks.json")),e.subscriptions.push(u("**/*.code-workspace")),e.subscriptions.push(function(){const e=new Map([[{language:"jsonc",pattern:"**/keybindings.json"},[["*","when"]]],[{language:"json",pattern:"**/package.json"},[["contributes","menus","*","*","when"],["contributes","views","*","*","when"],["contributes","viewsWelcome","*","when"],["contributes","keybindings","*","when"],["contributes","keybindings","when"]]]]);return c.languages.registerCompletionItemProvider([...e.keys()],{async provideCompletionItems(r,t,o){const s=(0,a.getLocation)(r.getText(),r.offsetAt(t));if(s.isAtPropertyKey)return;let n=!1;for(const[t,o]of e)if(c.languages.match(t,r)&&o.some(s.matches.bind(s))){n=!0;break}if(!n||!g(r,s,t))return;const i=r.getWordRangeAtPosition(t,/[a-zA-Z.]+/)||new c.Range(t,t),l=i.with(void 0,t),p=await c.commands.executeCommand("getContextKeyInfo");if(o.isCancellationRequested||!p)return;const u=new c.CompletionList;for(const e of p){const r=new c.CompletionItem(e.key,c.CompletionItemKind.Constant);r.detail=e.type,r.range={replacing:i,inserting:l},r.documentation=e.description,u.items.push(r)}return u}})}())};const a=t(887),c=i(t(398)),l=t(489),p=t(684);function u(e){return c.languages.registerCompletionItemProvider({language:"jsonc",pattern:e},{provideCompletionItems(e,r,t){const o=(0,a.getLocation)(e.getText(),e.offsetAt(r));if(g(e,o,r)){if(e.fileName.endsWith(".code-workspace")&&!function(e,r){return["launch","tasks"].includes(e.path[0])}(o))return[];let t=e.getWordRangeAtPosition(r,/\$\{[^"\}]*\}?/);return(!t||t.start.isEqual(r)||t.end.isEqual(r)&&e.getText(t).endsWith("}"))&&(t=new c.Range(r,r)),[{label:"workspaceFolder",detail:c.l10n.t("The path of the folder opened in VS Code")},{label:"workspaceFolderBasename",detail:c.l10n.t("The name of the folder opened in VS Code without any slashes (/)")},{label:"fileWorkspaceFolderBasename",detail:c.l10n.t("The current opened file workspace folder name without any slashes (/)")},{label:"relativeFile",detail:c.l10n.t("The current opened file relative to ${workspaceFolder}")},{label:"relativeFileDirname",detail:c.l10n.t("The current opened file's dirname relative to ${workspaceFolder}")},{label:"file",detail:c.l10n.t("The current opened file")},{label:"cwd",detail:c.l10n.t("The task runner's current working directory on startup")},{label:"lineNumber",detail:c.l10n.t("The current selected line number in the active file")},{label:"selectedText",detail:c.l10n.t("The current selected text in the active file")},{label:"fileDirname",detail:c.l10n.t("The current opened file's dirname")},{label:"fileExtname",detail:c.l10n.t("The current opened file's extension")},{label:"fileBasename",detail:c.l10n.t("The current opened file's basename")},{label:"fileBasenameNoExtension",detail:c.l10n.t("The current opened file's basename with no file extension")},{label:"defaultBuildTask",detail:c.l10n.t("The name of the default build task. If there is not a single default build task then a quick pick is shown to choose the build task.")},{label:"pathSeparator",detail:c.l10n.t("The character used by the operating system to separate components in file paths. Is also aliased to '/'.")},{label:"extensionInstallFolder",detail:c.l10n.t("The path where an extension is installed."),param:"publisher.extension"}].map((e=>({label:`\${${e.label}}`,range:t,insertText:e.param?new c.SnippetString(`\${${e.label}:`).appendPlaceholder(e.param).appendText("}"):`\${${e.label}}`,detail:e.detail})))}return[]}})}function g(e,r,t){if(r.isAtPropertyKey)return!1;const o=r.previousNode;if(o&&"string"===o.type){const r=e.offsetAt(t);return r>o.offset&&r<o.offset+o.length}return!1}function d(e,r,t){const o=r.previousNode;if(o){const r=e.positionAt(o.offset),s=e.positionAt(o.offset+o.length);if(r.isBeforeOrEqual(t)&&s.isAfterOrEqual(t))return new c.Range(r,s)}return new c.Range(t,t)}t(648),c.languages.registerDocumentSymbolProvider({pattern:"**/launch.json",language:"jsonc"},{provideDocumentSymbols(e,r){const t=[];let o="",s="",n=0,i=0;return(0,a.visit)(e.getText(),{onObjectProperty:(e,r,t)=>{s=e},onLiteralValue:(e,r,t)=>{"name"===s&&(o=e)},onObjectBegin:(e,r)=>{i++,2===i&&(n=e)},onObjectEnd:(r,s)=>{o&&2===i&&t.push(new c.SymbolInformation(o,c.SymbolKind.Object,new c.Range(e.positionAt(n),e.positionAt(r)))),i--}}),t}},{label:"Launch Targets"})},684:function(e,r,t){"use strict";var o,s=this&&this.__createBinding||(Object.create?function(e,r,t,o){void 0===o&&(o=t);var s=Object.getOwnPropertyDescriptor(r,t);s&&!("get"in s?!r.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return r[t]}}),Object.defineProperty(e,o,s)}:function(e,r,t,o){void 0===o&&(o=t),e[o]=r[t]}),n=this&&this.__setModuleDefault||(Object.create?function(e,r){Object.defineProperty(e,"default",{enumerable:!0,value:r})}:function(e,r){e.default=r}),i=this&&this.__importStar||(o=function(e){return o=Object.getOwnPropertyNames||function(e){var r=[];for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(r[r.length]=t);return r},o(e)},function(e){if(e&&e.__esModule)return e;var r={};if(null!=e)for(var t=o(e),i=0;i<t.length;i++)"default"!==t[i]&&s(r,e,t[i]);return n(r,e),r});Object.defineProperty(r,"__esModule",{value:!0}),r.provideInstalledExtensionProposals=async function(e,r,t,o){if(Array.isArray(e)){const s=(o?a.extensions.all:a.extensions.all.filter((e=>!(e.id.startsWith("vscode.")||"Microsoft.vscode-markdown"===e.id)))).filter((r=>-1===e.indexOf(r.id)));if(s.length)return s.map((e=>{const o=new a.CompletionItem(e.id),s=`"${e.id}"${r}`;return o.kind=a.CompletionItemKind.Value,o.insertText=s,o.range=t,o.filterText=s,o}));{const e=new a.CompletionItem(a.l10n.t("Example"));return e.insertText='"vscode.csharp"',e.kind=a.CompletionItemKind.Value,e.range=t,[e]}}return[]};const a=i(t(398))},648:function(e,r,t){"use strict";var o,s=this&&this.__createBinding||(Object.create?function(e,r,t,o){void 0===o&&(o=t);var s=Object.getOwnPropertyDescriptor(r,t);s&&!("get"in s?!r.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return r[t]}}),Object.defineProperty(e,o,s)}:function(e,r,t,o){void 0===o&&(o=t),e[o]=r[t]}),n=this&&this.__setModuleDefault||(Object.create?function(e,r){Object.defineProperty(e,"default",{enumerable:!0,value:r})}:function(e,r){e.default=r}),i=this&&this.__importStar||(o=function(e){return o=Object.getOwnPropertyNames||function(e){var r=[];for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(r[r.length]=t);return r},o(e)},function(e){if(e&&e.__esModule)return e;var r={};if(null!=e)for(var t=o(e),i=0;i<t.length;i++)"default"!==t[i]&&s(r,e,t[i]);return n(r,e),r});Object.defineProperty(r,"__esModule",{value:!0});const a=i(t(398)),c=t(928),l=t(161);a.window.registerProfileContentHandler("github",new class{constructor(){this.name=a.l10n.t("GitHub"),this.description=a.l10n.t("gist")}getOctokit(){return this._octokit||(this._octokit=(async()=>{const e=(await a.authentication.getSession("github",["gist","user:email"],{createIfNone:!0})).accessToken,{Octokit:r}=await Promise.resolve().then((()=>i(t(7))));return new r({request:{agent:l.agent},userAgent:"GitHub VSCode",auth:`token ${e}`})})()),this._octokit}async saveProfile(e,r){const t=await this.getOctokit(),o=await t.gists.create({public:!1,files:{[e]:{content:r}}});if(o.data.id&&o.data.html_url){const e=a.Uri.parse(o.data.html_url);return{id:o.data.id,link:e}}return null}getPublicOctokit(){return this._public_octokit||(this._public_octokit=(async()=>{const{Octokit:e}=await Promise.resolve().then((()=>i(t(7))));return new e({request:{agent:l.agent},userAgent:"GitHub VSCode"})})()),this._public_octokit}async readProfile(e){const r="string"==typeof e?e:(0,c.basename)(e.path),t=await this.getPublicOctokit();try{const e=await t.gists.get({gist_id:r});if(e.data.files)return e.data.files[Object.keys(e.data.files)[0]]?.content??null}catch(e){}return null}})},161:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.agent=void 0;const o=t(692),s=t(16),n=t(803),i=t(398);r.agent=function(e=process.env.HTTPS_PROXY){if(!e)return o.globalAgent;try{const{hostname:r,port:t,username:o,password:i}=new s.URL(e),a=o&&i&&`${o}:${i}`;return(0,n.httpsOverHttp)({proxy:{host:r,port:t,proxyAuth:a}})}catch(e){return i.window.showErrorMessage(`HTTPS_PROXY environment variable ignored: ${e.message}`),o.globalAgent}}()},489:function(e,r,t){"use strict";var o,s=this&&this.__createBinding||(Object.create?function(e,r,t,o){void 0===o&&(o=t);var s=Object.getOwnPropertyDescriptor(r,t);s&&!("get"in s?!r.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return r[t]}}),Object.defineProperty(e,o,s)}:function(e,r,t,o){void 0===o&&(o=t),e[o]=r[t]}),n=this&&this.__setModuleDefault||(Object.create?function(e,r){Object.defineProperty(e,"default",{enumerable:!0,value:r})}:function(e,r){e.default=r}),i=this&&this.__importStar||(o=function(e){return o=Object.getOwnPropertyNames||function(e){var r=[];for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(r[r.length]=t);return r},o(e)},function(e){if(e&&e.__esModule)return e;var r={};if(null!=e)for(var t=o(e),i=0;i<t.length;i++)"default"!==t[i]&&s(r,e,t[i]);return n(r,e),r});Object.defineProperty(r,"__esModule",{value:!0}),r.SettingsDocument=void 0;const a=i(t(398)),c=t(887),l=t(684),p=/\[([^\[\]]*)\]/g;r.SettingsDocument=class{constructor(e){this.document=e}async provideCompletionItems(e,r){const t=(0,c.getLocation)(this.document.getText(),this.document.offsetAt(e));if("window.title"===t.path[0])return this.provideWindowTitleCompletionItems(t,e);if("files.associations"===t.path[0])return this.provideFilesAssociationsCompletionItems(t,e);if("files.exclude"===t.path[0]||"search.exclude"===t.path[0]||"explorer.autoRevealExclude"===t.path[0])return this.provideExcludeCompletionItems(t,e);if("files.defaultLanguage"===t.path[0])return this.provideLanguageCompletionItems(t,e);if("workbench.editor.label.patterns"===t.path[0])return this.provideEditorLabelCompletionItems(t,e);if("settingsSync.ignoredExtensions"===t.path[0]){let r=[];try{r=(0,c.parse)(this.document.getText())["settingsSync.ignoredExtensions"]}catch(e){}const o=this.getReplaceRange(t,e);return(0,l.provideInstalledExtensionProposals)(r,"",o,!0)}if("remote.extensionKind"===t.path[0]&&2===t.path.length&&t.isAtPropertyKey){let r=[];try{r=Object.keys((0,c.parse)(this.document.getText())["remote.extensionKind"])}catch(e){}const o=this.getReplaceRange(t,e);return(0,l.provideInstalledExtensionProposals)(r,t.previousNode?"":': [\n\t"ui"\n]',o,!0)}return"remote.portsAttributes"===t.path[0]&&2===t.path.length&&t.isAtPropertyKey?this.providePortsAttributesCompletionItem(this.getReplaceRange(t,e)):this.provideLanguageOverridesCompletionItems(t,e)}getReplaceRange(e,r){const t=e.previousNode;if(t){const e=this.document.positionAt(t.offset),o=this.document.positionAt(t.offset+t.length);if(e.isBeforeOrEqual(r)&&o.isAfterOrEqual(r))return new a.Range(e,o)}return new a.Range(r,r)}isCompletingPropertyValue(e,r){if(e.isAtPropertyKey)return!1;const t=e.previousNode;if(t){const e=this.document.offsetAt(r);return e>=t.offset&&e<=t.offset+t.length}return!0}async provideWindowTitleCompletionItems(e,r){const t=[];if(!this.isCompletingPropertyValue(e,r))return t;let o=this.document.getWordRangeAtPosition(r,/\$\{[^"\}]*\}?/);(!o||o.start.isEqual(r)||o.end.isEqual(r)&&this.document.getText(o).endsWith("}"))&&(o=new a.Range(r,r));const s=r=>{const t="${"+r+"}";return e.previousNode?t:JSON.stringify(t)};return t.push(this.newSimpleCompletionItem(s("activeEditorShort"),o,a.l10n.t("the file name (e.g. myFile.txt)"))),t.push(this.newSimpleCompletionItem(s("activeEditorMedium"),o,a.l10n.t("the path of the file relative to the workspace folder (e.g. myFolder/myFileFolder/myFile.txt)"))),t.push(this.newSimpleCompletionItem(s("activeEditorLong"),o,a.l10n.t("the full path of the file (e.g. /Users/<USER>/myFolder/myFileFolder/myFile.txt)"))),t.push(this.newSimpleCompletionItem(s("activeFolderShort"),o,a.l10n.t("the name of the folder the file is contained in (e.g. myFileFolder)"))),t.push(this.newSimpleCompletionItem(s("activeFolderMedium"),o,a.l10n.t("the path of the folder the file is contained in, relative to the workspace folder (e.g. myFolder/myFileFolder)"))),t.push(this.newSimpleCompletionItem(s("activeFolderLong"),o,a.l10n.t("the full path of the folder the file is contained in (e.g. /Users/<USER>/myFolder/myFileFolder)"))),t.push(this.newSimpleCompletionItem(s("rootName"),o,a.l10n.t("name of the workspace with optional remote name and workspace indicator if applicable (e.g. myFolder, myRemoteFolder [SSH] or myWorkspace (Workspace))"))),t.push(this.newSimpleCompletionItem(s("rootNameShort"),o,a.l10n.t("shortened name of the workspace without suffixes (e.g. myFolder or myWorkspace)"))),t.push(this.newSimpleCompletionItem(s("rootPath"),o,a.l10n.t("file path of the workspace (e.g. /Users/<USER>/myWorkspace)"))),t.push(this.newSimpleCompletionItem(s("folderName"),o,a.l10n.t("name of the workspace folder the file is contained in (e.g. myFolder)"))),t.push(this.newSimpleCompletionItem(s("folderPath"),o,a.l10n.t("file path of the workspace folder the file is contained in (e.g. /Users/<USER>/myFolder)"))),t.push(this.newSimpleCompletionItem(s("appName"),o,a.l10n.t("e.g. VS Code"))),t.push(this.newSimpleCompletionItem(s("remoteName"),o,a.l10n.t("e.g. SSH"))),t.push(this.newSimpleCompletionItem(s("dirty"),o,a.l10n.t("an indicator for when the active editor has unsaved changes"))),t.push(this.newSimpleCompletionItem(s("separator"),o,a.l10n.t("a conditional separator (' - ') that only shows when surrounded by variables with values"))),t.push(this.newSimpleCompletionItem(s("activeRepositoryName"),o,a.l10n.t("the name of the active repository (e.g. vscode)"))),t.push(this.newSimpleCompletionItem(s("activeRepositoryBranchName"),o,a.l10n.t("the name of the active branch in the active repository (e.g. main)"))),t.push(this.newSimpleCompletionItem(s("activeEditorState"),o,a.l10n.t("the state of the active editor (e.g. modified)."))),t}async provideEditorLabelCompletionItems(e,r){const t=[];if(!this.isCompletingPropertyValue(e,r))return t;let o=this.document.getWordRangeAtPosition(r,/\$\{[^"\}]*\}?/);(!o||o.start.isEqual(r)||o.end.isEqual(r)&&this.document.getText(o).endsWith("}"))&&(o=new a.Range(r,r));const s=r=>{const t="${"+r+"}";return e.previousNode?t:JSON.stringify(t)};return t.push(this.newSimpleCompletionItem(s("dirname"),o,a.l10n.t("The parent folder name of the editor (e.g. myFileFolder)"))),t.push(this.newSimpleCompletionItem(s("dirname(1)"),o,a.l10n.t("The nth parent folder name of the editor"))),t.push(this.newSimpleCompletionItem(s("filename"),o,a.l10n.t("The file name of the editor without its directory or extension (e.g. myFile)"))),t.push(this.newSimpleCompletionItem(s("extname"),o,a.l10n.t("The file extension of the editor (e.g. txt)"))),t}async provideFilesAssociationsCompletionItems(e,r){const t=[];if(2===e.path.length)if(""===e.path[1]){const o=this.getReplaceRange(e,r);t.push(this.newSnippetCompletionItem({label:a.l10n.t("Files with Extension"),documentation:a.l10n.t("Map all files matching the glob pattern in their filename to the language with the given identifier."),snippet:e.isAtPropertyKey?'"*.${1:extension}": "${2:language}"':'{ "*.${1:extension}": "${2:language}" }',range:o})),t.push(this.newSnippetCompletionItem({label:a.l10n.t("Files with Path"),documentation:a.l10n.t("Map all files matching the absolute path glob pattern in their path to the language with the given identifier."),snippet:e.isAtPropertyKey?'"/${1:path to file}/*.${2:extension}": "${3:language}"':'{ "/${1:path to file}/*.${2:extension}": "${3:language}" }',range:o}))}else if(this.isCompletingPropertyValue(e,r))return this.provideLanguageCompletionItemsForLanguageOverrides(this.getReplaceRange(e,r));return t}async provideExcludeCompletionItems(e,r){const t=[];if(1===e.path.length||2===e.path.length&&""===e.path[1]){const o=this.getReplaceRange(e,r);t.push(this.newSnippetCompletionItem({label:a.l10n.t("Files by Extension"),documentation:a.l10n.t("Match all files of a specific file extension."),snippet:2===e.path.length?'"**/*.${1:extension}": true':'{ "**/*.${1:extension}": true }',range:o})),t.push(this.newSnippetCompletionItem({label:a.l10n.t("Files with Multiple Extensions"),documentation:a.l10n.t("Match all files with any of the file extensions."),snippet:2===e.path.length?'"**/*.{ext1,ext2,ext3}": true':'{ "**/*.{ext1,ext2,ext3}": true }',range:o})),t.push(this.newSnippetCompletionItem({label:a.l10n.t("Files with Siblings by Name"),documentation:a.l10n.t("Match files that have siblings with the same name but a different extension."),snippet:2===e.path.length?'"**/*.${1:source-extension}": { "when": "$(basename).${2:target-extension}" }':'{ "**/*.${1:source-extension}": { "when": "$(basename).${2:target-extension}" } }',range:o})),t.push(this.newSnippetCompletionItem({label:a.l10n.t("Folder by Name (Top Level)"),documentation:a.l10n.t("Match a top level folder with a specific name."),snippet:2===e.path.length?'"${1:name}": true':'{ "${1:name}": true }',range:o})),t.push(this.newSnippetCompletionItem({label:a.l10n.t("Folders with Multiple Names (Top Level)"),documentation:a.l10n.t("Match multiple top level folders."),snippet:2===e.path.length?'"{folder1,folder2,folder3}": true':'{ "{folder1,folder2,folder3}": true }',range:o})),t.push(this.newSnippetCompletionItem({label:a.l10n.t("Folder by Name (Any Location)"),documentation:a.l10n.t("Match a folder with a specific name in any location."),snippet:2===e.path.length?'"**/${1:name}": true':'{ "**/${1:name}": true }',range:o}))}else if(2===e.path.length&&this.isCompletingPropertyValue(e,r)){const o=this.getReplaceRange(e,r);t.push(this.newSnippetCompletionItem({label:a.l10n.t("Files with Siblings by Name"),documentation:a.l10n.t("Match files that have siblings with the same name but a different extension."),snippet:'{ "when": "$(basename).${1:extension}" }',range:o}))}return t}async provideLanguageCompletionItems(e,r){if(1===e.path.length&&this.isCompletingPropertyValue(e,r)){const t=this.getReplaceRange(e,r),o=await a.languages.getLanguages();return[this.newSimpleCompletionItem(JSON.stringify("${activeEditorLanguage}"),t,a.l10n.t("Use the language of the currently active text editor if any")),...o.map((e=>this.newSimpleCompletionItem(JSON.stringify(e),t)))]}return[]}async provideLanguageCompletionItemsForLanguageOverrides(e){const r=await a.languages.getLanguages(),t=[];for(const o of r){const r=new a.CompletionItem(JSON.stringify(o));r.kind=a.CompletionItemKind.Property,r.range=e,t.push(r)}return t}async provideLanguageOverridesCompletionItems(e,r){if(1===e.path.length&&e.isAtPropertyKey&&e.previousNode&&"string"==typeof e.previousNode.value&&e.previousNode.value.startsWith("[")){const t=this.document.positionAt(e.previousNode.offset+1),o=t.translate(void 0,e.previousNode.value.length),s=[],n=[];let i,c=p.exec(e.previousNode.value);for(;c?.length;)i=new a.Range(this.document.positionAt(e.previousNode.offset+1+c.index),this.document.positionAt(e.previousNode.offset+1+c.index+c[0].length)),n.push(i),i.contains(r)||s.push(c[1].trim()),c=p.exec(e.previousNode.value);const l=i?i.end:t;l.isBefore(o)&&n.push(new a.Range(l,o));const u=n.find((e=>e.contains(r)));if(u&&!u.isEqual(n[0])){const e=await a.languages.getLanguages(),r=[];for(const t of e)if(!s.includes(t)){const e=new a.CompletionItem(`[${t}]`);e.kind=a.CompletionItemKind.Property,e.range=u,r.push(e)}return r}}return[]}providePortsAttributesCompletionItem(e){return[this.newSnippetCompletionItem({label:'"3000"',documentation:"Single Port Attribute",range:e,snippet:'\n  "${1:3000}": {\n    "label": "${2:Application}",\n    "onAutoForward": "${3:openPreview}"\n  }\n'}),this.newSnippetCompletionItem({label:'"5000-6000"',documentation:"Ranged Port Attribute",range:e,snippet:'\n  "${1:40000-55000}": {\n    "onAutoForward": "${2:ignore}"\n  }\n'}),this.newSnippetCompletionItem({label:'".+\\\\/server.js"',documentation:"Command Match Port Attribute",range:e,snippet:'\n  "${1:.+\\\\/server.js}": {\n    "label": "${2:Application}",\n    "onAutoForward": "${3:openPreview}"\n  }\n'})]}newSimpleCompletionItem(e,r,t,o){const s=new a.CompletionItem(e);return s.kind=a.CompletionItemKind.Value,s.detail=t,s.insertText=o||e,s.range=r,s}newSnippetCompletionItem(e){const r=new a.CompletionItem(e.label);return r.kind=a.CompletionItemKind.Value,r.documentation=e.documentation,r.insertText=new a.SnippetString(e.snippet),r.range=e.range,r}}},398:e=>{"use strict";e.exports=require("vscode")},613:e=>{"use strict";e.exports=require("assert")},434:e=>{"use strict";e.exports=require("events")},611:e=>{"use strict";e.exports=require("http")},692:e=>{"use strict";e.exports=require("https")},278:e=>{"use strict";e.exports=require("net")},928:e=>{"use strict";e.exports=require("path")},756:e=>{"use strict";e.exports=require("tls")},16:e=>{"use strict";e.exports=require("url")},23:e=>{"use strict";e.exports=require("util")},773:e=>{"use strict";const r=function(){};r.prototype=Object.create(null);const t=/; *([!#$%&'*+.^\w`|~-]+)=("(?:[\v\u0020\u0021\u0023-\u005b\u005d-\u007e\u0080-\u00ff]|\\[\v\u0020-\u00ff])*"|[!#$%&'*+.^\w`|~-]+) */gu,o=/\\([\v\u0020-\u00ff])/gu,s=/^[!#$%&'*+.^\w|~-]+\/[!#$%&'*+.^\w|~-]+$/u,n={type:"",parameters:new r};Object.freeze(n.parameters),Object.freeze(n),e.exports.xL=function(e){if("string"!=typeof e)return n;let i=e.indexOf(";");const a=-1!==i?e.slice(0,i).trim():e.trim();if(!1===s.test(a))return n;const c={type:a.toLowerCase(),parameters:new r};if(-1===i)return c;let l,p,u;for(t.lastIndex=i;p=t.exec(e);){if(p.index!==i)return n;i+=p[0].length,l=p[1].toLowerCase(),u=p[2],'"'===u[0]&&(u=u.slice(1,u.length-1),o.test(u)&&(u=u.replace(o,"$1"))),c.parameters[l]=u}return i!==e.length?n:c}},7:(e,r,t)=>{"use strict";function o(){return"object"==typeof navigator&&"userAgent"in navigator?navigator.userAgent:"object"==typeof process&&void 0!==process.version?`Node.js/${process.version.substr(1)} (${process.platform}; ${process.arch})`:"<environment undetectable>"}function s(e,r,t,o){if("function"!=typeof t)throw new Error("method for before hook must be a function");return o||(o={}),Array.isArray(r)?r.reverse().reduce(((r,t)=>s.bind(null,e,t,r,o)),t)():Promise.resolve().then((()=>e.registry[r]?e.registry[r].reduce(((e,r)=>r.hook.bind(null,e,o)),t)():t(o)))}function n(e,r,t,o){const s=o;e.registry[t]||(e.registry[t]=[]),"before"===r&&(o=(e,r)=>Promise.resolve().then(s.bind(null,r)).then(e.bind(null,r))),"after"===r&&(o=(e,r)=>{let t;return Promise.resolve().then(e.bind(null,r)).then((e=>(t=e,s(t,r)))).then((()=>t))}),"error"===r&&(o=(e,r)=>Promise.resolve().then(e.bind(null,r)).catch((e=>s(e,r)))),e.registry[t].push({hook:o,orig:s})}function i(e,r,t){if(!e.registry[r])return;const o=e.registry[r].map((e=>e.orig)).indexOf(t);-1!==o&&e.registry[r].splice(o,1)}t.r(r),t.d(r,{Octokit:()=>te});const a=Function.bind,c=a.bind(a);function l(e,r,t){const o=c(i,null).apply(null,t?[r,t]:[r]);e.api={remove:o},e.remove=o,["before","error","after","wrap"].forEach((o=>{const s=t?[r,o,t]:[r,o];e[o]=e.api[o]=c(n,null).apply(null,s)}))}const p=function(){const e={registry:{}},r=s.bind(null,e);return l(r,e),r};var u=`octokit-endpoint.js/0.0.0-development ${o()}`;function g(e,r){const t=Object.assign({},e);return Object.keys(r).forEach((o=>{!function(e){if("object"!=typeof e||null===e)return!1;if("[object Object]"!==Object.prototype.toString.call(e))return!1;const r=Object.getPrototypeOf(e);if(null===r)return!0;const t=Object.prototype.hasOwnProperty.call(r,"constructor")&&r.constructor;return"function"==typeof t&&t instanceof t&&Function.prototype.call(t)===Function.prototype.call(e)}(r[o])?Object.assign(t,{[o]:r[o]}):o in e?t[o]=g(e[o],r[o]):Object.assign(t,{[o]:r[o]})})),t}function d(e){for(const r in e)void 0===e[r]&&delete e[r];return e}function m(e,r,t){if("string"==typeof r){let[e,o]=r.split(" ");t=Object.assign(o?{method:e,url:o}:{url:e},t)}else t=Object.assign({},r);var o;t.headers=(o=t.headers)?Object.keys(o).reduce(((e,r)=>(e[r.toLowerCase()]=o[r],e)),{}):{},d(t),d(t.headers);const s=g(e||{},t);return"/graphql"===t.url&&(e&&e.mediaType.previews?.length&&(s.mediaType.previews=e.mediaType.previews.filter((e=>!s.mediaType.previews.includes(e))).concat(s.mediaType.previews)),s.mediaType.previews=(s.mediaType.previews||[]).map((e=>e.replace(/-preview/,"")))),s}var h=/\{[^{}}]+\}/g;function f(e){return e.replace(/(?:^\W+)|(?:(?<!\W)\W+$)/g,"").split(/,/)}function T(e,r){const t={__proto__:null};for(const o of Object.keys(e))-1===r.indexOf(o)&&(t[o]=e[o]);return t}function E(e){return e.split(/(%[0-9A-Fa-f]{2})/g).map((function(e){return/%[0-9A-Fa-f]/.test(e)||(e=encodeURI(e).replace(/%5B/g,"[").replace(/%5D/g,"]")),e})).join("")}function b(e){return encodeURIComponent(e).replace(/[!'()*]/g,(function(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()}))}function w(e,r,t){return r="+"===e||"#"===e?E(r):b(r),t?b(t)+"="+r:r}function _(e){return null!=e}function y(e){return";"===e||"&"===e||"?"===e}function k(e,r){var t=["+","#",".","/",";","?","&"];return e=e.replace(/\{([^\{\}]+)\}|([^\{\}]+)/g,(function(e,o,s){if(o){let e="";const s=[];if(-1!==t.indexOf(o.charAt(0))&&(e=o.charAt(0),o=o.substr(1)),o.split(/,/g).forEach((function(t){var o=/([^:\*]*)(?::(\d+)|(\*))?/.exec(t);s.push(function(e,r,t,o){var s=e[t],n=[];if(_(s)&&""!==s)if("string"==typeof s||"number"==typeof s||"boolean"==typeof s)s=s.toString(),o&&"*"!==o&&(s=s.substring(0,parseInt(o,10))),n.push(w(r,s,y(r)?t:""));else if("*"===o)Array.isArray(s)?s.filter(_).forEach((function(e){n.push(w(r,e,y(r)?t:""))})):Object.keys(s).forEach((function(e){_(s[e])&&n.push(w(r,s[e],e))}));else{const e=[];Array.isArray(s)?s.filter(_).forEach((function(t){e.push(w(r,t))})):Object.keys(s).forEach((function(t){_(s[t])&&(e.push(b(t)),e.push(w(r,s[t].toString())))})),y(r)?n.push(b(t)+"="+e.join(",")):0!==e.length&&n.push(e.join(","))}else";"===r?_(s)&&n.push(b(t)):""!==s||"&"!==r&&"?"!==r?""===s&&n.push(""):n.push(b(t)+"=");return n}(r,e,o[1],o[2]||o[3]))})),e&&"+"!==e){var n=",";return"?"===e?n="&":"#"!==e&&(n=e),(0!==s.length?e:"")+s.join(n)}return s.join(",")}return E(s)})),"/"===e?e:e.replace(/\/$/,"")}function v(e){let r,t=e.method.toUpperCase(),o=(e.url||"/").replace(/:([a-z]\w+)/g,"{$1}"),s=Object.assign({},e.headers),n=T(e,["method","baseUrl","url","headers","request","mediaType"]);const i=function(e){const r=e.match(h);return r?r.map(f).reduce(((e,r)=>e.concat(r)),[]):[]}(o);var a;o=(a=o,{expand:k.bind(null,a)}).expand(n),/^http/.test(o)||(o=e.baseUrl+o);const c=T(n,Object.keys(e).filter((e=>i.includes(e))).concat("baseUrl"));if(!/application\/octet-stream/i.test(s.accept)&&(e.mediaType.format&&(s.accept=s.accept.split(/,/).map((r=>r.replace(/application\/vnd(\.\w+)(\.v3)?(\.\w+)?(\+json)?$/,`application/vnd$1$2.${e.mediaType.format}`))).join(",")),o.endsWith("/graphql")&&e.mediaType.previews?.length)){const r=s.accept.match(/(?<![\w-])[\w-]+(?=-preview)/g)||[];s.accept=r.concat(e.mediaType.previews).map((r=>`application/vnd.github.${r}-preview${e.mediaType.format?`.${e.mediaType.format}`:"+json"}`)).join(",")}return["GET","HEAD"].includes(t)?o=function(e,r){const t=/\?/.test(e)?"&":"?",o=Object.keys(r);return 0===o.length?e:e+t+o.map((e=>"q"===e?"q="+r.q.split("+").map(encodeURIComponent).join("+"):`${e}=${encodeURIComponent(r[e])}`)).join("&")}(o,c):"data"in c?r=c.data:Object.keys(c).length&&(r=c),s["content-type"]||void 0===r||(s["content-type"]="application/json; charset=utf-8"),["PATCH","PUT"].includes(t)&&void 0===r&&(r=""),Object.assign({method:t,url:o,headers:s},void 0!==r?{body:r}:null,e.request?{request:e.request}:null)}function P(e,r,t){return v(m(e,r,t))}var O=function e(r,t){const o=m(r,t),s=P.bind(null,o);return Object.assign(s,{DEFAULTS:o,defaults:e.bind(null,o),merge:m.bind(null,o),parse:v})}(null,{method:"GET",baseUrl:"https://api.github.com",headers:{accept:"application/vnd.github.v3+json","user-agent":u},mediaType:{format:""}}),G=t(773);class A extends Error{name;status;request;response;constructor(e,r,t){super(e),this.name="HttpError",this.status=Number.parseInt(r),Number.isNaN(this.status)&&(this.status=0),"response"in t&&(this.response=t.response);const o=Object.assign({},t.request);t.request.headers.authorization&&(o.headers=Object.assign({},t.request.headers,{authorization:t.request.headers.authorization.replace(/(?<! ) .*$/," [REDACTED]")})),o.url=o.url.replace(/\bclient_secret=\w+/g,"client_secret=[REDACTED]").replace(/\baccess_token=\w+/g,"access_token=[REDACTED]"),this.request=o}}async function S(e){const r=e.request?.fetch||globalThis.fetch;if(!r)throw new Error("fetch is not set. Please pass a fetch implementation as new Octokit({ request: { fetch }}). Learn more at https://github.com/octokit/octokit.js/#fetch-missing");const t=e.request?.log||console,o=!1!==e.request?.parseSuccessResponseBody,s=function(e){if("object"!=typeof e||null===e)return!1;if("[object Object]"!==Object.prototype.toString.call(e))return!1;const r=Object.getPrototypeOf(e);if(null===r)return!0;const t=Object.prototype.hasOwnProperty.call(r,"constructor")&&r.constructor;return"function"==typeof t&&t instanceof t&&Function.prototype.call(t)===Function.prototype.call(e)}(e.body)||Array.isArray(e.body)?JSON.stringify(e.body):e.body,n=Object.fromEntries(Object.entries(e.headers).map((([e,r])=>[e,String(r)])));let i;try{i=await r(e.url,{method:e.method,body:s,redirect:e.request?.redirect,headers:n,signal:e.request?.signal,...e.body&&{duplex:"half"}})}catch(r){let t="Unknown Error";if(r instanceof Error){if("AbortError"===r.name)throw r.status=500,r;t=r.message,"TypeError"===r.name&&"cause"in r&&(r.cause instanceof Error?t=r.cause.message:"string"==typeof r.cause&&(t=r.cause))}const o=new A(t,500,{request:e});throw o.cause=r,o}const a=i.status,c=i.url,l={};for(const[e,r]of i.headers)l[e]=r;const p={url:c,status:a,headers:l,data:""};if("deprecation"in l){const r=l.link&&l.link.match(/<([^<>]+)>; rel="deprecation"/),o=r&&r.pop();t.warn(`[@octokit/request] "${e.method} ${e.url}" is deprecated. It is scheduled to be removed on ${l.sunset}${o?`. See ${o}`:""}`)}if(204===a||205===a)return p;if("HEAD"===e.method){if(a<400)return p;throw new A(i.statusText,a,{response:p,request:e})}if(304===a)throw p.data=await C(i),new A("Not modified",a,{response:p,request:e});if(a>=400)throw p.data=await C(i),new A(function(e){if("string"==typeof e)return e;if(e instanceof ArrayBuffer)return"Unknown error";if("message"in e){const r="documentation_url"in e?` - ${e.documentation_url}`:"";return Array.isArray(e.errors)?`${e.message}: ${e.errors.map((e=>JSON.stringify(e))).join(", ")}${r}`:`${e.message}${r}`}return`Unknown error: ${JSON.stringify(e)}`}(p.data),a,{response:p,request:e});return p.data=o?await C(i):i.body,p}async function C(e){const r=e.headers.get("content-type");if(!r)return e.text().catch((()=>""));const t=(0,G.xL)(r);if(!function(e){return"application/json"===e.type||"application/scim+json"===e.type}(t))return t.type.startsWith("text/")||"utf-8"===t.parameters.charset?.toLowerCase()?e.text().catch((()=>"")):e.arrayBuffer().catch((()=>new ArrayBuffer(0)));{let r="";try{return r=await e.text(),JSON.parse(r)}catch(e){return r}}}var F=function e(r,t){const o=r.defaults(t);return Object.assign((function(r,t){const s=o.merge(r,t);if(!s.request||!s.request.hook)return S(o.parse(s));const n=(e,r)=>S(o.parse(o.merge(e,r)));return Object.assign(n,{endpoint:o,defaults:e.bind(null,o)}),s.request.hook(n,s)}),{endpoint:o,defaults:e.bind(null,o)})}(O,{headers:{"user-agent":`octokit-request.js/0.0.0-development ${o()}`}}),R=class extends Error{constructor(e,r,t){super("Request failed due to following response errors:\n"+t.errors.map((e=>` - ${e.message}`)).join("\n")),this.request=e,this.headers=r,this.response=t,this.errors=t.errors,this.data=t.data,Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}name="GraphqlResponseError";errors;data},U=["method","baseUrl","url","headers","request","query","mediaType","operationName"],D=["query","method","url"],L=/\/api\/v3\/?$/;function I(e,r){const t=e.defaults(r);return Object.assign(((e,r)=>function(e,r,t){if(t){if("string"==typeof r&&"query"in t)return Promise.reject(new Error('[@octokit/graphql] "query" cannot be used as variable name'));for(const e in t)if(D.includes(e))return Promise.reject(new Error(`[@octokit/graphql] "${e}" cannot be used as variable name`))}const o="string"==typeof r?Object.assign({query:r},t):r,s=Object.keys(o).reduce(((e,r)=>U.includes(r)?(e[r]=o[r],e):(e.variables||(e.variables={}),e.variables[r]=o[r],e)),{}),n=o.baseUrl||e.endpoint.DEFAULTS.baseUrl;return L.test(n)&&(s.url=n.replace(L,"/api/graphql")),e(s).then((e=>{if(e.data.errors){const r={};for(const t of Object.keys(e.headers))r[t]=e.headers[t];throw new R(s,r,e.data)}return e.data.data}))}(t,e,r)),{defaults:I.bind(null,t),endpoint:t.endpoint})}I(F,{headers:{"user-agent":`octokit-graphql.js/0.0.0-development ${o()}`},method:"POST",url:"/graphql"});var x="(?:[a-zA-Z0-9_-]+)",j=new RegExp(`^${x}\\.${x}\\.${x}$`),q=j.test.bind(j);async function $(e){const r=q(e),t=e.startsWith("v1.")||e.startsWith("ghs_"),o=e.startsWith("ghu_");return{type:"token",token:e,tokenType:r?"app":t?"installation":o?"user-to-server":"oauth"}}async function B(e,r,t,o){const s=r.endpoint.merge(t,o);return s.headers.authorization=function(e){return 3===e.split(/\./).length?`bearer ${e}`:`token ${e}`}(e),r(s)}var N=function(e){if(!e)throw new Error("[@octokit/auth-token] No token passed to createTokenAuth");if("string"!=typeof e)throw new Error("[@octokit/auth-token] Token passed to createTokenAuth is not a string");return e=e.replace(/^(token|bearer) +/i,""),Object.assign($.bind(null,e),{hook:B.bind(null,e)})};const W="6.1.4",H=()=>{},M=console.warn.bind(console),V=console.error.bind(console),K=`octokit-core.js/${W} ${o()}`;function z(e){e.hook.wrap("request",((r,t)=>{e.log.debug("request",t);const o=Date.now(),s=e.request.endpoint.parse(t),n=s.url.replace(t.baseUrl,"");return r(t).then((r=>{const t=r.headers["x-github-request-id"];return e.log.info(`${s.method} ${n} - ${r.status} with id ${t} in ${Date.now()-o}ms`),r})).catch((r=>{const t=r.response?.headers["x-github-request-id"]||"UNKNOWN";throw e.log.error(`${s.method} ${n} - ${r.status} with id ${t} in ${Date.now()-o}ms`),r}))}))}function J(e,r,t){const o="function"==typeof r?r.endpoint(t):e.request.endpoint(r,t),s="function"==typeof r?r:e.request,n=o.method,i=o.headers;let a=o.url;return{[Symbol.asyncIterator]:()=>({async next(){if(!a)return{done:!0};try{const e=function(e){if(!e.data)return{...e,data:[]};if(!("total_count"in e.data)||"url"in e.data)return e;const r=e.data.incomplete_results,t=e.data.repository_selection,o=e.data.total_count;delete e.data.incomplete_results,delete e.data.repository_selection,delete e.data.total_count;const s=Object.keys(e.data)[0],n=e.data[s];return e.data=n,void 0!==r&&(e.data.incomplete_results=r),void 0!==t&&(e.data.repository_selection=t),e.data.total_count=o,e}(await s({method:n,url:a,headers:i}));return a=((e.headers.link||"").match(/<([^<>]+)>;\s*rel="next"/)||[])[1],{value:e}}catch(e){if(409!==e.status)throw e;return a="",{value:{status:200,headers:{},data:[]}}}}})}}function Z(e,r,t,o){return"function"==typeof t&&(o=t,t=void 0),Y(e,[],J(e,r,t)[Symbol.asyncIterator](),o)}function Y(e,r,t,o){return t.next().then((s=>{if(s.done)return r;let n=!1;return r=r.concat(o?o(s.value,(function(){n=!0})):s.value.data),n?r:Y(e,r,t,o)}))}function Q(e){return{paginate:Object.assign(Z.bind(null,e),{iterator:J.bind(null,e)})}}z.VERSION="5.3.1",Object.assign(Z,{iterator:J}),Q.VERSION="0.0.0-development";const X=new Map;for(const[e,r]of Object.entries({actions:{addCustomLabelsToSelfHostedRunnerForOrg:["POST /orgs/{org}/actions/runners/{runner_id}/labels"],addCustomLabelsToSelfHostedRunnerForRepo:["POST /repos/{owner}/{repo}/actions/runners/{runner_id}/labels"],addRepoAccessToSelfHostedRunnerGroupInOrg:["PUT /orgs/{org}/actions/runner-groups/{runner_group_id}/repositories/{repository_id}"],addSelectedRepoToOrgSecret:["PUT /orgs/{org}/actions/secrets/{secret_name}/repositories/{repository_id}"],addSelectedRepoToOrgVariable:["PUT /orgs/{org}/actions/variables/{name}/repositories/{repository_id}"],approveWorkflowRun:["POST /repos/{owner}/{repo}/actions/runs/{run_id}/approve"],cancelWorkflowRun:["POST /repos/{owner}/{repo}/actions/runs/{run_id}/cancel"],createEnvironmentVariable:["POST /repos/{owner}/{repo}/environments/{environment_name}/variables"],createOrUpdateEnvironmentSecret:["PUT /repos/{owner}/{repo}/environments/{environment_name}/secrets/{secret_name}"],createOrUpdateOrgSecret:["PUT /orgs/{org}/actions/secrets/{secret_name}"],createOrUpdateRepoSecret:["PUT /repos/{owner}/{repo}/actions/secrets/{secret_name}"],createOrgVariable:["POST /orgs/{org}/actions/variables"],createRegistrationTokenForOrg:["POST /orgs/{org}/actions/runners/registration-token"],createRegistrationTokenForRepo:["POST /repos/{owner}/{repo}/actions/runners/registration-token"],createRemoveTokenForOrg:["POST /orgs/{org}/actions/runners/remove-token"],createRemoveTokenForRepo:["POST /repos/{owner}/{repo}/actions/runners/remove-token"],createRepoVariable:["POST /repos/{owner}/{repo}/actions/variables"],createWorkflowDispatch:["POST /repos/{owner}/{repo}/actions/workflows/{workflow_id}/dispatches"],deleteActionsCacheById:["DELETE /repos/{owner}/{repo}/actions/caches/{cache_id}"],deleteActionsCacheByKey:["DELETE /repos/{owner}/{repo}/actions/caches{?key,ref}"],deleteArtifact:["DELETE /repos/{owner}/{repo}/actions/artifacts/{artifact_id}"],deleteEnvironmentSecret:["DELETE /repos/{owner}/{repo}/environments/{environment_name}/secrets/{secret_name}"],deleteEnvironmentVariable:["DELETE /repos/{owner}/{repo}/environments/{environment_name}/variables/{name}"],deleteOrgSecret:["DELETE /orgs/{org}/actions/secrets/{secret_name}"],deleteOrgVariable:["DELETE /orgs/{org}/actions/variables/{name}"],deleteRepoSecret:["DELETE /repos/{owner}/{repo}/actions/secrets/{secret_name}"],deleteRepoVariable:["DELETE /repos/{owner}/{repo}/actions/variables/{name}"],deleteSelfHostedRunnerFromOrg:["DELETE /orgs/{org}/actions/runners/{runner_id}"],deleteSelfHostedRunnerFromRepo:["DELETE /repos/{owner}/{repo}/actions/runners/{runner_id}"],deleteWorkflowRun:["DELETE /repos/{owner}/{repo}/actions/runs/{run_id}"],deleteWorkflowRunLogs:["DELETE /repos/{owner}/{repo}/actions/runs/{run_id}/logs"],disableSelectedRepositoryGithubActionsOrganization:["DELETE /orgs/{org}/actions/permissions/repositories/{repository_id}"],disableWorkflow:["PUT /repos/{owner}/{repo}/actions/workflows/{workflow_id}/disable"],downloadArtifact:["GET /repos/{owner}/{repo}/actions/artifacts/{artifact_id}/{archive_format}"],downloadJobLogsForWorkflowRun:["GET /repos/{owner}/{repo}/actions/jobs/{job_id}/logs"],downloadWorkflowRunAttemptLogs:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}/logs"],downloadWorkflowRunLogs:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/logs"],enableSelectedRepositoryGithubActionsOrganization:["PUT /orgs/{org}/actions/permissions/repositories/{repository_id}"],enableWorkflow:["PUT /repos/{owner}/{repo}/actions/workflows/{workflow_id}/enable"],forceCancelWorkflowRun:["POST /repos/{owner}/{repo}/actions/runs/{run_id}/force-cancel"],generateRunnerJitconfigForOrg:["POST /orgs/{org}/actions/runners/generate-jitconfig"],generateRunnerJitconfigForRepo:["POST /repos/{owner}/{repo}/actions/runners/generate-jitconfig"],getActionsCacheList:["GET /repos/{owner}/{repo}/actions/caches"],getActionsCacheUsage:["GET /repos/{owner}/{repo}/actions/cache/usage"],getActionsCacheUsageByRepoForOrg:["GET /orgs/{org}/actions/cache/usage-by-repository"],getActionsCacheUsageForOrg:["GET /orgs/{org}/actions/cache/usage"],getAllowedActionsOrganization:["GET /orgs/{org}/actions/permissions/selected-actions"],getAllowedActionsRepository:["GET /repos/{owner}/{repo}/actions/permissions/selected-actions"],getArtifact:["GET /repos/{owner}/{repo}/actions/artifacts/{artifact_id}"],getCustomOidcSubClaimForRepo:["GET /repos/{owner}/{repo}/actions/oidc/customization/sub"],getEnvironmentPublicKey:["GET /repos/{owner}/{repo}/environments/{environment_name}/secrets/public-key"],getEnvironmentSecret:["GET /repos/{owner}/{repo}/environments/{environment_name}/secrets/{secret_name}"],getEnvironmentVariable:["GET /repos/{owner}/{repo}/environments/{environment_name}/variables/{name}"],getGithubActionsDefaultWorkflowPermissionsOrganization:["GET /orgs/{org}/actions/permissions/workflow"],getGithubActionsDefaultWorkflowPermissionsRepository:["GET /repos/{owner}/{repo}/actions/permissions/workflow"],getGithubActionsPermissionsOrganization:["GET /orgs/{org}/actions/permissions"],getGithubActionsPermissionsRepository:["GET /repos/{owner}/{repo}/actions/permissions"],getJobForWorkflowRun:["GET /repos/{owner}/{repo}/actions/jobs/{job_id}"],getOrgPublicKey:["GET /orgs/{org}/actions/secrets/public-key"],getOrgSecret:["GET /orgs/{org}/actions/secrets/{secret_name}"],getOrgVariable:["GET /orgs/{org}/actions/variables/{name}"],getPendingDeploymentsForRun:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/pending_deployments"],getRepoPermissions:["GET /repos/{owner}/{repo}/actions/permissions",{},{renamed:["actions","getGithubActionsPermissionsRepository"]}],getRepoPublicKey:["GET /repos/{owner}/{repo}/actions/secrets/public-key"],getRepoSecret:["GET /repos/{owner}/{repo}/actions/secrets/{secret_name}"],getRepoVariable:["GET /repos/{owner}/{repo}/actions/variables/{name}"],getReviewsForRun:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/approvals"],getSelfHostedRunnerForOrg:["GET /orgs/{org}/actions/runners/{runner_id}"],getSelfHostedRunnerForRepo:["GET /repos/{owner}/{repo}/actions/runners/{runner_id}"],getWorkflow:["GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}"],getWorkflowAccessToRepository:["GET /repos/{owner}/{repo}/actions/permissions/access"],getWorkflowRun:["GET /repos/{owner}/{repo}/actions/runs/{run_id}"],getWorkflowRunAttempt:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}"],getWorkflowRunUsage:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/timing"],getWorkflowUsage:["GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}/timing"],listArtifactsForRepo:["GET /repos/{owner}/{repo}/actions/artifacts"],listEnvironmentSecrets:["GET /repos/{owner}/{repo}/environments/{environment_name}/secrets"],listEnvironmentVariables:["GET /repos/{owner}/{repo}/environments/{environment_name}/variables"],listJobsForWorkflowRun:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/jobs"],listJobsForWorkflowRunAttempt:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}/jobs"],listLabelsForSelfHostedRunnerForOrg:["GET /orgs/{org}/actions/runners/{runner_id}/labels"],listLabelsForSelfHostedRunnerForRepo:["GET /repos/{owner}/{repo}/actions/runners/{runner_id}/labels"],listOrgSecrets:["GET /orgs/{org}/actions/secrets"],listOrgVariables:["GET /orgs/{org}/actions/variables"],listRepoOrganizationSecrets:["GET /repos/{owner}/{repo}/actions/organization-secrets"],listRepoOrganizationVariables:["GET /repos/{owner}/{repo}/actions/organization-variables"],listRepoSecrets:["GET /repos/{owner}/{repo}/actions/secrets"],listRepoVariables:["GET /repos/{owner}/{repo}/actions/variables"],listRepoWorkflows:["GET /repos/{owner}/{repo}/actions/workflows"],listRunnerApplicationsForOrg:["GET /orgs/{org}/actions/runners/downloads"],listRunnerApplicationsForRepo:["GET /repos/{owner}/{repo}/actions/runners/downloads"],listSelectedReposForOrgSecret:["GET /orgs/{org}/actions/secrets/{secret_name}/repositories"],listSelectedReposForOrgVariable:["GET /orgs/{org}/actions/variables/{name}/repositories"],listSelectedRepositoriesEnabledGithubActionsOrganization:["GET /orgs/{org}/actions/permissions/repositories"],listSelfHostedRunnersForOrg:["GET /orgs/{org}/actions/runners"],listSelfHostedRunnersForRepo:["GET /repos/{owner}/{repo}/actions/runners"],listWorkflowRunArtifacts:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/artifacts"],listWorkflowRuns:["GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}/runs"],listWorkflowRunsForRepo:["GET /repos/{owner}/{repo}/actions/runs"],reRunJobForWorkflowRun:["POST /repos/{owner}/{repo}/actions/jobs/{job_id}/rerun"],reRunWorkflow:["POST /repos/{owner}/{repo}/actions/runs/{run_id}/rerun"],reRunWorkflowFailedJobs:["POST /repos/{owner}/{repo}/actions/runs/{run_id}/rerun-failed-jobs"],removeAllCustomLabelsFromSelfHostedRunnerForOrg:["DELETE /orgs/{org}/actions/runners/{runner_id}/labels"],removeAllCustomLabelsFromSelfHostedRunnerForRepo:["DELETE /repos/{owner}/{repo}/actions/runners/{runner_id}/labels"],removeCustomLabelFromSelfHostedRunnerForOrg:["DELETE /orgs/{org}/actions/runners/{runner_id}/labels/{name}"],removeCustomLabelFromSelfHostedRunnerForRepo:["DELETE /repos/{owner}/{repo}/actions/runners/{runner_id}/labels/{name}"],removeSelectedRepoFromOrgSecret:["DELETE /orgs/{org}/actions/secrets/{secret_name}/repositories/{repository_id}"],removeSelectedRepoFromOrgVariable:["DELETE /orgs/{org}/actions/variables/{name}/repositories/{repository_id}"],reviewCustomGatesForRun:["POST /repos/{owner}/{repo}/actions/runs/{run_id}/deployment_protection_rule"],reviewPendingDeploymentsForRun:["POST /repos/{owner}/{repo}/actions/runs/{run_id}/pending_deployments"],setAllowedActionsOrganization:["PUT /orgs/{org}/actions/permissions/selected-actions"],setAllowedActionsRepository:["PUT /repos/{owner}/{repo}/actions/permissions/selected-actions"],setCustomLabelsForSelfHostedRunnerForOrg:["PUT /orgs/{org}/actions/runners/{runner_id}/labels"],setCustomLabelsForSelfHostedRunnerForRepo:["PUT /repos/{owner}/{repo}/actions/runners/{runner_id}/labels"],setCustomOidcSubClaimForRepo:["PUT /repos/{owner}/{repo}/actions/oidc/customization/sub"],setGithubActionsDefaultWorkflowPermissionsOrganization:["PUT /orgs/{org}/actions/permissions/workflow"],setGithubActionsDefaultWorkflowPermissionsRepository:["PUT /repos/{owner}/{repo}/actions/permissions/workflow"],setGithubActionsPermissionsOrganization:["PUT /orgs/{org}/actions/permissions"],setGithubActionsPermissionsRepository:["PUT /repos/{owner}/{repo}/actions/permissions"],setSelectedReposForOrgSecret:["PUT /orgs/{org}/actions/secrets/{secret_name}/repositories"],setSelectedReposForOrgVariable:["PUT /orgs/{org}/actions/variables/{name}/repositories"],setSelectedRepositoriesEnabledGithubActionsOrganization:["PUT /orgs/{org}/actions/permissions/repositories"],setWorkflowAccessToRepository:["PUT /repos/{owner}/{repo}/actions/permissions/access"],updateEnvironmentVariable:["PATCH /repos/{owner}/{repo}/environments/{environment_name}/variables/{name}"],updateOrgVariable:["PATCH /orgs/{org}/actions/variables/{name}"],updateRepoVariable:["PATCH /repos/{owner}/{repo}/actions/variables/{name}"]},activity:{checkRepoIsStarredByAuthenticatedUser:["GET /user/starred/{owner}/{repo}"],deleteRepoSubscription:["DELETE /repos/{owner}/{repo}/subscription"],deleteThreadSubscription:["DELETE /notifications/threads/{thread_id}/subscription"],getFeeds:["GET /feeds"],getRepoSubscription:["GET /repos/{owner}/{repo}/subscription"],getThread:["GET /notifications/threads/{thread_id}"],getThreadSubscriptionForAuthenticatedUser:["GET /notifications/threads/{thread_id}/subscription"],listEventsForAuthenticatedUser:["GET /users/{username}/events"],listNotificationsForAuthenticatedUser:["GET /notifications"],listOrgEventsForAuthenticatedUser:["GET /users/{username}/events/orgs/{org}"],listPublicEvents:["GET /events"],listPublicEventsForRepoNetwork:["GET /networks/{owner}/{repo}/events"],listPublicEventsForUser:["GET /users/{username}/events/public"],listPublicOrgEvents:["GET /orgs/{org}/events"],listReceivedEventsForUser:["GET /users/{username}/received_events"],listReceivedPublicEventsForUser:["GET /users/{username}/received_events/public"],listRepoEvents:["GET /repos/{owner}/{repo}/events"],listRepoNotificationsForAuthenticatedUser:["GET /repos/{owner}/{repo}/notifications"],listReposStarredByAuthenticatedUser:["GET /user/starred"],listReposStarredByUser:["GET /users/{username}/starred"],listReposWatchedByUser:["GET /users/{username}/subscriptions"],listStargazersForRepo:["GET /repos/{owner}/{repo}/stargazers"],listWatchedReposForAuthenticatedUser:["GET /user/subscriptions"],listWatchersForRepo:["GET /repos/{owner}/{repo}/subscribers"],markNotificationsAsRead:["PUT /notifications"],markRepoNotificationsAsRead:["PUT /repos/{owner}/{repo}/notifications"],markThreadAsDone:["DELETE /notifications/threads/{thread_id}"],markThreadAsRead:["PATCH /notifications/threads/{thread_id}"],setRepoSubscription:["PUT /repos/{owner}/{repo}/subscription"],setThreadSubscription:["PUT /notifications/threads/{thread_id}/subscription"],starRepoForAuthenticatedUser:["PUT /user/starred/{owner}/{repo}"],unstarRepoForAuthenticatedUser:["DELETE /user/starred/{owner}/{repo}"]},apps:{addRepoToInstallation:["PUT /user/installations/{installation_id}/repositories/{repository_id}",{},{renamed:["apps","addRepoToInstallationForAuthenticatedUser"]}],addRepoToInstallationForAuthenticatedUser:["PUT /user/installations/{installation_id}/repositories/{repository_id}"],checkToken:["POST /applications/{client_id}/token"],createFromManifest:["POST /app-manifests/{code}/conversions"],createInstallationAccessToken:["POST /app/installations/{installation_id}/access_tokens"],deleteAuthorization:["DELETE /applications/{client_id}/grant"],deleteInstallation:["DELETE /app/installations/{installation_id}"],deleteToken:["DELETE /applications/{client_id}/token"],getAuthenticated:["GET /app"],getBySlug:["GET /apps/{app_slug}"],getInstallation:["GET /app/installations/{installation_id}"],getOrgInstallation:["GET /orgs/{org}/installation"],getRepoInstallation:["GET /repos/{owner}/{repo}/installation"],getSubscriptionPlanForAccount:["GET /marketplace_listing/accounts/{account_id}"],getSubscriptionPlanForAccountStubbed:["GET /marketplace_listing/stubbed/accounts/{account_id}"],getUserInstallation:["GET /users/{username}/installation"],getWebhookConfigForApp:["GET /app/hook/config"],getWebhookDelivery:["GET /app/hook/deliveries/{delivery_id}"],listAccountsForPlan:["GET /marketplace_listing/plans/{plan_id}/accounts"],listAccountsForPlanStubbed:["GET /marketplace_listing/stubbed/plans/{plan_id}/accounts"],listInstallationReposForAuthenticatedUser:["GET /user/installations/{installation_id}/repositories"],listInstallationRequestsForAuthenticatedApp:["GET /app/installation-requests"],listInstallations:["GET /app/installations"],listInstallationsForAuthenticatedUser:["GET /user/installations"],listPlans:["GET /marketplace_listing/plans"],listPlansStubbed:["GET /marketplace_listing/stubbed/plans"],listReposAccessibleToInstallation:["GET /installation/repositories"],listSubscriptionsForAuthenticatedUser:["GET /user/marketplace_purchases"],listSubscriptionsForAuthenticatedUserStubbed:["GET /user/marketplace_purchases/stubbed"],listWebhookDeliveries:["GET /app/hook/deliveries"],redeliverWebhookDelivery:["POST /app/hook/deliveries/{delivery_id}/attempts"],removeRepoFromInstallation:["DELETE /user/installations/{installation_id}/repositories/{repository_id}",{},{renamed:["apps","removeRepoFromInstallationForAuthenticatedUser"]}],removeRepoFromInstallationForAuthenticatedUser:["DELETE /user/installations/{installation_id}/repositories/{repository_id}"],resetToken:["PATCH /applications/{client_id}/token"],revokeInstallationAccessToken:["DELETE /installation/token"],scopeToken:["POST /applications/{client_id}/token/scoped"],suspendInstallation:["PUT /app/installations/{installation_id}/suspended"],unsuspendInstallation:["DELETE /app/installations/{installation_id}/suspended"],updateWebhookConfigForApp:["PATCH /app/hook/config"]},billing:{getGithubActionsBillingOrg:["GET /orgs/{org}/settings/billing/actions"],getGithubActionsBillingUser:["GET /users/{username}/settings/billing/actions"],getGithubBillingUsageReportOrg:["GET /organizations/{org}/settings/billing/usage"],getGithubPackagesBillingOrg:["GET /orgs/{org}/settings/billing/packages"],getGithubPackagesBillingUser:["GET /users/{username}/settings/billing/packages"],getSharedStorageBillingOrg:["GET /orgs/{org}/settings/billing/shared-storage"],getSharedStorageBillingUser:["GET /users/{username}/settings/billing/shared-storage"]},checks:{create:["POST /repos/{owner}/{repo}/check-runs"],createSuite:["POST /repos/{owner}/{repo}/check-suites"],get:["GET /repos/{owner}/{repo}/check-runs/{check_run_id}"],getSuite:["GET /repos/{owner}/{repo}/check-suites/{check_suite_id}"],listAnnotations:["GET /repos/{owner}/{repo}/check-runs/{check_run_id}/annotations"],listForRef:["GET /repos/{owner}/{repo}/commits/{ref}/check-runs"],listForSuite:["GET /repos/{owner}/{repo}/check-suites/{check_suite_id}/check-runs"],listSuitesForRef:["GET /repos/{owner}/{repo}/commits/{ref}/check-suites"],rerequestRun:["POST /repos/{owner}/{repo}/check-runs/{check_run_id}/rerequest"],rerequestSuite:["POST /repos/{owner}/{repo}/check-suites/{check_suite_id}/rerequest"],setSuitesPreferences:["PATCH /repos/{owner}/{repo}/check-suites/preferences"],update:["PATCH /repos/{owner}/{repo}/check-runs/{check_run_id}"]},codeScanning:{commitAutofix:["POST /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/autofix/commits"],createAutofix:["POST /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/autofix"],createVariantAnalysis:["POST /repos/{owner}/{repo}/code-scanning/codeql/variant-analyses"],deleteAnalysis:["DELETE /repos/{owner}/{repo}/code-scanning/analyses/{analysis_id}{?confirm_delete}"],deleteCodeqlDatabase:["DELETE /repos/{owner}/{repo}/code-scanning/codeql/databases/{language}"],getAlert:["GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}",{},{renamedParameters:{alert_id:"alert_number"}}],getAnalysis:["GET /repos/{owner}/{repo}/code-scanning/analyses/{analysis_id}"],getAutofix:["GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/autofix"],getCodeqlDatabase:["GET /repos/{owner}/{repo}/code-scanning/codeql/databases/{language}"],getDefaultSetup:["GET /repos/{owner}/{repo}/code-scanning/default-setup"],getSarif:["GET /repos/{owner}/{repo}/code-scanning/sarifs/{sarif_id}"],getVariantAnalysis:["GET /repos/{owner}/{repo}/code-scanning/codeql/variant-analyses/{codeql_variant_analysis_id}"],getVariantAnalysisRepoTask:["GET /repos/{owner}/{repo}/code-scanning/codeql/variant-analyses/{codeql_variant_analysis_id}/repos/{repo_owner}/{repo_name}"],listAlertInstances:["GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/instances"],listAlertsForOrg:["GET /orgs/{org}/code-scanning/alerts"],listAlertsForRepo:["GET /repos/{owner}/{repo}/code-scanning/alerts"],listAlertsInstances:["GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/instances",{},{renamed:["codeScanning","listAlertInstances"]}],listCodeqlDatabases:["GET /repos/{owner}/{repo}/code-scanning/codeql/databases"],listRecentAnalyses:["GET /repos/{owner}/{repo}/code-scanning/analyses"],updateAlert:["PATCH /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}"],updateDefaultSetup:["PATCH /repos/{owner}/{repo}/code-scanning/default-setup"],uploadSarif:["POST /repos/{owner}/{repo}/code-scanning/sarifs"]},codeSecurity:{attachConfiguration:["POST /orgs/{org}/code-security/configurations/{configuration_id}/attach"],attachEnterpriseConfiguration:["POST /enterprises/{enterprise}/code-security/configurations/{configuration_id}/attach"],createConfiguration:["POST /orgs/{org}/code-security/configurations"],createConfigurationForEnterprise:["POST /enterprises/{enterprise}/code-security/configurations"],deleteConfiguration:["DELETE /orgs/{org}/code-security/configurations/{configuration_id}"],deleteConfigurationForEnterprise:["DELETE /enterprises/{enterprise}/code-security/configurations/{configuration_id}"],detachConfiguration:["DELETE /orgs/{org}/code-security/configurations/detach"],getConfiguration:["GET /orgs/{org}/code-security/configurations/{configuration_id}"],getConfigurationForRepository:["GET /repos/{owner}/{repo}/code-security-configuration"],getConfigurationsForEnterprise:["GET /enterprises/{enterprise}/code-security/configurations"],getConfigurationsForOrg:["GET /orgs/{org}/code-security/configurations"],getDefaultConfigurations:["GET /orgs/{org}/code-security/configurations/defaults"],getDefaultConfigurationsForEnterprise:["GET /enterprises/{enterprise}/code-security/configurations/defaults"],getRepositoriesForConfiguration:["GET /orgs/{org}/code-security/configurations/{configuration_id}/repositories"],getRepositoriesForEnterpriseConfiguration:["GET /enterprises/{enterprise}/code-security/configurations/{configuration_id}/repositories"],getSingleConfigurationForEnterprise:["GET /enterprises/{enterprise}/code-security/configurations/{configuration_id}"],setConfigurationAsDefault:["PUT /orgs/{org}/code-security/configurations/{configuration_id}/defaults"],setConfigurationAsDefaultForEnterprise:["PUT /enterprises/{enterprise}/code-security/configurations/{configuration_id}/defaults"],updateConfiguration:["PATCH /orgs/{org}/code-security/configurations/{configuration_id}"],updateEnterpriseConfiguration:["PATCH /enterprises/{enterprise}/code-security/configurations/{configuration_id}"]},codesOfConduct:{getAllCodesOfConduct:["GET /codes_of_conduct"],getConductCode:["GET /codes_of_conduct/{key}"]},codespaces:{addRepositoryForSecretForAuthenticatedUser:["PUT /user/codespaces/secrets/{secret_name}/repositories/{repository_id}"],addSelectedRepoToOrgSecret:["PUT /orgs/{org}/codespaces/secrets/{secret_name}/repositories/{repository_id}"],checkPermissionsForDevcontainer:["GET /repos/{owner}/{repo}/codespaces/permissions_check"],codespaceMachinesForAuthenticatedUser:["GET /user/codespaces/{codespace_name}/machines"],createForAuthenticatedUser:["POST /user/codespaces"],createOrUpdateOrgSecret:["PUT /orgs/{org}/codespaces/secrets/{secret_name}"],createOrUpdateRepoSecret:["PUT /repos/{owner}/{repo}/codespaces/secrets/{secret_name}"],createOrUpdateSecretForAuthenticatedUser:["PUT /user/codespaces/secrets/{secret_name}"],createWithPrForAuthenticatedUser:["POST /repos/{owner}/{repo}/pulls/{pull_number}/codespaces"],createWithRepoForAuthenticatedUser:["POST /repos/{owner}/{repo}/codespaces"],deleteForAuthenticatedUser:["DELETE /user/codespaces/{codespace_name}"],deleteFromOrganization:["DELETE /orgs/{org}/members/{username}/codespaces/{codespace_name}"],deleteOrgSecret:["DELETE /orgs/{org}/codespaces/secrets/{secret_name}"],deleteRepoSecret:["DELETE /repos/{owner}/{repo}/codespaces/secrets/{secret_name}"],deleteSecretForAuthenticatedUser:["DELETE /user/codespaces/secrets/{secret_name}"],exportForAuthenticatedUser:["POST /user/codespaces/{codespace_name}/exports"],getCodespacesForUserInOrg:["GET /orgs/{org}/members/{username}/codespaces"],getExportDetailsForAuthenticatedUser:["GET /user/codespaces/{codespace_name}/exports/{export_id}"],getForAuthenticatedUser:["GET /user/codespaces/{codespace_name}"],getOrgPublicKey:["GET /orgs/{org}/codespaces/secrets/public-key"],getOrgSecret:["GET /orgs/{org}/codespaces/secrets/{secret_name}"],getPublicKeyForAuthenticatedUser:["GET /user/codespaces/secrets/public-key"],getRepoPublicKey:["GET /repos/{owner}/{repo}/codespaces/secrets/public-key"],getRepoSecret:["GET /repos/{owner}/{repo}/codespaces/secrets/{secret_name}"],getSecretForAuthenticatedUser:["GET /user/codespaces/secrets/{secret_name}"],listDevcontainersInRepositoryForAuthenticatedUser:["GET /repos/{owner}/{repo}/codespaces/devcontainers"],listForAuthenticatedUser:["GET /user/codespaces"],listInOrganization:["GET /orgs/{org}/codespaces",{},{renamedParameters:{org_id:"org"}}],listInRepositoryForAuthenticatedUser:["GET /repos/{owner}/{repo}/codespaces"],listOrgSecrets:["GET /orgs/{org}/codespaces/secrets"],listRepoSecrets:["GET /repos/{owner}/{repo}/codespaces/secrets"],listRepositoriesForSecretForAuthenticatedUser:["GET /user/codespaces/secrets/{secret_name}/repositories"],listSecretsForAuthenticatedUser:["GET /user/codespaces/secrets"],listSelectedReposForOrgSecret:["GET /orgs/{org}/codespaces/secrets/{secret_name}/repositories"],preFlightWithRepoForAuthenticatedUser:["GET /repos/{owner}/{repo}/codespaces/new"],publishForAuthenticatedUser:["POST /user/codespaces/{codespace_name}/publish"],removeRepositoryForSecretForAuthenticatedUser:["DELETE /user/codespaces/secrets/{secret_name}/repositories/{repository_id}"],removeSelectedRepoFromOrgSecret:["DELETE /orgs/{org}/codespaces/secrets/{secret_name}/repositories/{repository_id}"],repoMachinesForAuthenticatedUser:["GET /repos/{owner}/{repo}/codespaces/machines"],setRepositoriesForSecretForAuthenticatedUser:["PUT /user/codespaces/secrets/{secret_name}/repositories"],setSelectedReposForOrgSecret:["PUT /orgs/{org}/codespaces/secrets/{secret_name}/repositories"],startForAuthenticatedUser:["POST /user/codespaces/{codespace_name}/start"],stopForAuthenticatedUser:["POST /user/codespaces/{codespace_name}/stop"],stopInOrganization:["POST /orgs/{org}/members/{username}/codespaces/{codespace_name}/stop"],updateForAuthenticatedUser:["PATCH /user/codespaces/{codespace_name}"]},copilot:{addCopilotSeatsForTeams:["POST /orgs/{org}/copilot/billing/selected_teams"],addCopilotSeatsForUsers:["POST /orgs/{org}/copilot/billing/selected_users"],cancelCopilotSeatAssignmentForTeams:["DELETE /orgs/{org}/copilot/billing/selected_teams"],cancelCopilotSeatAssignmentForUsers:["DELETE /orgs/{org}/copilot/billing/selected_users"],copilotMetricsForOrganization:["GET /orgs/{org}/copilot/metrics"],copilotMetricsForTeam:["GET /orgs/{org}/team/{team_slug}/copilot/metrics"],getCopilotOrganizationDetails:["GET /orgs/{org}/copilot/billing"],getCopilotSeatDetailsForUser:["GET /orgs/{org}/members/{username}/copilot"],listCopilotSeats:["GET /orgs/{org}/copilot/billing/seats"],usageMetricsForOrg:["GET /orgs/{org}/copilot/usage"],usageMetricsForTeam:["GET /orgs/{org}/team/{team_slug}/copilot/usage"]},dependabot:{addSelectedRepoToOrgSecret:["PUT /orgs/{org}/dependabot/secrets/{secret_name}/repositories/{repository_id}"],createOrUpdateOrgSecret:["PUT /orgs/{org}/dependabot/secrets/{secret_name}"],createOrUpdateRepoSecret:["PUT /repos/{owner}/{repo}/dependabot/secrets/{secret_name}"],deleteOrgSecret:["DELETE /orgs/{org}/dependabot/secrets/{secret_name}"],deleteRepoSecret:["DELETE /repos/{owner}/{repo}/dependabot/secrets/{secret_name}"],getAlert:["GET /repos/{owner}/{repo}/dependabot/alerts/{alert_number}"],getOrgPublicKey:["GET /orgs/{org}/dependabot/secrets/public-key"],getOrgSecret:["GET /orgs/{org}/dependabot/secrets/{secret_name}"],getRepoPublicKey:["GET /repos/{owner}/{repo}/dependabot/secrets/public-key"],getRepoSecret:["GET /repos/{owner}/{repo}/dependabot/secrets/{secret_name}"],listAlertsForEnterprise:["GET /enterprises/{enterprise}/dependabot/alerts"],listAlertsForOrg:["GET /orgs/{org}/dependabot/alerts"],listAlertsForRepo:["GET /repos/{owner}/{repo}/dependabot/alerts"],listOrgSecrets:["GET /orgs/{org}/dependabot/secrets"],listRepoSecrets:["GET /repos/{owner}/{repo}/dependabot/secrets"],listSelectedReposForOrgSecret:["GET /orgs/{org}/dependabot/secrets/{secret_name}/repositories"],removeSelectedRepoFromOrgSecret:["DELETE /orgs/{org}/dependabot/secrets/{secret_name}/repositories/{repository_id}"],setSelectedReposForOrgSecret:["PUT /orgs/{org}/dependabot/secrets/{secret_name}/repositories"],updateAlert:["PATCH /repos/{owner}/{repo}/dependabot/alerts/{alert_number}"]},dependencyGraph:{createRepositorySnapshot:["POST /repos/{owner}/{repo}/dependency-graph/snapshots"],diffRange:["GET /repos/{owner}/{repo}/dependency-graph/compare/{basehead}"],exportSbom:["GET /repos/{owner}/{repo}/dependency-graph/sbom"]},emojis:{get:["GET /emojis"]},gists:{checkIsStarred:["GET /gists/{gist_id}/star"],create:["POST /gists"],createComment:["POST /gists/{gist_id}/comments"],delete:["DELETE /gists/{gist_id}"],deleteComment:["DELETE /gists/{gist_id}/comments/{comment_id}"],fork:["POST /gists/{gist_id}/forks"],get:["GET /gists/{gist_id}"],getComment:["GET /gists/{gist_id}/comments/{comment_id}"],getRevision:["GET /gists/{gist_id}/{sha}"],list:["GET /gists"],listComments:["GET /gists/{gist_id}/comments"],listCommits:["GET /gists/{gist_id}/commits"],listForUser:["GET /users/{username}/gists"],listForks:["GET /gists/{gist_id}/forks"],listPublic:["GET /gists/public"],listStarred:["GET /gists/starred"],star:["PUT /gists/{gist_id}/star"],unstar:["DELETE /gists/{gist_id}/star"],update:["PATCH /gists/{gist_id}"],updateComment:["PATCH /gists/{gist_id}/comments/{comment_id}"]},git:{createBlob:["POST /repos/{owner}/{repo}/git/blobs"],createCommit:["POST /repos/{owner}/{repo}/git/commits"],createRef:["POST /repos/{owner}/{repo}/git/refs"],createTag:["POST /repos/{owner}/{repo}/git/tags"],createTree:["POST /repos/{owner}/{repo}/git/trees"],deleteRef:["DELETE /repos/{owner}/{repo}/git/refs/{ref}"],getBlob:["GET /repos/{owner}/{repo}/git/blobs/{file_sha}"],getCommit:["GET /repos/{owner}/{repo}/git/commits/{commit_sha}"],getRef:["GET /repos/{owner}/{repo}/git/ref/{ref}"],getTag:["GET /repos/{owner}/{repo}/git/tags/{tag_sha}"],getTree:["GET /repos/{owner}/{repo}/git/trees/{tree_sha}"],listMatchingRefs:["GET /repos/{owner}/{repo}/git/matching-refs/{ref}"],updateRef:["PATCH /repos/{owner}/{repo}/git/refs/{ref}"]},gitignore:{getAllTemplates:["GET /gitignore/templates"],getTemplate:["GET /gitignore/templates/{name}"]},interactions:{getRestrictionsForAuthenticatedUser:["GET /user/interaction-limits"],getRestrictionsForOrg:["GET /orgs/{org}/interaction-limits"],getRestrictionsForRepo:["GET /repos/{owner}/{repo}/interaction-limits"],getRestrictionsForYourPublicRepos:["GET /user/interaction-limits",{},{renamed:["interactions","getRestrictionsForAuthenticatedUser"]}],removeRestrictionsForAuthenticatedUser:["DELETE /user/interaction-limits"],removeRestrictionsForOrg:["DELETE /orgs/{org}/interaction-limits"],removeRestrictionsForRepo:["DELETE /repos/{owner}/{repo}/interaction-limits"],removeRestrictionsForYourPublicRepos:["DELETE /user/interaction-limits",{},{renamed:["interactions","removeRestrictionsForAuthenticatedUser"]}],setRestrictionsForAuthenticatedUser:["PUT /user/interaction-limits"],setRestrictionsForOrg:["PUT /orgs/{org}/interaction-limits"],setRestrictionsForRepo:["PUT /repos/{owner}/{repo}/interaction-limits"],setRestrictionsForYourPublicRepos:["PUT /user/interaction-limits",{},{renamed:["interactions","setRestrictionsForAuthenticatedUser"]}]},issues:{addAssignees:["POST /repos/{owner}/{repo}/issues/{issue_number}/assignees"],addLabels:["POST /repos/{owner}/{repo}/issues/{issue_number}/labels"],addSubIssue:["POST /repos/{owner}/{repo}/issues/{issue_number}/sub_issues"],checkUserCanBeAssigned:["GET /repos/{owner}/{repo}/assignees/{assignee}"],checkUserCanBeAssignedToIssue:["GET /repos/{owner}/{repo}/issues/{issue_number}/assignees/{assignee}"],create:["POST /repos/{owner}/{repo}/issues"],createComment:["POST /repos/{owner}/{repo}/issues/{issue_number}/comments"],createLabel:["POST /repos/{owner}/{repo}/labels"],createMilestone:["POST /repos/{owner}/{repo}/milestones"],deleteComment:["DELETE /repos/{owner}/{repo}/issues/comments/{comment_id}"],deleteLabel:["DELETE /repos/{owner}/{repo}/labels/{name}"],deleteMilestone:["DELETE /repos/{owner}/{repo}/milestones/{milestone_number}"],get:["GET /repos/{owner}/{repo}/issues/{issue_number}"],getComment:["GET /repos/{owner}/{repo}/issues/comments/{comment_id}"],getEvent:["GET /repos/{owner}/{repo}/issues/events/{event_id}"],getLabel:["GET /repos/{owner}/{repo}/labels/{name}"],getMilestone:["GET /repos/{owner}/{repo}/milestones/{milestone_number}"],list:["GET /issues"],listAssignees:["GET /repos/{owner}/{repo}/assignees"],listComments:["GET /repos/{owner}/{repo}/issues/{issue_number}/comments"],listCommentsForRepo:["GET /repos/{owner}/{repo}/issues/comments"],listEvents:["GET /repos/{owner}/{repo}/issues/{issue_number}/events"],listEventsForRepo:["GET /repos/{owner}/{repo}/issues/events"],listEventsForTimeline:["GET /repos/{owner}/{repo}/issues/{issue_number}/timeline"],listForAuthenticatedUser:["GET /user/issues"],listForOrg:["GET /orgs/{org}/issues"],listForRepo:["GET /repos/{owner}/{repo}/issues"],listLabelsForMilestone:["GET /repos/{owner}/{repo}/milestones/{milestone_number}/labels"],listLabelsForRepo:["GET /repos/{owner}/{repo}/labels"],listLabelsOnIssue:["GET /repos/{owner}/{repo}/issues/{issue_number}/labels"],listMilestones:["GET /repos/{owner}/{repo}/milestones"],listSubIssues:["GET /repos/{owner}/{repo}/issues/{issue_number}/sub_issues"],lock:["PUT /repos/{owner}/{repo}/issues/{issue_number}/lock"],removeAllLabels:["DELETE /repos/{owner}/{repo}/issues/{issue_number}/labels"],removeAssignees:["DELETE /repos/{owner}/{repo}/issues/{issue_number}/assignees"],removeLabel:["DELETE /repos/{owner}/{repo}/issues/{issue_number}/labels/{name}"],removeSubIssue:["DELETE /repos/{owner}/{repo}/issues/{issue_number}/sub_issue"],reprioritizeSubIssue:["PATCH /repos/{owner}/{repo}/issues/{issue_number}/sub_issues/priority"],setLabels:["PUT /repos/{owner}/{repo}/issues/{issue_number}/labels"],unlock:["DELETE /repos/{owner}/{repo}/issues/{issue_number}/lock"],update:["PATCH /repos/{owner}/{repo}/issues/{issue_number}"],updateComment:["PATCH /repos/{owner}/{repo}/issues/comments/{comment_id}"],updateLabel:["PATCH /repos/{owner}/{repo}/labels/{name}"],updateMilestone:["PATCH /repos/{owner}/{repo}/milestones/{milestone_number}"]},licenses:{get:["GET /licenses/{license}"],getAllCommonlyUsed:["GET /licenses"],getForRepo:["GET /repos/{owner}/{repo}/license"]},markdown:{render:["POST /markdown"],renderRaw:["POST /markdown/raw",{headers:{"content-type":"text/plain; charset=utf-8"}}]},meta:{get:["GET /meta"],getAllVersions:["GET /versions"],getOctocat:["GET /octocat"],getZen:["GET /zen"],root:["GET /"]},migrations:{deleteArchiveForAuthenticatedUser:["DELETE /user/migrations/{migration_id}/archive"],deleteArchiveForOrg:["DELETE /orgs/{org}/migrations/{migration_id}/archive"],downloadArchiveForOrg:["GET /orgs/{org}/migrations/{migration_id}/archive"],getArchiveForAuthenticatedUser:["GET /user/migrations/{migration_id}/archive"],getStatusForAuthenticatedUser:["GET /user/migrations/{migration_id}"],getStatusForOrg:["GET /orgs/{org}/migrations/{migration_id}"],listForAuthenticatedUser:["GET /user/migrations"],listForOrg:["GET /orgs/{org}/migrations"],listReposForAuthenticatedUser:["GET /user/migrations/{migration_id}/repositories"],listReposForOrg:["GET /orgs/{org}/migrations/{migration_id}/repositories"],listReposForUser:["GET /user/migrations/{migration_id}/repositories",{},{renamed:["migrations","listReposForAuthenticatedUser"]}],startForAuthenticatedUser:["POST /user/migrations"],startForOrg:["POST /orgs/{org}/migrations"],unlockRepoForAuthenticatedUser:["DELETE /user/migrations/{migration_id}/repos/{repo_name}/lock"],unlockRepoForOrg:["DELETE /orgs/{org}/migrations/{migration_id}/repos/{repo_name}/lock"]},oidc:{getOidcCustomSubTemplateForOrg:["GET /orgs/{org}/actions/oidc/customization/sub"],updateOidcCustomSubTemplateForOrg:["PUT /orgs/{org}/actions/oidc/customization/sub"]},orgs:{addSecurityManagerTeam:["PUT /orgs/{org}/security-managers/teams/{team_slug}",{},{deprecated:"octokit.rest.orgs.addSecurityManagerTeam() is deprecated, see https://docs.github.com/rest/orgs/security-managers#add-a-security-manager-team"}],assignTeamToOrgRole:["PUT /orgs/{org}/organization-roles/teams/{team_slug}/{role_id}"],assignUserToOrgRole:["PUT /orgs/{org}/organization-roles/users/{username}/{role_id}"],blockUser:["PUT /orgs/{org}/blocks/{username}"],cancelInvitation:["DELETE /orgs/{org}/invitations/{invitation_id}"],checkBlockedUser:["GET /orgs/{org}/blocks/{username}"],checkMembershipForUser:["GET /orgs/{org}/members/{username}"],checkPublicMembershipForUser:["GET /orgs/{org}/public_members/{username}"],convertMemberToOutsideCollaborator:["PUT /orgs/{org}/outside_collaborators/{username}"],createInvitation:["POST /orgs/{org}/invitations"],createOrUpdateCustomProperties:["PATCH /orgs/{org}/properties/schema"],createOrUpdateCustomPropertiesValuesForRepos:["PATCH /orgs/{org}/properties/values"],createOrUpdateCustomProperty:["PUT /orgs/{org}/properties/schema/{custom_property_name}"],createWebhook:["POST /orgs/{org}/hooks"],delete:["DELETE /orgs/{org}"],deleteWebhook:["DELETE /orgs/{org}/hooks/{hook_id}"],enableOrDisableSecurityProductOnAllOrgRepos:["POST /orgs/{org}/{security_product}/{enablement}",{},{deprecated:"octokit.rest.orgs.enableOrDisableSecurityProductOnAllOrgRepos() is deprecated, see https://docs.github.com/rest/orgs/orgs#enable-or-disable-a-security-feature-for-an-organization"}],get:["GET /orgs/{org}"],getAllCustomProperties:["GET /orgs/{org}/properties/schema"],getCustomProperty:["GET /orgs/{org}/properties/schema/{custom_property_name}"],getMembershipForAuthenticatedUser:["GET /user/memberships/orgs/{org}"],getMembershipForUser:["GET /orgs/{org}/memberships/{username}"],getOrgRole:["GET /orgs/{org}/organization-roles/{role_id}"],getWebhook:["GET /orgs/{org}/hooks/{hook_id}"],getWebhookConfigForOrg:["GET /orgs/{org}/hooks/{hook_id}/config"],getWebhookDelivery:["GET /orgs/{org}/hooks/{hook_id}/deliveries/{delivery_id}"],list:["GET /organizations"],listAppInstallations:["GET /orgs/{org}/installations"],listAttestations:["GET /orgs/{org}/attestations/{subject_digest}"],listBlockedUsers:["GET /orgs/{org}/blocks"],listCustomPropertiesValuesForRepos:["GET /orgs/{org}/properties/values"],listFailedInvitations:["GET /orgs/{org}/failed_invitations"],listForAuthenticatedUser:["GET /user/orgs"],listForUser:["GET /users/{username}/orgs"],listInvitationTeams:["GET /orgs/{org}/invitations/{invitation_id}/teams"],listMembers:["GET /orgs/{org}/members"],listMembershipsForAuthenticatedUser:["GET /user/memberships/orgs"],listOrgRoleTeams:["GET /orgs/{org}/organization-roles/{role_id}/teams"],listOrgRoleUsers:["GET /orgs/{org}/organization-roles/{role_id}/users"],listOrgRoles:["GET /orgs/{org}/organization-roles"],listOrganizationFineGrainedPermissions:["GET /orgs/{org}/organization-fine-grained-permissions"],listOutsideCollaborators:["GET /orgs/{org}/outside_collaborators"],listPatGrantRepositories:["GET /orgs/{org}/personal-access-tokens/{pat_id}/repositories"],listPatGrantRequestRepositories:["GET /orgs/{org}/personal-access-token-requests/{pat_request_id}/repositories"],listPatGrantRequests:["GET /orgs/{org}/personal-access-token-requests"],listPatGrants:["GET /orgs/{org}/personal-access-tokens"],listPendingInvitations:["GET /orgs/{org}/invitations"],listPublicMembers:["GET /orgs/{org}/public_members"],listSecurityManagerTeams:["GET /orgs/{org}/security-managers",{},{deprecated:"octokit.rest.orgs.listSecurityManagerTeams() is deprecated, see https://docs.github.com/rest/orgs/security-managers#list-security-manager-teams"}],listWebhookDeliveries:["GET /orgs/{org}/hooks/{hook_id}/deliveries"],listWebhooks:["GET /orgs/{org}/hooks"],pingWebhook:["POST /orgs/{org}/hooks/{hook_id}/pings"],redeliverWebhookDelivery:["POST /orgs/{org}/hooks/{hook_id}/deliveries/{delivery_id}/attempts"],removeCustomProperty:["DELETE /orgs/{org}/properties/schema/{custom_property_name}"],removeMember:["DELETE /orgs/{org}/members/{username}"],removeMembershipForUser:["DELETE /orgs/{org}/memberships/{username}"],removeOutsideCollaborator:["DELETE /orgs/{org}/outside_collaborators/{username}"],removePublicMembershipForAuthenticatedUser:["DELETE /orgs/{org}/public_members/{username}"],removeSecurityManagerTeam:["DELETE /orgs/{org}/security-managers/teams/{team_slug}",{},{deprecated:"octokit.rest.orgs.removeSecurityManagerTeam() is deprecated, see https://docs.github.com/rest/orgs/security-managers#remove-a-security-manager-team"}],reviewPatGrantRequest:["POST /orgs/{org}/personal-access-token-requests/{pat_request_id}"],reviewPatGrantRequestsInBulk:["POST /orgs/{org}/personal-access-token-requests"],revokeAllOrgRolesTeam:["DELETE /orgs/{org}/organization-roles/teams/{team_slug}"],revokeAllOrgRolesUser:["DELETE /orgs/{org}/organization-roles/users/{username}"],revokeOrgRoleTeam:["DELETE /orgs/{org}/organization-roles/teams/{team_slug}/{role_id}"],revokeOrgRoleUser:["DELETE /orgs/{org}/organization-roles/users/{username}/{role_id}"],setMembershipForUser:["PUT /orgs/{org}/memberships/{username}"],setPublicMembershipForAuthenticatedUser:["PUT /orgs/{org}/public_members/{username}"],unblockUser:["DELETE /orgs/{org}/blocks/{username}"],update:["PATCH /orgs/{org}"],updateMembershipForAuthenticatedUser:["PATCH /user/memberships/orgs/{org}"],updatePatAccess:["POST /orgs/{org}/personal-access-tokens/{pat_id}"],updatePatAccesses:["POST /orgs/{org}/personal-access-tokens"],updateWebhook:["PATCH /orgs/{org}/hooks/{hook_id}"],updateWebhookConfigForOrg:["PATCH /orgs/{org}/hooks/{hook_id}/config"]},packages:{deletePackageForAuthenticatedUser:["DELETE /user/packages/{package_type}/{package_name}"],deletePackageForOrg:["DELETE /orgs/{org}/packages/{package_type}/{package_name}"],deletePackageForUser:["DELETE /users/{username}/packages/{package_type}/{package_name}"],deletePackageVersionForAuthenticatedUser:["DELETE /user/packages/{package_type}/{package_name}/versions/{package_version_id}"],deletePackageVersionForOrg:["DELETE /orgs/{org}/packages/{package_type}/{package_name}/versions/{package_version_id}"],deletePackageVersionForUser:["DELETE /users/{username}/packages/{package_type}/{package_name}/versions/{package_version_id}"],getAllPackageVersionsForAPackageOwnedByAnOrg:["GET /orgs/{org}/packages/{package_type}/{package_name}/versions",{},{renamed:["packages","getAllPackageVersionsForPackageOwnedByOrg"]}],getAllPackageVersionsForAPackageOwnedByTheAuthenticatedUser:["GET /user/packages/{package_type}/{package_name}/versions",{},{renamed:["packages","getAllPackageVersionsForPackageOwnedByAuthenticatedUser"]}],getAllPackageVersionsForPackageOwnedByAuthenticatedUser:["GET /user/packages/{package_type}/{package_name}/versions"],getAllPackageVersionsForPackageOwnedByOrg:["GET /orgs/{org}/packages/{package_type}/{package_name}/versions"],getAllPackageVersionsForPackageOwnedByUser:["GET /users/{username}/packages/{package_type}/{package_name}/versions"],getPackageForAuthenticatedUser:["GET /user/packages/{package_type}/{package_name}"],getPackageForOrganization:["GET /orgs/{org}/packages/{package_type}/{package_name}"],getPackageForUser:["GET /users/{username}/packages/{package_type}/{package_name}"],getPackageVersionForAuthenticatedUser:["GET /user/packages/{package_type}/{package_name}/versions/{package_version_id}"],getPackageVersionForOrganization:["GET /orgs/{org}/packages/{package_type}/{package_name}/versions/{package_version_id}"],getPackageVersionForUser:["GET /users/{username}/packages/{package_type}/{package_name}/versions/{package_version_id}"],listDockerMigrationConflictingPackagesForAuthenticatedUser:["GET /user/docker/conflicts"],listDockerMigrationConflictingPackagesForOrganization:["GET /orgs/{org}/docker/conflicts"],listDockerMigrationConflictingPackagesForUser:["GET /users/{username}/docker/conflicts"],listPackagesForAuthenticatedUser:["GET /user/packages"],listPackagesForOrganization:["GET /orgs/{org}/packages"],listPackagesForUser:["GET /users/{username}/packages"],restorePackageForAuthenticatedUser:["POST /user/packages/{package_type}/{package_name}/restore{?token}"],restorePackageForOrg:["POST /orgs/{org}/packages/{package_type}/{package_name}/restore{?token}"],restorePackageForUser:["POST /users/{username}/packages/{package_type}/{package_name}/restore{?token}"],restorePackageVersionForAuthenticatedUser:["POST /user/packages/{package_type}/{package_name}/versions/{package_version_id}/restore"],restorePackageVersionForOrg:["POST /orgs/{org}/packages/{package_type}/{package_name}/versions/{package_version_id}/restore"],restorePackageVersionForUser:["POST /users/{username}/packages/{package_type}/{package_name}/versions/{package_version_id}/restore"]},privateRegistries:{createOrgPrivateRegistry:["POST /orgs/{org}/private-registries"],deleteOrgPrivateRegistry:["DELETE /orgs/{org}/private-registries/{secret_name}"],getOrgPrivateRegistry:["GET /orgs/{org}/private-registries/{secret_name}"],getOrgPublicKey:["GET /orgs/{org}/private-registries/public-key"],listOrgPrivateRegistries:["GET /orgs/{org}/private-registries"],updateOrgPrivateRegistry:["PATCH /orgs/{org}/private-registries/{secret_name}"]},projects:{addCollaborator:["PUT /projects/{project_id}/collaborators/{username}"],createCard:["POST /projects/columns/{column_id}/cards"],createColumn:["POST /projects/{project_id}/columns"],createForAuthenticatedUser:["POST /user/projects"],createForOrg:["POST /orgs/{org}/projects"],createForRepo:["POST /repos/{owner}/{repo}/projects"],delete:["DELETE /projects/{project_id}"],deleteCard:["DELETE /projects/columns/cards/{card_id}"],deleteColumn:["DELETE /projects/columns/{column_id}"],get:["GET /projects/{project_id}"],getCard:["GET /projects/columns/cards/{card_id}"],getColumn:["GET /projects/columns/{column_id}"],getPermissionForUser:["GET /projects/{project_id}/collaborators/{username}/permission"],listCards:["GET /projects/columns/{column_id}/cards"],listCollaborators:["GET /projects/{project_id}/collaborators"],listColumns:["GET /projects/{project_id}/columns"],listForOrg:["GET /orgs/{org}/projects"],listForRepo:["GET /repos/{owner}/{repo}/projects"],listForUser:["GET /users/{username}/projects"],moveCard:["POST /projects/columns/cards/{card_id}/moves"],moveColumn:["POST /projects/columns/{column_id}/moves"],removeCollaborator:["DELETE /projects/{project_id}/collaborators/{username}"],update:["PATCH /projects/{project_id}"],updateCard:["PATCH /projects/columns/cards/{card_id}"],updateColumn:["PATCH /projects/columns/{column_id}"]},pulls:{checkIfMerged:["GET /repos/{owner}/{repo}/pulls/{pull_number}/merge"],create:["POST /repos/{owner}/{repo}/pulls"],createReplyForReviewComment:["POST /repos/{owner}/{repo}/pulls/{pull_number}/comments/{comment_id}/replies"],createReview:["POST /repos/{owner}/{repo}/pulls/{pull_number}/reviews"],createReviewComment:["POST /repos/{owner}/{repo}/pulls/{pull_number}/comments"],deletePendingReview:["DELETE /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}"],deleteReviewComment:["DELETE /repos/{owner}/{repo}/pulls/comments/{comment_id}"],dismissReview:["PUT /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/dismissals"],get:["GET /repos/{owner}/{repo}/pulls/{pull_number}"],getReview:["GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}"],getReviewComment:["GET /repos/{owner}/{repo}/pulls/comments/{comment_id}"],list:["GET /repos/{owner}/{repo}/pulls"],listCommentsForReview:["GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/comments"],listCommits:["GET /repos/{owner}/{repo}/pulls/{pull_number}/commits"],listFiles:["GET /repos/{owner}/{repo}/pulls/{pull_number}/files"],listRequestedReviewers:["GET /repos/{owner}/{repo}/pulls/{pull_number}/requested_reviewers"],listReviewComments:["GET /repos/{owner}/{repo}/pulls/{pull_number}/comments"],listReviewCommentsForRepo:["GET /repos/{owner}/{repo}/pulls/comments"],listReviews:["GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews"],merge:["PUT /repos/{owner}/{repo}/pulls/{pull_number}/merge"],removeRequestedReviewers:["DELETE /repos/{owner}/{repo}/pulls/{pull_number}/requested_reviewers"],requestReviewers:["POST /repos/{owner}/{repo}/pulls/{pull_number}/requested_reviewers"],submitReview:["POST /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/events"],update:["PATCH /repos/{owner}/{repo}/pulls/{pull_number}"],updateBranch:["PUT /repos/{owner}/{repo}/pulls/{pull_number}/update-branch"],updateReview:["PUT /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}"],updateReviewComment:["PATCH /repos/{owner}/{repo}/pulls/comments/{comment_id}"]},rateLimit:{get:["GET /rate_limit"]},reactions:{createForCommitComment:["POST /repos/{owner}/{repo}/comments/{comment_id}/reactions"],createForIssue:["POST /repos/{owner}/{repo}/issues/{issue_number}/reactions"],createForIssueComment:["POST /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions"],createForPullRequestReviewComment:["POST /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions"],createForRelease:["POST /repos/{owner}/{repo}/releases/{release_id}/reactions"],createForTeamDiscussionCommentInOrg:["POST /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions"],createForTeamDiscussionInOrg:["POST /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions"],deleteForCommitComment:["DELETE /repos/{owner}/{repo}/comments/{comment_id}/reactions/{reaction_id}"],deleteForIssue:["DELETE /repos/{owner}/{repo}/issues/{issue_number}/reactions/{reaction_id}"],deleteForIssueComment:["DELETE /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions/{reaction_id}"],deleteForPullRequestComment:["DELETE /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions/{reaction_id}"],deleteForRelease:["DELETE /repos/{owner}/{repo}/releases/{release_id}/reactions/{reaction_id}"],deleteForTeamDiscussion:["DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions/{reaction_id}"],deleteForTeamDiscussionComment:["DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions/{reaction_id}"],listForCommitComment:["GET /repos/{owner}/{repo}/comments/{comment_id}/reactions"],listForIssue:["GET /repos/{owner}/{repo}/issues/{issue_number}/reactions"],listForIssueComment:["GET /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions"],listForPullRequestReviewComment:["GET /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions"],listForRelease:["GET /repos/{owner}/{repo}/releases/{release_id}/reactions"],listForTeamDiscussionCommentInOrg:["GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions"],listForTeamDiscussionInOrg:["GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions"]},repos:{acceptInvitation:["PATCH /user/repository_invitations/{invitation_id}",{},{renamed:["repos","acceptInvitationForAuthenticatedUser"]}],acceptInvitationForAuthenticatedUser:["PATCH /user/repository_invitations/{invitation_id}"],addAppAccessRestrictions:["POST /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps",{},{mapToData:"apps"}],addCollaborator:["PUT /repos/{owner}/{repo}/collaborators/{username}"],addStatusCheckContexts:["POST /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts",{},{mapToData:"contexts"}],addTeamAccessRestrictions:["POST /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams",{},{mapToData:"teams"}],addUserAccessRestrictions:["POST /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users",{},{mapToData:"users"}],cancelPagesDeployment:["POST /repos/{owner}/{repo}/pages/deployments/{pages_deployment_id}/cancel"],checkAutomatedSecurityFixes:["GET /repos/{owner}/{repo}/automated-security-fixes"],checkCollaborator:["GET /repos/{owner}/{repo}/collaborators/{username}"],checkPrivateVulnerabilityReporting:["GET /repos/{owner}/{repo}/private-vulnerability-reporting"],checkVulnerabilityAlerts:["GET /repos/{owner}/{repo}/vulnerability-alerts"],codeownersErrors:["GET /repos/{owner}/{repo}/codeowners/errors"],compareCommits:["GET /repos/{owner}/{repo}/compare/{base}...{head}"],compareCommitsWithBasehead:["GET /repos/{owner}/{repo}/compare/{basehead}"],createAttestation:["POST /repos/{owner}/{repo}/attestations"],createAutolink:["POST /repos/{owner}/{repo}/autolinks"],createCommitComment:["POST /repos/{owner}/{repo}/commits/{commit_sha}/comments"],createCommitSignatureProtection:["POST /repos/{owner}/{repo}/branches/{branch}/protection/required_signatures"],createCommitStatus:["POST /repos/{owner}/{repo}/statuses/{sha}"],createDeployKey:["POST /repos/{owner}/{repo}/keys"],createDeployment:["POST /repos/{owner}/{repo}/deployments"],createDeploymentBranchPolicy:["POST /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies"],createDeploymentProtectionRule:["POST /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules"],createDeploymentStatus:["POST /repos/{owner}/{repo}/deployments/{deployment_id}/statuses"],createDispatchEvent:["POST /repos/{owner}/{repo}/dispatches"],createForAuthenticatedUser:["POST /user/repos"],createFork:["POST /repos/{owner}/{repo}/forks"],createInOrg:["POST /orgs/{org}/repos"],createOrUpdateCustomPropertiesValues:["PATCH /repos/{owner}/{repo}/properties/values"],createOrUpdateEnvironment:["PUT /repos/{owner}/{repo}/environments/{environment_name}"],createOrUpdateFileContents:["PUT /repos/{owner}/{repo}/contents/{path}"],createOrgRuleset:["POST /orgs/{org}/rulesets"],createPagesDeployment:["POST /repos/{owner}/{repo}/pages/deployments"],createPagesSite:["POST /repos/{owner}/{repo}/pages"],createRelease:["POST /repos/{owner}/{repo}/releases"],createRepoRuleset:["POST /repos/{owner}/{repo}/rulesets"],createUsingTemplate:["POST /repos/{template_owner}/{template_repo}/generate"],createWebhook:["POST /repos/{owner}/{repo}/hooks"],declineInvitation:["DELETE /user/repository_invitations/{invitation_id}",{},{renamed:["repos","declineInvitationForAuthenticatedUser"]}],declineInvitationForAuthenticatedUser:["DELETE /user/repository_invitations/{invitation_id}"],delete:["DELETE /repos/{owner}/{repo}"],deleteAccessRestrictions:["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions"],deleteAdminBranchProtection:["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/enforce_admins"],deleteAnEnvironment:["DELETE /repos/{owner}/{repo}/environments/{environment_name}"],deleteAutolink:["DELETE /repos/{owner}/{repo}/autolinks/{autolink_id}"],deleteBranchProtection:["DELETE /repos/{owner}/{repo}/branches/{branch}/protection"],deleteCommitComment:["DELETE /repos/{owner}/{repo}/comments/{comment_id}"],deleteCommitSignatureProtection:["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_signatures"],deleteDeployKey:["DELETE /repos/{owner}/{repo}/keys/{key_id}"],deleteDeployment:["DELETE /repos/{owner}/{repo}/deployments/{deployment_id}"],deleteDeploymentBranchPolicy:["DELETE /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies/{branch_policy_id}"],deleteFile:["DELETE /repos/{owner}/{repo}/contents/{path}"],deleteInvitation:["DELETE /repos/{owner}/{repo}/invitations/{invitation_id}"],deleteOrgRuleset:["DELETE /orgs/{org}/rulesets/{ruleset_id}"],deletePagesSite:["DELETE /repos/{owner}/{repo}/pages"],deletePullRequestReviewProtection:["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_pull_request_reviews"],deleteRelease:["DELETE /repos/{owner}/{repo}/releases/{release_id}"],deleteReleaseAsset:["DELETE /repos/{owner}/{repo}/releases/assets/{asset_id}"],deleteRepoRuleset:["DELETE /repos/{owner}/{repo}/rulesets/{ruleset_id}"],deleteWebhook:["DELETE /repos/{owner}/{repo}/hooks/{hook_id}"],disableAutomatedSecurityFixes:["DELETE /repos/{owner}/{repo}/automated-security-fixes"],disableDeploymentProtectionRule:["DELETE /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/{protection_rule_id}"],disablePrivateVulnerabilityReporting:["DELETE /repos/{owner}/{repo}/private-vulnerability-reporting"],disableVulnerabilityAlerts:["DELETE /repos/{owner}/{repo}/vulnerability-alerts"],downloadArchive:["GET /repos/{owner}/{repo}/zipball/{ref}",{},{renamed:["repos","downloadZipballArchive"]}],downloadTarballArchive:["GET /repos/{owner}/{repo}/tarball/{ref}"],downloadZipballArchive:["GET /repos/{owner}/{repo}/zipball/{ref}"],enableAutomatedSecurityFixes:["PUT /repos/{owner}/{repo}/automated-security-fixes"],enablePrivateVulnerabilityReporting:["PUT /repos/{owner}/{repo}/private-vulnerability-reporting"],enableVulnerabilityAlerts:["PUT /repos/{owner}/{repo}/vulnerability-alerts"],generateReleaseNotes:["POST /repos/{owner}/{repo}/releases/generate-notes"],get:["GET /repos/{owner}/{repo}"],getAccessRestrictions:["GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions"],getAdminBranchProtection:["GET /repos/{owner}/{repo}/branches/{branch}/protection/enforce_admins"],getAllDeploymentProtectionRules:["GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules"],getAllEnvironments:["GET /repos/{owner}/{repo}/environments"],getAllStatusCheckContexts:["GET /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts"],getAllTopics:["GET /repos/{owner}/{repo}/topics"],getAppsWithAccessToProtectedBranch:["GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps"],getAutolink:["GET /repos/{owner}/{repo}/autolinks/{autolink_id}"],getBranch:["GET /repos/{owner}/{repo}/branches/{branch}"],getBranchProtection:["GET /repos/{owner}/{repo}/branches/{branch}/protection"],getBranchRules:["GET /repos/{owner}/{repo}/rules/branches/{branch}"],getClones:["GET /repos/{owner}/{repo}/traffic/clones"],getCodeFrequencyStats:["GET /repos/{owner}/{repo}/stats/code_frequency"],getCollaboratorPermissionLevel:["GET /repos/{owner}/{repo}/collaborators/{username}/permission"],getCombinedStatusForRef:["GET /repos/{owner}/{repo}/commits/{ref}/status"],getCommit:["GET /repos/{owner}/{repo}/commits/{ref}"],getCommitActivityStats:["GET /repos/{owner}/{repo}/stats/commit_activity"],getCommitComment:["GET /repos/{owner}/{repo}/comments/{comment_id}"],getCommitSignatureProtection:["GET /repos/{owner}/{repo}/branches/{branch}/protection/required_signatures"],getCommunityProfileMetrics:["GET /repos/{owner}/{repo}/community/profile"],getContent:["GET /repos/{owner}/{repo}/contents/{path}"],getContributorsStats:["GET /repos/{owner}/{repo}/stats/contributors"],getCustomDeploymentProtectionRule:["GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/{protection_rule_id}"],getCustomPropertiesValues:["GET /repos/{owner}/{repo}/properties/values"],getDeployKey:["GET /repos/{owner}/{repo}/keys/{key_id}"],getDeployment:["GET /repos/{owner}/{repo}/deployments/{deployment_id}"],getDeploymentBranchPolicy:["GET /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies/{branch_policy_id}"],getDeploymentStatus:["GET /repos/{owner}/{repo}/deployments/{deployment_id}/statuses/{status_id}"],getEnvironment:["GET /repos/{owner}/{repo}/environments/{environment_name}"],getLatestPagesBuild:["GET /repos/{owner}/{repo}/pages/builds/latest"],getLatestRelease:["GET /repos/{owner}/{repo}/releases/latest"],getOrgRuleSuite:["GET /orgs/{org}/rulesets/rule-suites/{rule_suite_id}"],getOrgRuleSuites:["GET /orgs/{org}/rulesets/rule-suites"],getOrgRuleset:["GET /orgs/{org}/rulesets/{ruleset_id}"],getOrgRulesets:["GET /orgs/{org}/rulesets"],getPages:["GET /repos/{owner}/{repo}/pages"],getPagesBuild:["GET /repos/{owner}/{repo}/pages/builds/{build_id}"],getPagesDeployment:["GET /repos/{owner}/{repo}/pages/deployments/{pages_deployment_id}"],getPagesHealthCheck:["GET /repos/{owner}/{repo}/pages/health"],getParticipationStats:["GET /repos/{owner}/{repo}/stats/participation"],getPullRequestReviewProtection:["GET /repos/{owner}/{repo}/branches/{branch}/protection/required_pull_request_reviews"],getPunchCardStats:["GET /repos/{owner}/{repo}/stats/punch_card"],getReadme:["GET /repos/{owner}/{repo}/readme"],getReadmeInDirectory:["GET /repos/{owner}/{repo}/readme/{dir}"],getRelease:["GET /repos/{owner}/{repo}/releases/{release_id}"],getReleaseAsset:["GET /repos/{owner}/{repo}/releases/assets/{asset_id}"],getReleaseByTag:["GET /repos/{owner}/{repo}/releases/tags/{tag}"],getRepoRuleSuite:["GET /repos/{owner}/{repo}/rulesets/rule-suites/{rule_suite_id}"],getRepoRuleSuites:["GET /repos/{owner}/{repo}/rulesets/rule-suites"],getRepoRuleset:["GET /repos/{owner}/{repo}/rulesets/{ruleset_id}"],getRepoRulesets:["GET /repos/{owner}/{repo}/rulesets"],getStatusChecksProtection:["GET /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks"],getTeamsWithAccessToProtectedBranch:["GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams"],getTopPaths:["GET /repos/{owner}/{repo}/traffic/popular/paths"],getTopReferrers:["GET /repos/{owner}/{repo}/traffic/popular/referrers"],getUsersWithAccessToProtectedBranch:["GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users"],getViews:["GET /repos/{owner}/{repo}/traffic/views"],getWebhook:["GET /repos/{owner}/{repo}/hooks/{hook_id}"],getWebhookConfigForRepo:["GET /repos/{owner}/{repo}/hooks/{hook_id}/config"],getWebhookDelivery:["GET /repos/{owner}/{repo}/hooks/{hook_id}/deliveries/{delivery_id}"],listActivities:["GET /repos/{owner}/{repo}/activity"],listAttestations:["GET /repos/{owner}/{repo}/attestations/{subject_digest}"],listAutolinks:["GET /repos/{owner}/{repo}/autolinks"],listBranches:["GET /repos/{owner}/{repo}/branches"],listBranchesForHeadCommit:["GET /repos/{owner}/{repo}/commits/{commit_sha}/branches-where-head"],listCollaborators:["GET /repos/{owner}/{repo}/collaborators"],listCommentsForCommit:["GET /repos/{owner}/{repo}/commits/{commit_sha}/comments"],listCommitCommentsForRepo:["GET /repos/{owner}/{repo}/comments"],listCommitStatusesForRef:["GET /repos/{owner}/{repo}/commits/{ref}/statuses"],listCommits:["GET /repos/{owner}/{repo}/commits"],listContributors:["GET /repos/{owner}/{repo}/contributors"],listCustomDeploymentRuleIntegrations:["GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/apps"],listDeployKeys:["GET /repos/{owner}/{repo}/keys"],listDeploymentBranchPolicies:["GET /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies"],listDeploymentStatuses:["GET /repos/{owner}/{repo}/deployments/{deployment_id}/statuses"],listDeployments:["GET /repos/{owner}/{repo}/deployments"],listForAuthenticatedUser:["GET /user/repos"],listForOrg:["GET /orgs/{org}/repos"],listForUser:["GET /users/{username}/repos"],listForks:["GET /repos/{owner}/{repo}/forks"],listInvitations:["GET /repos/{owner}/{repo}/invitations"],listInvitationsForAuthenticatedUser:["GET /user/repository_invitations"],listLanguages:["GET /repos/{owner}/{repo}/languages"],listPagesBuilds:["GET /repos/{owner}/{repo}/pages/builds"],listPublic:["GET /repositories"],listPullRequestsAssociatedWithCommit:["GET /repos/{owner}/{repo}/commits/{commit_sha}/pulls"],listReleaseAssets:["GET /repos/{owner}/{repo}/releases/{release_id}/assets"],listReleases:["GET /repos/{owner}/{repo}/releases"],listTags:["GET /repos/{owner}/{repo}/tags"],listTeams:["GET /repos/{owner}/{repo}/teams"],listWebhookDeliveries:["GET /repos/{owner}/{repo}/hooks/{hook_id}/deliveries"],listWebhooks:["GET /repos/{owner}/{repo}/hooks"],merge:["POST /repos/{owner}/{repo}/merges"],mergeUpstream:["POST /repos/{owner}/{repo}/merge-upstream"],pingWebhook:["POST /repos/{owner}/{repo}/hooks/{hook_id}/pings"],redeliverWebhookDelivery:["POST /repos/{owner}/{repo}/hooks/{hook_id}/deliveries/{delivery_id}/attempts"],removeAppAccessRestrictions:["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps",{},{mapToData:"apps"}],removeCollaborator:["DELETE /repos/{owner}/{repo}/collaborators/{username}"],removeStatusCheckContexts:["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts",{},{mapToData:"contexts"}],removeStatusCheckProtection:["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks"],removeTeamAccessRestrictions:["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams",{},{mapToData:"teams"}],removeUserAccessRestrictions:["DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users",{},{mapToData:"users"}],renameBranch:["POST /repos/{owner}/{repo}/branches/{branch}/rename"],replaceAllTopics:["PUT /repos/{owner}/{repo}/topics"],requestPagesBuild:["POST /repos/{owner}/{repo}/pages/builds"],setAdminBranchProtection:["POST /repos/{owner}/{repo}/branches/{branch}/protection/enforce_admins"],setAppAccessRestrictions:["PUT /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps",{},{mapToData:"apps"}],setStatusCheckContexts:["PUT /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts",{},{mapToData:"contexts"}],setTeamAccessRestrictions:["PUT /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams",{},{mapToData:"teams"}],setUserAccessRestrictions:["PUT /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users",{},{mapToData:"users"}],testPushWebhook:["POST /repos/{owner}/{repo}/hooks/{hook_id}/tests"],transfer:["POST /repos/{owner}/{repo}/transfer"],update:["PATCH /repos/{owner}/{repo}"],updateBranchProtection:["PUT /repos/{owner}/{repo}/branches/{branch}/protection"],updateCommitComment:["PATCH /repos/{owner}/{repo}/comments/{comment_id}"],updateDeploymentBranchPolicy:["PUT /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies/{branch_policy_id}"],updateInformationAboutPagesSite:["PUT /repos/{owner}/{repo}/pages"],updateInvitation:["PATCH /repos/{owner}/{repo}/invitations/{invitation_id}"],updateOrgRuleset:["PUT /orgs/{org}/rulesets/{ruleset_id}"],updatePullRequestReviewProtection:["PATCH /repos/{owner}/{repo}/branches/{branch}/protection/required_pull_request_reviews"],updateRelease:["PATCH /repos/{owner}/{repo}/releases/{release_id}"],updateReleaseAsset:["PATCH /repos/{owner}/{repo}/releases/assets/{asset_id}"],updateRepoRuleset:["PUT /repos/{owner}/{repo}/rulesets/{ruleset_id}"],updateStatusCheckPotection:["PATCH /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks",{},{renamed:["repos","updateStatusCheckProtection"]}],updateStatusCheckProtection:["PATCH /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks"],updateWebhook:["PATCH /repos/{owner}/{repo}/hooks/{hook_id}"],updateWebhookConfigForRepo:["PATCH /repos/{owner}/{repo}/hooks/{hook_id}/config"],uploadReleaseAsset:["POST /repos/{owner}/{repo}/releases/{release_id}/assets{?name,label}",{baseUrl:"https://uploads.github.com"}]},search:{code:["GET /search/code"],commits:["GET /search/commits"],issuesAndPullRequests:["GET /search/issues"],labels:["GET /search/labels"],repos:["GET /search/repositories"],topics:["GET /search/topics"],users:["GET /search/users"]},secretScanning:{createPushProtectionBypass:["POST /repos/{owner}/{repo}/secret-scanning/push-protection-bypasses"],getAlert:["GET /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}"],getScanHistory:["GET /repos/{owner}/{repo}/secret-scanning/scan-history"],listAlertsForEnterprise:["GET /enterprises/{enterprise}/secret-scanning/alerts"],listAlertsForOrg:["GET /orgs/{org}/secret-scanning/alerts"],listAlertsForRepo:["GET /repos/{owner}/{repo}/secret-scanning/alerts"],listLocationsForAlert:["GET /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}/locations"],updateAlert:["PATCH /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}"]},securityAdvisories:{createFork:["POST /repos/{owner}/{repo}/security-advisories/{ghsa_id}/forks"],createPrivateVulnerabilityReport:["POST /repos/{owner}/{repo}/security-advisories/reports"],createRepositoryAdvisory:["POST /repos/{owner}/{repo}/security-advisories"],createRepositoryAdvisoryCveRequest:["POST /repos/{owner}/{repo}/security-advisories/{ghsa_id}/cve"],getGlobalAdvisory:["GET /advisories/{ghsa_id}"],getRepositoryAdvisory:["GET /repos/{owner}/{repo}/security-advisories/{ghsa_id}"],listGlobalAdvisories:["GET /advisories"],listOrgRepositoryAdvisories:["GET /orgs/{org}/security-advisories"],listRepositoryAdvisories:["GET /repos/{owner}/{repo}/security-advisories"],updateRepositoryAdvisory:["PATCH /repos/{owner}/{repo}/security-advisories/{ghsa_id}"]},teams:{addOrUpdateMembershipForUserInOrg:["PUT /orgs/{org}/teams/{team_slug}/memberships/{username}"],addOrUpdateProjectPermissionsInOrg:["PUT /orgs/{org}/teams/{team_slug}/projects/{project_id}"],addOrUpdateRepoPermissionsInOrg:["PUT /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}"],checkPermissionsForProjectInOrg:["GET /orgs/{org}/teams/{team_slug}/projects/{project_id}"],checkPermissionsForRepoInOrg:["GET /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}"],create:["POST /orgs/{org}/teams"],createDiscussionCommentInOrg:["POST /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments"],createDiscussionInOrg:["POST /orgs/{org}/teams/{team_slug}/discussions"],deleteDiscussionCommentInOrg:["DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}"],deleteDiscussionInOrg:["DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}"],deleteInOrg:["DELETE /orgs/{org}/teams/{team_slug}"],getByName:["GET /orgs/{org}/teams/{team_slug}"],getDiscussionCommentInOrg:["GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}"],getDiscussionInOrg:["GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}"],getMembershipForUserInOrg:["GET /orgs/{org}/teams/{team_slug}/memberships/{username}"],list:["GET /orgs/{org}/teams"],listChildInOrg:["GET /orgs/{org}/teams/{team_slug}/teams"],listDiscussionCommentsInOrg:["GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments"],listDiscussionsInOrg:["GET /orgs/{org}/teams/{team_slug}/discussions"],listForAuthenticatedUser:["GET /user/teams"],listMembersInOrg:["GET /orgs/{org}/teams/{team_slug}/members"],listPendingInvitationsInOrg:["GET /orgs/{org}/teams/{team_slug}/invitations"],listProjectsInOrg:["GET /orgs/{org}/teams/{team_slug}/projects"],listReposInOrg:["GET /orgs/{org}/teams/{team_slug}/repos"],removeMembershipForUserInOrg:["DELETE /orgs/{org}/teams/{team_slug}/memberships/{username}"],removeProjectInOrg:["DELETE /orgs/{org}/teams/{team_slug}/projects/{project_id}"],removeRepoInOrg:["DELETE /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}"],updateDiscussionCommentInOrg:["PATCH /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}"],updateDiscussionInOrg:["PATCH /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}"],updateInOrg:["PATCH /orgs/{org}/teams/{team_slug}"]},users:{addEmailForAuthenticated:["POST /user/emails",{},{renamed:["users","addEmailForAuthenticatedUser"]}],addEmailForAuthenticatedUser:["POST /user/emails"],addSocialAccountForAuthenticatedUser:["POST /user/social_accounts"],block:["PUT /user/blocks/{username}"],checkBlocked:["GET /user/blocks/{username}"],checkFollowingForUser:["GET /users/{username}/following/{target_user}"],checkPersonIsFollowedByAuthenticated:["GET /user/following/{username}"],createGpgKeyForAuthenticated:["POST /user/gpg_keys",{},{renamed:["users","createGpgKeyForAuthenticatedUser"]}],createGpgKeyForAuthenticatedUser:["POST /user/gpg_keys"],createPublicSshKeyForAuthenticated:["POST /user/keys",{},{renamed:["users","createPublicSshKeyForAuthenticatedUser"]}],createPublicSshKeyForAuthenticatedUser:["POST /user/keys"],createSshSigningKeyForAuthenticatedUser:["POST /user/ssh_signing_keys"],deleteEmailForAuthenticated:["DELETE /user/emails",{},{renamed:["users","deleteEmailForAuthenticatedUser"]}],deleteEmailForAuthenticatedUser:["DELETE /user/emails"],deleteGpgKeyForAuthenticated:["DELETE /user/gpg_keys/{gpg_key_id}",{},{renamed:["users","deleteGpgKeyForAuthenticatedUser"]}],deleteGpgKeyForAuthenticatedUser:["DELETE /user/gpg_keys/{gpg_key_id}"],deletePublicSshKeyForAuthenticated:["DELETE /user/keys/{key_id}",{},{renamed:["users","deletePublicSshKeyForAuthenticatedUser"]}],deletePublicSshKeyForAuthenticatedUser:["DELETE /user/keys/{key_id}"],deleteSocialAccountForAuthenticatedUser:["DELETE /user/social_accounts"],deleteSshSigningKeyForAuthenticatedUser:["DELETE /user/ssh_signing_keys/{ssh_signing_key_id}"],follow:["PUT /user/following/{username}"],getAuthenticated:["GET /user"],getById:["GET /user/{account_id}"],getByUsername:["GET /users/{username}"],getContextForUser:["GET /users/{username}/hovercard"],getGpgKeyForAuthenticated:["GET /user/gpg_keys/{gpg_key_id}",{},{renamed:["users","getGpgKeyForAuthenticatedUser"]}],getGpgKeyForAuthenticatedUser:["GET /user/gpg_keys/{gpg_key_id}"],getPublicSshKeyForAuthenticated:["GET /user/keys/{key_id}",{},{renamed:["users","getPublicSshKeyForAuthenticatedUser"]}],getPublicSshKeyForAuthenticatedUser:["GET /user/keys/{key_id}"],getSshSigningKeyForAuthenticatedUser:["GET /user/ssh_signing_keys/{ssh_signing_key_id}"],list:["GET /users"],listAttestations:["GET /users/{username}/attestations/{subject_digest}"],listBlockedByAuthenticated:["GET /user/blocks",{},{renamed:["users","listBlockedByAuthenticatedUser"]}],listBlockedByAuthenticatedUser:["GET /user/blocks"],listEmailsForAuthenticated:["GET /user/emails",{},{renamed:["users","listEmailsForAuthenticatedUser"]}],listEmailsForAuthenticatedUser:["GET /user/emails"],listFollowedByAuthenticated:["GET /user/following",{},{renamed:["users","listFollowedByAuthenticatedUser"]}],listFollowedByAuthenticatedUser:["GET /user/following"],listFollowersForAuthenticatedUser:["GET /user/followers"],listFollowersForUser:["GET /users/{username}/followers"],listFollowingForUser:["GET /users/{username}/following"],listGpgKeysForAuthenticated:["GET /user/gpg_keys",{},{renamed:["users","listGpgKeysForAuthenticatedUser"]}],listGpgKeysForAuthenticatedUser:["GET /user/gpg_keys"],listGpgKeysForUser:["GET /users/{username}/gpg_keys"],listPublicEmailsForAuthenticated:["GET /user/public_emails",{},{renamed:["users","listPublicEmailsForAuthenticatedUser"]}],listPublicEmailsForAuthenticatedUser:["GET /user/public_emails"],listPublicKeysForUser:["GET /users/{username}/keys"],listPublicSshKeysForAuthenticated:["GET /user/keys",{},{renamed:["users","listPublicSshKeysForAuthenticatedUser"]}],listPublicSshKeysForAuthenticatedUser:["GET /user/keys"],listSocialAccountsForAuthenticatedUser:["GET /user/social_accounts"],listSocialAccountsForUser:["GET /users/{username}/social_accounts"],listSshSigningKeysForAuthenticatedUser:["GET /user/ssh_signing_keys"],listSshSigningKeysForUser:["GET /users/{username}/ssh_signing_keys"],setPrimaryEmailVisibilityForAuthenticated:["PATCH /user/email/visibility",{},{renamed:["users","setPrimaryEmailVisibilityForAuthenticatedUser"]}],setPrimaryEmailVisibilityForAuthenticatedUser:["PATCH /user/email/visibility"],unblock:["DELETE /user/blocks/{username}"],unfollow:["DELETE /user/following/{username}"],updateAuthenticated:["PATCH /user"]}}))for(const[t,o]of Object.entries(r)){const[r,s,n]=o,[i,a]=r.split(/ /),c=Object.assign({method:i,url:a},s);X.has(e)||X.set(e,new Map),X.get(e).set(t,{scope:e,methodName:t,endpointDefaults:c,decorations:n})}const ee={has:({scope:e},r)=>X.get(e).has(r),getOwnPropertyDescriptor(e,r){return{value:this.get(e,r),configurable:!0,writable:!0,enumerable:!0}},defineProperty:(e,r,t)=>(Object.defineProperty(e.cache,r,t),!0),deleteProperty:(e,r)=>(delete e.cache[r],!0),ownKeys:({scope:e})=>[...X.get(e).keys()],set:(e,r,t)=>e.cache[r]=t,get({octokit:e,scope:r,cache:t},o){if(t[o])return t[o];const s=X.get(r).get(o);if(!s)return;const{endpointDefaults:n,decorations:i}=s;return t[o]=i?function(e,r,t,o,s){const n=e.request.defaults(o);return Object.assign((function(...o){let i=n.endpoint.merge(...o);if(s.mapToData)return i=Object.assign({},i,{data:i[s.mapToData],[s.mapToData]:void 0}),n(i);if(s.renamed){const[o,n]=s.renamed;e.log.warn(`octokit.${r}.${t}() has been renamed to octokit.${o}.${n}()`)}if(s.deprecated&&e.log.warn(s.deprecated),s.renamedParameters){const i=n.endpoint.merge(...o);for(const[o,n]of Object.entries(s.renamedParameters))o in i&&(e.log.warn(`"${o}" parameter is deprecated for "octokit.${r}.${t}()". Use "${n}" instead`),n in i||(i[n]=i[o]),delete i[o]);return n(i)}return n(...o)}),n)}(e,r,o,n,i):e.request.defaults(n),t[o]}};function re(e){const r=function(e){const r={};for(const t of X.keys())r[t]=new Proxy({octokit:e,scope:t,cache:{}},ee);return r}(e);return{...r,rest:r}}re.VERSION="13.3.1";const te=class{static VERSION=W;static defaults(e){return class extends(this){constructor(...r){const t=r[0]||{};super("function"!=typeof e?Object.assign({},e,t,t.userAgent&&e.userAgent?{userAgent:`${t.userAgent} ${e.userAgent}`}:null):e(t))}}}static plugins=[];static plugin(...e){const r=this.plugins;return class extends(this){static plugins=r.concat(e.filter((e=>!r.includes(e))))}}constructor(e={}){const r=new p,t={baseUrl:F.endpoint.DEFAULTS.baseUrl,headers:{},request:Object.assign({},e.request,{hook:r.bind(null,"request")}),mediaType:{previews:[],format:""}};var o;if(t.headers["user-agent"]=e.userAgent?`${e.userAgent} ${K}`:K,e.baseUrl&&(t.baseUrl=e.baseUrl),e.previews&&(t.mediaType.previews=e.previews),e.timeZone&&(t.headers["time-zone"]=e.timeZone),this.request=F.defaults(t),this.graphql=(o=this.request,I(o,{method:"POST",url:"/graphql"})).defaults(t),this.log=Object.assign({debug:H,info:H,warn:M,error:V},e.log),this.hook=r,e.authStrategy){const{authStrategy:t,...o}=e,s=t(Object.assign({request:this.request,log:this.log,octokit:this,octokitOptions:o},e.auth));r.wrap("request",s.hook),this.auth=s}else if(e.auth){const t=N(e.auth);r.wrap("request",t.hook),this.auth=t}else this.auth=async()=>({type:"unauthenticated"});const s=this.constructor;for(let r=0;r<s.plugins.length;++r)Object.assign(this,s.plugins[r](this,e))}request;graphql;log;hook;auth}.plugin(z,re,Q).defaults({userAgent:"octokit-rest.js/21.1.1"})}},r={};function t(o){var s=r[o];if(void 0!==s)return s.exports;var n=r[o]={exports:{}};return e[o].call(n.exports,n,n.exports,t),n.exports}t.d=(e,r)=>{for(var o in r)t.o(r,o)&&!t.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:r[o]})},t.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r),t.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o=t(670),s=exports;for(var n in o)s[n]=o[n];o.__esModule&&Object.defineProperty(s,"__esModule",{value:!0})})();
//# sourceMappingURL=http://go/sourcemap/sourcemaps/3af362bc7c6ffdde67ee75328bc9be679d6f3a40/extensions/configuration-editing/dist/configurationEditingMain.js.map