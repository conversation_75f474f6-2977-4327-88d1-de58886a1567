(()=>{var e={366:function(e,n,t){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,n,t,r){void 0===r&&(r=t);var o=Object.getOwnPropertyDescriptor(n,t);o&&!("get"in o?!n.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return n[t]}}),Object.defineProperty(e,r,o)}:function(e,n,t,r){void 0===r&&(r=t),e[r]=n[t]}),o=this&&this.__setModuleDefault||(Object.create?function(e,n){Object.defineProperty(e,"default",{enumerable:!0,value:n})}:function(e,n){e.default=n}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var n={};if(null!=e)for(var t in e)"default"!==t&&Object.prototype.hasOwnProperty.call(e,t)&&r(n,e,t);return o(n,e),n};Object.defineProperty(n,"__esModule",{value:!0}),n.ControlProvider=void 0;const _=i(t(398)),d=t(887);class s{constructor(){this.tokenizer=new d.Tokenizer,this.providerRegistration=_.workspace.registerControlProvider(s.id,this)}getRegistrationSource(){return s.id}dispose(){this.providerRegistration.dispose()}async getFullDiff(e,n){}async getDataframeSummary(e){}async tokenizeBPE(e,n){return e.map((e=>this.tokenizer.tokenize(e,n)))}async appendCppTelem(){}streamCpp(){}flushCpp(){return{type:"success",buffer:[],rangeToReplaceOneIndexed:void 0,cursorPredictionTarget:void 0,doneEdit:void 0}}cancelCpp(){}async getCppReport(){}}n.ControlProvider=s,s.id="cursor-tokenize"},501:(e,n,t)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.deactivate=n.activate=void 0;const r=t(366),o=[];n.activate=async function(e){const n=new r.ControlProvider;o.push((async()=>n.dispose()))},n.deactivate=async function(){for(const e of o)await e()}},887:(e,n,t)=>{"use strict";e=t.nmd(e),Object.defineProperty(n,"__esModule",{value:!0}),n.Tokenizer=n.getTokenizerFromName=n.GPT2_TOKENIZER=n.P50_TOKENIZER=n.CLK_TOKENIZER=void 0;const r=t(509);function o(e){switch(e){case"gpt2":return n.GPT2_TOKENIZER;case"p50k_base":return n.P50_TOKENIZER;case"cl100k_base":return n.CLK_TOKENIZER;case"r50k_base":throw new Error("r50k_base not supported")}}n.CLK_TOKENIZER=(0,r.get_encoding)("cl100k_base"),n.P50_TOKENIZER=(0,r.get_encoding)("p50k_base"),n.GPT2_TOKENIZER=(0,r.get_encoding)("gpt2"),n.getTokenizerFromName=o;class i{tokenize(e,n){const t=o(n),r=t.encode(e,"all"),i=[];for(const e of r){const n=new Uint32Array([e]);i.push({text:(new TextDecoder).decode(t.decode(n)),token:e})}return i}}if(n.Tokenizer=i,t.c[t.s]===e){const e="Where is the stripe code that handle payments?",n=(new i).tokenize(e,"gpt2");console.log(n)}},398:e=>{"use strict";e.exports=require("vscode")},896:e=>{"use strict";e.exports=require("fs")},928:e=>{"use strict";e.exports=require("path")},509:(e,n,t)=>{const r=t(529);let o={};o["./tiktoken_bg.js"]=r;const i=t(928),_=t(896),d=__dirname.split(i.sep).reduce(((e,n,t,r)=>{const o=r.slice(0,t+1).join(i.sep)+i.sep;return o.includes("node_modules"+i.sep)||e.unshift(i.join(o,"node_modules","@dqbd","tiktoken","","./tiktoken_bg.wasm")),e}),[]);d.unshift(i.join(__dirname,"./tiktoken_bg.wasm"));let s=null;for(const e of d)try{s=_.readFileSync(e);break}catch{}if(null==s)throw new Error("Missing tiktoken_bg.wasm");const a=new WebAssembly.Module(s),c=new WebAssembly.Instance(a,o);r.__wbg_set_wasm(c.exports),n.get_encoding=r.get_encoding,n.encoding_for_model=r.encoding_for_model,n.Tiktoken=r.Tiktoken},529:(e,n,t)=>{let r;(e=t.nmd(e)).exports.__wbg_set_wasm=function(e){r=e};const o=new Array(128).fill(void 0);function i(e){return o[e]}o.push(void 0,null,!0,!1);let _=o.length;function d(e){const n=i(e);return function(e){e<132||(o[e]=_,_=e)}(e),n}let s=0,a=null;function c(){return null!==a&&0!==a.byteLength||(a=new Uint8Array(r.memory.buffer)),a}let u=new("undefined"==typeof TextEncoder?(0,e.require)("util").TextEncoder:TextEncoder)("utf-8");const l="function"==typeof u.encodeInto?function(e,n){return u.encodeInto(e,n)}:function(e,n){const t=u.encode(e);return n.set(t),{read:e.length,written:t.length}};function p(e,n,t){if(void 0===t){const t=u.encode(e),r=n(t.length);return c().subarray(r,r+t.length).set(t),s=t.length,r}let r=e.length,o=n(r);const i=c();let _=0;for(;_<r;_++){const n=e.charCodeAt(_);if(n>127)break;i[o+_]=n}if(_!==r){0!==_&&(e=e.slice(_)),o=t(o,r,r=_+3*e.length);const n=c().subarray(o+_,o+r);_+=l(e,n).written}return s=_,o}let b=null;function f(){return null!==b&&0!==b.byteLength||(b=new Int32Array(r.memory.buffer)),b}let g=new("undefined"==typeof TextDecoder?(0,e.require)("util").TextDecoder:TextDecoder)("utf-8",{ignoreBOM:!0,fatal:!0});function w(e,n){return g.decode(c().subarray(e,e+n))}function k(e){_===o.length&&o.push(o.length+1);const n=_;return _=o[n],o[n]=e,n}g.decode();let y=null;function h(){return null!==y&&0!==y.byteLength||(y=new Uint32Array(r.memory.buffer)),y}function x(e,n){return h().subarray(e/4,e/4+n)}function v(e,n){return c().subarray(e/1,e/1+n)}function m(e,n){try{return e.apply(this,n)}catch(e){r.__wbindgen_export_3(k(e))}}e.exports.get_encoding=function(e,n){if(null==r)throw new Error("@dqbd/tiktoken: WASM binary has not been propery initialized.");try{const i=r.__wbindgen_add_to_stack_pointer(-16),_=p(e,r.__wbindgen_export_0,r.__wbindgen_export_1),a=s;r.get_encoding(i,_,a,k(n));var t=f()[i/4+0],o=f()[i/4+1];if(f()[i/4+2])throw d(o);return E.__wrap(t)}finally{r.__wbindgen_add_to_stack_pointer(16)}},e.exports.encoding_for_model=function(e,n){if(null==r)throw new Error("@dqbd/tiktoken: WASM binary has not been propery initialized.");try{const i=r.__wbindgen_add_to_stack_pointer(-16),_=p(e,r.__wbindgen_export_0,r.__wbindgen_export_1),a=s;r.encoding_for_model(i,_,a,k(n));var t=f()[i/4+0],o=f()[i/4+1];if(f()[i/4+2])throw d(o);return E.__wrap(t)}finally{r.__wbindgen_add_to_stack_pointer(16)}};class E{constructor(e,n,t){if(null==r)throw new Error("@dqbd/tiktoken: WASM binary has not been propery initialized.");const o=p(e,r.__wbindgen_export_0,r.__wbindgen_export_1),i=s,_=p(t,r.__wbindgen_export_0,r.__wbindgen_export_1),d=s,a=r.tiktoken_new(o,i,k(n),_,d);return E.__wrap(a)}get name(){try{const t=r.__wbindgen_add_to_stack_pointer(-16);r.tiktoken_name(t,this.ptr);var e=f()[t/4+0],n=f()[t/4+1];let o;return 0!==e&&(o=w(e,n).slice(),r.__wbindgen_export_2(e,1*n)),o}finally{r.__wbindgen_add_to_stack_pointer(16)}}static __wrap(e){const n=Object.create(E.prototype);return n.ptr=e,n}__destroy_into_raw(){const e=this.ptr;return this.ptr=0,e}free(){if(null==r)throw new Error("@dqbd/tiktoken: WASM binary has not been propery initialized.");const e=this.__destroy_into_raw();r.__wbg_tiktoken_free(e)}encode(e,n,t){if(null==r)throw new Error("@dqbd/tiktoken: WASM binary has not been propery initialized.");try{const c=r.__wbindgen_add_to_stack_pointer(-16),u=p(e,r.__wbindgen_export_0,r.__wbindgen_export_1),l=s;r.tiktoken_encode(c,this.ptr,u,l,k(n),k(t));var o=f()[c/4+0],i=f()[c/4+1],_=f()[c/4+2];if(f()[c/4+3])throw d(_);var a=x(o,i).slice();return r.__wbindgen_export_2(o,4*i),a}finally{r.__wbindgen_add_to_stack_pointer(16)}}encode_ordinary(e){if(null==r)throw new Error("@dqbd/tiktoken: WASM binary has not been propery initialized.");try{const i=r.__wbindgen_add_to_stack_pointer(-16),_=p(e,r.__wbindgen_export_0,r.__wbindgen_export_1),d=s;r.tiktoken_encode_ordinary(i,this.ptr,_,d);var n=f()[i/4+0],t=f()[i/4+1],o=x(n,t).slice();return r.__wbindgen_export_2(n,4*t),o}finally{r.__wbindgen_add_to_stack_pointer(16)}}encode_with_unstable(e,n,t){if(null==r)throw new Error("@dqbd/tiktoken: WASM binary has not been propery initialized.");try{const _=r.__wbindgen_add_to_stack_pointer(-16),a=p(e,r.__wbindgen_export_0,r.__wbindgen_export_1),c=s;r.tiktoken_encode_with_unstable(_,this.ptr,a,c,k(n),k(t));var o=f()[_/4+0],i=f()[_/4+1];if(f()[_/4+2])throw d(i);return d(o)}finally{r.__wbindgen_add_to_stack_pointer(16)}}encode_single_token(e){if(null==r)throw new Error("@dqbd/tiktoken: WASM binary has not been propery initialized.");const n=function(e,n){const t=n(1*e.length);return c().set(e,t/1),s=e.length,t}(e,r.__wbindgen_export_0),t=s;return r.tiktoken_encode_single_token(this.ptr,n,t)>>>0}decode(e){if(null==r)throw new Error("@dqbd/tiktoken: WASM binary has not been propery initialized.");try{const i=r.__wbindgen_add_to_stack_pointer(-16),_=function(e,n){const t=n(4*e.length);return h().set(e,t/4),s=e.length,t}(e,r.__wbindgen_export_0),d=s;r.tiktoken_decode(i,this.ptr,_,d);var n=f()[i/4+0],t=f()[i/4+1],o=v(n,t).slice();return r.__wbindgen_export_2(n,1*t),o}finally{r.__wbindgen_add_to_stack_pointer(16)}}decode_single_token_bytes(e){if(null==r)throw new Error("@dqbd/tiktoken: WASM binary has not been propery initialized.");try{const i=r.__wbindgen_add_to_stack_pointer(-16);r.tiktoken_decode_single_token_bytes(i,this.ptr,e);var n=f()[i/4+0],t=f()[i/4+1],o=v(n,t).slice();return r.__wbindgen_export_2(n,1*t),o}finally{r.__wbindgen_add_to_stack_pointer(16)}}token_byte_values(){if(null==r)throw new Error("@dqbd/tiktoken: WASM binary has not been propery initialized.");return d(r.tiktoken_token_byte_values(this.ptr))}}e.exports.Tiktoken=E,e.exports.__wbindgen_object_drop_ref=function(e){d(e)},e.exports.__wbindgen_is_undefined=function(e){return void 0===i(e)},e.exports.__wbg_stringify_029a979dfb73aa17=function(){return m((function(e){return k(JSON.stringify(i(e)))}),arguments)},e.exports.__wbindgen_string_get=function(e,n){if(null==r)throw new Error("@dqbd/tiktoken: WASM binary has not been propery initialized.");const t=i(n),o="string"==typeof t?t:void 0;var _=null==o?0:p(o,r.__wbindgen_export_0,r.__wbindgen_export_1),d=s;f()[e/4+1]=d,f()[e/4+0]=_},e.exports.__wbindgen_error_new=function(e,n){return k(new Error(w(e,n)))},e.exports.__wbg_parse_3ac95b51fc312db8=function(){return m((function(e,n){return k(JSON.parse(w(e,n)))}),arguments)},e.exports.__wbindgen_throw=function(e,n){throw new Error(w(e,n))}}},n={};function t(r){var o=n[r];if(void 0!==o)return o.exports;var i=n[r]={id:r,loaded:!1,exports:{}};return e[r].call(i.exports,i,i.exports,t),i.loaded=!0,i.exports}t.c=n,t.nmd=e=>(e.paths=[],e.children||(e.children=[]),e);var r=t(t.s=501),o=exports;for(var i in r)o[i]=r[i];r.__esModule&&Object.defineProperty(o,"__esModule",{value:!0})})();
//# sourceMappingURL=http://go/sourcemap/sourcemaps/3af362bc7c6ffdde67ee75328bc9be679d6f3a40/extensions/cursor-tokenize/dist/main.js.map