{"$schema": "https://json-schema.org/draft/2019-09/schema", "description": "Defines a dev environment", "allowComments": true, "allowTrailingCommas": false, "definitions": {"common": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the environment."}, "user": {"type": "string", "description": "The user to run the environment as."}, "install": {"type": "string", "description": "The install command to run when the environment is started."}, "start": {"type": "string", "description": "The start command to run when the environment is started."}, "repositoryDependencies": {"type": "array", "description": "Repositories that are required for the environment to work, and need to be included in the GitHub access token that is generated for the environment.", "items": {"type": "string", "description": "The URL of the dependent repository, e.g. `github.com/org/repo`."}}, "ports": {"type": "array", "description": "Ports to expose from the container. Similar to devcontainers port forwarding.", "items": {"type": "object", "required": ["port"], "properties": {"name": {"type": "string", "description": "A descriptive name for the port (e.g., 'web server', 'api')."}, "port": {"type": "integer", "minimum": 1, "maximum": 65535, "description": "The port number inside the container to expose."}}}}, "terminals": {"type": "array", "description": "The terminals to run when the environment is started.", "items": {"oneOf": [{"type": "array", "items": {"type": "object", "required": ["command"], "properties": {"name": {"type": "string", "description": "The name of the terminal."}, "command": {"type": "string", "description": "The command to run in the terminal."}, "description": {"type": "string", "description": "A description of what the terminal does. This is displayed to the agent."}}}}, {"type": "object", "required": ["command"], "properties": {"name": {"type": "string", "description": "The name of the terminal."}, "command": {"type": "string", "description": "The command to run in the terminal."}, "description": {"type": "string", "description": "A description of what the terminal does. This is displayed to the agent."}}}]}}}}, "container": {"type": "object", "properties": {"build": {"type": "object", "description": "Docker build-related options.", "properties": {"dockerfile": {"type": "string", "description": "The location of the Dockerfile that defines the contents of the container. The path is relative to the folder containing the `environment.json` file."}, "context": {"type": "string", "description": "The location of the context folder for building the Dock<PERSON> image. The path is relative to the folder containing the `environment.json` file."}}, "required": ["dockerfile"], "unevaluatedProperties": false}, "snapshot": {"type": "string", "description": "A snapshot ID for the base environment."}, "agentCanUpdateSnapshot": {"type": "boolean", "description": "Whether the agent can update the snapshot."}}, "required": []}}, "allOf": [{"$ref": "#/definitions/container"}, {"$ref": "#/definitions/common"}], "unevaluatedProperties": false}