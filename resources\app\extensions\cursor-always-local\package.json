{"name": "cursor-always-local", "description": "Implements experimentation features for Cursor", "author": "Anysphere, Inc.", "publisher": "anysphere", "version": "0.0.1", "private": true, "repository": {"type": "git", "url": "https://github.com/anysphere/vscode"}, "extensionKind": ["ui"], "engines": {"vscode": "^1.43.0", "yarn": "please-use-npm"}, "activationEvents": ["onStartupFinished", "onResolveRemoteAuthority:background-composer"], "enabledApiProposals": ["cursor", "control", "externalUriOpener", "contribSourceControlInputBoxMenu", "resolvers"], "main": "./dist/main", "contributes": {"commands": [{"command": "rcp-server.showStatus", "title": "Show RCP Server Status"}, {"command": "rcp-server.restart", "title": "Restart RCP Server"}, {"command": "rcp-server.enable", "title": "Enable RCP Server"}, {"command": "rcp-server.disable", "title": "Disable RCP Server"}], "keybindings": [], "menus": {"scm/inputBox": [{"command": "cursor.generateGitCommitMessage", "when": "scmProvider == git"}]}, "jsonValidation": [{"fileMatch": ".cursor/environment.json", "url": "./schemas/environment.schema.json"}], "resourceLabelFormatters": [{"scheme": "vscode-remote", "authority": "background-composer+*", "formatting": {"label": "${path}", "separator": "/", "tildify": true, "workspaceSuffix": "background-agent"}}], "configuration": {"type": "object", "title": "Cursor Always Local"}}, "optionalDependencies": {"@vscode/windows-ca-certs": "^0.3.3"}}