{"name": "@anysphere/file-service", "version": "0.0.0-f71d571a", "main": "index.js", "types": "index.d.ts", "napi": {"name": "file_service", "triples": {"additional": ["aarch64-apple-darwin", "aarch64-pc-windows-msvc", "aarch64-unknown-linux-musl", "x86_64-unknown-linux-musl", "universal-apple-darwin", "aarch64-unknown-linux-gnu"]}}, "files": ["index.js", "index.d.ts"], "license": "UNLICENSED", "devDependencies": {"@napi-rs/cli": "^2.18.4", "ava": "^6.3.0"}, "ava": {"timeout": "3m"}, "engines": {"node": ">= 10"}, "scripts": {"artifacts": "napi artifacts", "build": "napi build --platform --release", "build:debug": "napi build --platform", "prepublishOnly": "napi prepublish -t npm", "test": "ava", "universal": "napi universal", "preversion": "git config --global user.name || git config --global user.name 'Your Name'; git config --global user.email || git config --global user.email '<EMAIL>'", "version": "napi version"}, "optionalDependencies": {"@anysphere/file-service-win32-x64-msvc": "0.0.0-f71d571a", "@anysphere/file-service-darwin-x64": "0.0.0-f71d571a", "@anysphere/file-service-linux-x64-gnu": "0.0.0-f71d571a", "@anysphere/file-service-darwin-arm64": "0.0.0-f71d571a", "@anysphere/file-service-win32-arm64-msvc": "0.0.0-f71d571a", "@anysphere/file-service-linux-arm64-musl": "0.0.0-f71d571a", "@anysphere/file-service-linux-x64-musl": "0.0.0-f71d571a", "@anysphere/file-service-darwin-universal": "0.0.0-f71d571a", "@anysphere/file-service-linux-arm64-gnu": "0.0.0-f71d571a"}}