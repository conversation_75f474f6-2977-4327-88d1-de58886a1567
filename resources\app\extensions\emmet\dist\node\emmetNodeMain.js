(()=>{var e={7545:(e,t,n)=>{"use strict";n.r(t),n.d(t,{Token:()=>h,any:()=>Me,backtick:()=>ne,comment:()=>W,createAtRule:()=>Ge,createProperty:()=>Qe,createRule:()=>We,default:()=>Ye,formatting:()=>De,ident:()=>E,interpolation:()=>L,keyword:()=>ze,lexer:()=>Xe,parseMediaExpression:()=>qe,parsePropertyName:()=>Be,parsePropertyValue:()=>Ue,parseSelector:()=>Fe,selector:()=>Ne,string:()=>F,url:()=>Ee,value:()=>Le,variable:()=>Pe,whitespace:()=>Q});var r=n(8769),o=n(6613);class i{constructor(e){this.type=e,this.children=[],this.parent=null}get firstChild(){return this.children[0]}get nextSibling(){const e=this.index();return-1!==e?this.parent.children[e+1]:null}get previousSibling(){const e=this.index();return-1!==e?this.parent.children[e-1]:null}index(){return this.parent?this.parent.children.indexOf(this):-1}add(e){return e&&(e.remove(),this.children.push(e),e.parent=this),this}remove(){if(this.parent){const e=this.index();-1!==e&&(this.parent.children.splice(e,1),this.parent=null)}return this}}class s extends i{constructor(){super("stylesheet"),this.comments=[]}get start(){const e=this.firstChild;return e&&e.start}get end(){const e=this.children[this.children.length-1];return e&&e.end}addComment(e){this.comments.push(e)}}function a(e){return function(e){let t;for(e=e.slice();t!==e.length;)t=e.length,c(e[0])&&e.shift(),c(d(e))&&e.pop();return e}(e)}function c(e){const t=e&&e.type;return"whitespace"===t||"comment"===t}function u(e,t){const n=e.pos;for(let r=0,o=t.length;r<o;r++)if(!e.eat(t.charCodeAt(r)))return e.pos=n,!1;return!0}function l(e,t){const n=e.pos;return!!e.eat(t)&&(e.start=n,!0)}function f(e,t){const n=e.pos;return!!e.eatWhile(t)&&(e.start=n,!0)}function d(e){return e[e.length-1]}function p(e){return e&&e.valueOf()}class h{constructor(e,t,n,r){this.stream=e,this.start=null!=n?n:e.start,this.end=null!=r?r:e.pos,this.type=t,this._props=null,this._value=null,this._items=null}get size(){return this._items?this._items.length:0}get items(){return this._items}clone(e,t){return new this.constructor(this.stream,this.type,null!=e?e:this.start,null!=t?t:this.end)}add(e){if(Array.isArray(e))for(let t=0,n=e.length;t<n;t++)this.add(e[t]);else e&&(this._items?this._items.push(e):this._items=[e]);return this}remove(e){if(this._items){const t=this._items.indexOf(e);-1!==t&&this._items.splice(t,1)}return this}item(e){const t=this.size;return this._items&&this._items[(t+e)%t]}limit(){return this.stream.limit(this.start,this.end)}slice(e,t){const n=this.clone(),r=this._items&&this._items.slice(e,t);return r&&r.length?(n.start=r[0].start,n.end=r[r.length-1].end,n.add(r)):r&&(n.start=n.end),n}property(e,t){return void 0!==t&&(this._props||(this._props={}),this._props[e]=t),this._props&&this._props[e]}toString(){return`${this.valueOf()} [${this.start}, ${this.end}] (${this.type})`}valueOf(){return null===this._value&&(this._value=this.stream.substring(this.start,this.end)),this._value}}const m=44,g=58,b=59,v=123,y=125,x=(new Map).set(m,"comma").set(g,"propertyDelimiter").set(b,"propertyTerminator").set(v,"ruleStart").set(y,"ruleEnd");var w=function(e,t){if(40===e.peek()){const r=e.pos;e.next();const o=[];let i,s=!1;for(;!e.eof()&&(n=e.peek())!==v&&n!==y&&!e.eat(41)&&(i=t(e),i);)T(i)&&(s=!0),o.push(i);return e.start=r,function(e,t,n){const r=new h(e,"arguments"),o=n?T:_;let i=[];for(let n,s=0,a=t.length;s<a;s++)n=t[s],o(n)?(r.add(k(e,i)||C(e,n.start)),i.length=0):i.push(n);return i.length&&r.add(k(e,i)),r}(e,o,s)}var n};function k(e,t){if((t=a(t)).length){const n=new h(e,"argument",t[0].start,d(t).end);for(let e=0;e<t.length;e++)n.add(t[e]);return n}}function C(e,t){const n=new h(e,"argument",t,t);return n.property("empty",!0),n}function _(e){return"comma"===e.property("type")}function T(e){return"propertyTerminator"===e.property("type")}const S=45,O=95;function E(e){return A(e)&&new h(e,"ident")}function A(e){const t=e.pos;return e.eat(S),e.eat(j)?(e.eatWhile(P),e.start=t,!0):(e.pos=t,!1)}function j(e){return e===O||e===S||(0,o.R5)(e)||e>=128}function P(e){return(0,o.Et)(e)||j(e)}function I(e,t,n,r,o){const i=e.pos;if(e.eat(n)){const n=r(e,i);if(n||o)return e.start=i,new h(e,t,i).add(n)}e.pos=i}const $=64;function R(e){return I(e,"at-keyword",$,E)}const M=35,N=64;function L(e,t){const n=e.pos;if(t=t||D,(e.eat(M)||e.eat(N))&&e.eat(v)){const r=new h(e,"interpolation",n);let o,i=1;for(;!e.eof();)if(e.eat(v))i++;else if(e.eat(y)){if(i--,!i)return r.end=e.pos,r}else{if(!(o=t(e)))break;r.add(o)}}e.pos=n}function z(e){const t=e.pos;return(e.eat(M)||e.eat(N))&&(0,o.Ji)(e,v,y)?(e.start=t,!0):(e.pos=t,!1)}function D(e){const t=e.pos;for(;!e.eof()&&e.peek()!==y;)B(e)||e.next();if(t!==e.pos)return new h(e,"expression",t)}function F(e){return B(e,!0)}function B(e,t){let n,r,i,s=e.peek();if((0,o.vG)(s)){e.start=e.pos,e.next();const o=s,a=e.pos;for(;!e.eof()&&(n=e.pos,!e.eat(o)&&!e.eat(U));)e.eat(92)?e.eat(U):t&&(i=L(e))&&(r?r.push(i):r=[i]),e.next();if(t){const t=new h(e,"string"),i=new h(e,"unquoted",a,n);return i.add(r),t.add(i),t.property("quote",o),t}return!0}return!1}function U(e){return 10===e||13===e}const q=42,V=47;var W=function(e){return function(e){if(G(e)){const t=new h(e,"comment");return t.property("type","single-line"),t}}(e)||function(e){if(J(e)){const t=new h(e,"comment");return t.property("type","multiline"),t}}(e)};function H(e){return G(e)||J(e)}function G(e){const t=e.pos;if(e.eat(V)&&e.eat(V)){for(e.start=t;!e.eof()&&10!==(n=e.next())&&13!==n;);return!0}var n;return e.pos=t,!1}function J(e){const t=e.pos;if(e.eat(V)&&e.eat(q)){for(;!(e.eof()||e.next()===q&&e.eat(V)););return e.start=t,!0}return e.pos=t,!1}function Q(e){return K(e)&&new h(e,"whitespace")}function K(e){return f(e,o.xC)}const X=91,Z=93;function Y(e){for(;!e.eof();)if(!K(e)&&!H(e))return!0}function ee(e){return 126===e||124===e||94===e||36===e||42===e||61===e}const te=96;function ne(e){if(re(e))return new h(e,"backtick")}function re(e){const t=e.pos;return!!(0,o.Ji)(e,te,te)&&(e.start=t,!0)}const oe=46,ie={43:"adjacentSibling",126:"generalSibling",62:"child",38:"nesting"};var se=function(e){if(43===(t=e.peek())||126===t||38===t||62===t){const t=e.pos,n=ie[e.next()],r=new h(e,"combinator",t);return r.property("type",n),r}var t};const ae=35;function ce(e){if(function(e){return f(e,ue)}(e))return new h(e,"hash-value")}function ue(e){return(0,o.Et)(e)||(0,o.R5)(e,65,70)||95===e||45===e||e>128}const le=35,fe=33,de=46;function pe(e){return 45===e||43===e}const he=33,me=42,ge=43,be=45,ve=47,ye=60,xe=61,we=62;function ke(e){return e===he||e===ye||e===xe||e===we}function Ce(e){return e===me||e===ge||e===be||e===ve||ke(e)}var _e=function(e){const t=e.pos;if(e.eatWhile(58)){const n=E(e);if(n)return new h(e,"pseudo",t).add(n)}e.pos=t},Te=function(e){return Se(e)&&new h(e,"unquoted")};function Se(e){return f(e,Oe)}function Oe(e){return!(isNaN(e)||(0,o.vG)(e)||(0,o.xC)(e)||40===e||41===e||92===e||function(e){return e>=0&&e<=8||11===e||e>=14&&e<=31||127===e}(e))}function Ee(e){const t=e.pos;if(u(e,"url(")){K(e);const n=F(e)||Te(e);return K(e),e.eat(41),new h(e,"url",t).add(n)}e.pos=t}function Ae(e){const t=e.pos;return u(e,"url(")?(K(e),B(e)||Se(e),K(e),e.eat(41),e.start=t,!0):(e.pos=t,!1)}const je=36;function Pe(e){return I(e,"variable",je,Ie)}function Ie(e){if(function(e){return f(e,$e)}(e))return new h(e,"name")}function $e(e){return e===je||P(e)}function Re(e){const t=Me(e)||w(e,Re);if(t&&"ident"===t.type){const n=w(e,Re);if(n)return new h(e,"function",t.start,n.end).add(t).add(n)}return t||function(e){e.start=e.pos;if(null!=e.next())return new h(e,"unknown")}(e)}function Me(e){return De(e)||Ee(e)||Ne(e)||Le(e)||function(e){if((t=e.peek())===m||t===g||t===b||t===v||t===y){const t=e.pos,n=x.get(e.next()),r=new h(e,"separator",t);return r.property("type",n),r}var t}(e)}function Ne(e){return L(e)||ne(e)||E(e)||R(e)||function(e){return I(e,"class",oe,E)}(e)||function(e){return I(e,"id",le,E)}(e)||_e(e)||function(e){const t=e.pos;if(e.eat(X)){Y(e);const n=E(e);Y(e);const r=function(e){return f(e,ee)&&new h(e,"operator")}(e);Y(e);const o=F(e)||E(e);return Y(e),e.eat(Z),new h(e,"attribute",t).add(n).add(r).add(o)}}(e)||se(e)}function Le(e){return Ee(e)||F(e)||L(e)||ne(e)||function(e){if(function(e){const t=e.pos;if(e.eat(pe),e.eatWhile(o.Et)){e.start=t;const n=e.pos;return e.eat(de)&&e.eatWhile(o.Et)||(e.pos=n),!0}return e.eat(de)&&e.eatWhile(o.Et)?(e.start=t,!0):(e.pos=t,!1)}(e)){const t=e.start,n=new h(e,"value"),r=function(e){return A(e)||function(e){return l(e,37)}(e)}(e)?new h(e,"unit"):null;return new h(e,"number",t).add(n).add(r)}}(e)||function(e){return I(e,"hash",ae,ce,!0)}(e)||ze(e)||function(e){return I(e,"important",fe,E)}(e)||function(e){return function(e){return l(e,ke)?(e.eatWhile(xe),!0):!!l(e,Ce)}(e)&&new h(e,"operator")}(e)}function ze(e){return ne(e)||Pe(e)||R(e)||E(e)}function De(e){return W(e)||Q(e)}function Fe(e){return Ve(e,"selector")}function Be(e){const t="string"==typeof e?new r.A(e):e,n=[];for(;!t.eof();)n.push(Re(t));let o;if(1===n.length)o=n[0];else{o=new h(t,"property-name",t.start,t.end);for(let e=0,t=n.length;e<t;e++)o.add(n[e])}return o}function Ue(e){return Ve(e)}function qe(e){return Ve(e)}function Ve(e,t){t=t||"item";const n="string"==typeof e?new r.A(e):e,o=[],i=[],s=()=>{const e=a(i);if(e.length){const r=new h(n,t,e[0].start,d(e).end);for(let t=0;t<e.length;t++)r.add(e[t]);o.push(r)}i.length=0};let c;for(;!n.eof();)if(n.eat(44))s();else{if(!(c=Re(n)))throw n.error("Unexpected character");"comment"!==c.type&&i.push(c)}return s(),o}function We(e,t,n,r){if(!t.length)return null;const o=t[0];return o.end=d(t).end,new He(e,o,n,r)}class He extends i{constructor(e,t,n,r){super("rule"),this.stream=e,this.selectorToken=t,this.contentStartToken=n,this.contentEndToken=r||n,this._parsedSelector=null}get selector(){return p(this.selectorToken)}get parsedSelector(){return this._parsedSelector||(this._parsedSelector=Fe(this.selectorToken.limit())),this._parsedSelector}get start(){return this.selectorToken&&this.selectorToken.start}get end(){const e=this.contentEndToken||this.contentStartToken||this.nameToken;return e&&e.end}}function Ge(e,t,n,r){if(!t.length)return null;let o,i=0;const s=t[i++];return i<t.length?(o=t[i++],o.type="expression",o.end=d(t).end):o=new h(e,"expression",s.end,s.end),new Je(e,s,o,n,r)}class Je extends i{constructor(e,t,n,r,o){super("at-rule"),this.stream=e,this.nameToken=t,this.expressionToken=n,this.contentStartToken=r,this.contentEndToken=o||r,this._parsedExpression=null}get name(){return p(this.nameToken&&this.nameToken.item(0))}get expression(){return p(this.expressionToken)}get parsedExpression(){return this._parsedExpression||(this._parsedExpression=qe(this.expressionToken.limit())),this._parsedExpression}get start(){return this.nameToken&&this.nameToken.start}get end(){const e=this.contentEndToken||this.contentStartToken||this.nameToken;return e&&e.end}}function Qe(e,t,n){if(!t.length)return null;let r,o,i=0;const s=t[i++];return i<t.length&&(o=t[i++],o.type="value",o.end=d(t).end),s&&o&&(r=new h(e,"separator",s.end,o.start)),new Ke(e,s,o,r,n)}class Ke extends i{constructor(e,t,n,r,o){super("property"),this.stream=e,this.nameToken=t,this.valueToken=n,this._parsedName=null,this._parsedValue=null,this.separatorToken=r,this.terminatorToken=o}get name(){return p(this.nameToken)}get parsedName(){return this._parsedName||(this._parsedName=Be(this.nameToken.limit())),this._parsedName}get value(){return p(this.valueToken)}get parsedValue(){return this._parsedValue||(this._parsedValue=Ue(this.valueToken.limit())),this._parsedValue}get separator(){return p(this.separatorToken)}get terminator(){return p(this.terminatorToken)}get start(){const e=this.nameToken||this.separatorToken||this.valueToken||this.terminatorToken;return e&&e.start}get end(){const e=this.terminatorToken||this.valueToken||this.separatorToken||this.nameToken;return e&&e.end}}function Xe(e,t){t=t||Re;const n="string"==typeof e?new r.A(e):e,o=[];let i;for(;!n.eof()&&(i=t(n));)o.push(i);return o}function Ze(e,t){if(e.eat(40)){let n,r=1;for(;!e.eof();)if(e.eat(41)){if(r--,!r)break}else if(e.eat(40))r++;else{if(Ae(e)||B(e))continue;if(n=W(e)){t.addComment(n);continue}e.next()}return!0}return!1}const Ye=function(e){const t="string"==typeof e?new r.A(e):e,n=new s;let o,i,a,c=n,u=[];const l=()=>{i&&(u.push(i),i=null)};for(;!t.eof();)if(!K(t))if(a=W(t))n.addComment(a);else if(t.start=t.pos,t.eatWhile(58))u.length||(i?l():i=new h(t,"preparse"));else if(t.eat(59))l(),c.add(Qe(t,u,new h(t,"termintator"))),u.length=0;else if(t.eat(123))l(),u.length>0&&(o="at-keyword"===u[0].type?Ge(t,u,new h(t,"body-start")):We(t,u,new h(t,"body-start")),c.add(o),c=o,u.length=0);else if(t.eat(125))l(),c.add(Qe(t,u)),"stylesheet"!==c.type&&(c.contentEndToken=new h(t,"body-end"),c=c.parent),u.length=0;else if(a=R(t))l(),u.push(a);else{if(!(Ae(t)||z(t)||re(t)||Ze(t,n)||B(t)||t.next()))throw new Error(`Unexpected end-of-stream at ${t.pos}`);i=i||new h(t,"preparse"),i.end=t.pos}for(i&&u.push(i),c.add(Qe(t,u)),t.start=t.pos;c&&c!==n;)c.contentEndToken=new h(t,"body-end"),c=c.parent;return n}},1253:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>$,defaultOptions:()=>A,match:()=>j});var r=n(8769),o=n(6613);class i{constructor(e,t,n,r){this.stream=e,this.type=t,this.open=n,this.close=r,this.children=[],this.parent=null}get name(){return"tag"===this.type&&this.open?this.open&&this.open.name&&this.open.name.value:"#"+this.type}get attributes(){return this.open&&this.open.attributes}get start(){return this.open&&this.open.start}get end(){return this.close?this.close.end:this.open&&this.open.end}get firstChild(){return this.children[0]}get nextSibling(){const e=this.getIndex();return-1!==e?this.parent.children[e+1]:null}get previousSibling(){const e=this.getIndex();return-1!==e?this.parent.children[e-1]:null}getIndex(){return this.parent?this.parent.children.indexOf(this):-1}addChild(e){return this.removeChild(e),this.children.push(e),e.parent=this,this}removeChild(e){const t=this.children.indexOf(e);return-1!==t&&(this.children.splice(t,1),e.parent=null),this}}var s=function(e,t,n){return"function"==typeof t?function(e,t){const n=e.pos;if(e.eatWhile(t))return new a(e,n,e.pos);e.pos=n}(e,t):new a(e,t,n)};class a{constructor(e,t,n){this.stream=e,this.start=null!=t?t:e.start,this.end=null!=n?n:e.pos,this._value=null}get value(){if(null===this._value){const e=this.stream.start,t=this.stream.pos;this.stream.start=this.start,this.stream.pos=this.end,this._value=this.stream.current(),this.stream.start=e,this.stream.pos=t}return this._value}toString(){return this.value}valueOf(){return`${this.value} [${this.start}; ${this.end}]`}}const c={throws:!0};var u=function(e){const t=e.pos;if((0,o.Ji)(e,60,62,c)||(0,o.Ji)(e,91,93,c)||(0,o.Ji)(e,40,41,c)||(0,o.Ji)(e,123,125,c))return s(e,t)};const l=47,f=61,d=62;function p(e){return u(e)||s(e,m)}function h(e){const t=e.pos;if((0,o.vP)(e)){const n=e.pos;let r,o;e.pos=t,e.next(),r=e.start=e.pos,e.pos=n,e.backUp(1),o=e.pos;const i=s(e,r,o);return e.pos=n,i}return u(e)||function(e){return s(e,b)}(e)}function m(e){return e!==f&&!g(e)&&!(0,o.xC)(e)}function g(e){return e===d||e===l}function b(e){return!(isNaN(e)||(0,o.vG)(e)||(0,o.xC)(e)||g(e))}var v=function(e){const t=e.pos;if(e.eat(60)){const n={type:e.eat(47)?"close":"open"};if((n.name=function(e){return s(e,y)}(e))&&("close"!==n.type&&(n.attributes=function(e){const t=[];let n;for(;!e.eof();)if(e.eatWhile(o.xC),n={start:e.pos},n.name=p(e))e.eat(f)?n.value=h(e):n.boolean=!0,n.end=e.pos,t.push(n);else{if(g(e.peek()))break;e.next()}return t}(e),e.eatWhile(o.xC),n.selfClosing=e.eat(47)),e.eat(62)))return Object.assign(s(e,t),n)}return e.pos=t,null};function y(e){return(0,o.gA)(e)||58===e||46===e||45===e||95===e}function x(e,t){const n=e.pos;for(let r=0;r<t.length;r++)if(!e.eat(t[r]))return e.pos=n,!1;return e.start=n,!0}function w(e,t,n,r){const o=e.pos;if(x(e,t)){for(;!e.eof();){if(x(e,n))return!0;e.next()}return!!r||(e.pos=o,!1)}return e.pos=o,null}function k(e){return e.split("").map((e=>e.charCodeAt(0)))}const C=k("\x3c!--"),_=k("--\x3e");var T=function(e){const t=e.pos;if(w(e,C,_,!0)){const n=s(e,t);return n.type="comment",n}return null};const S=k("<![CDATA["),O=k("]]>");var E=function(e){const t=e.pos;if(w(e,S,O,!0)){const n=s(e,t);return n.type="cdata",n}return null};const A={xml:!1,special:["script","style"],empty:["img","meta","link","br","base","hr","area","wbr","col","embed","input","param","source","track"]};function j(e){if(60===e.peek())return T(e)||E(e)||v(e)}function P(e,t){const n=e.pos;for(;!e.eof();){if(x(e,t))return e.pos=e.start,v(e);e.next()}return e.pos=n,null}function I(e){return e[e.length-1]}const $=function(e,t){t=Object.assign({},A,t);const n="string"==typeof e?new r.A(e):e,o=new i(n,"root"),s=new Set(t.empty),a=t.special.reduce(((e,t)=>e.set(t,k(`</${t}>`))),new Map),c=(e,n)=>e.selfClosing||!t.xml&&s.has(n);let u,l,f,d=[o];for(;!n.eof();)if(u=j(n))if(f=(p=u).name?p.name.value.toLowerCase():`#${p.type}`,"open"===u.type)l=new i(n,"tag",u),I(d).addChild(l),a.has(f)?l.close=P(n,a.get(f)):c(u,f)||d.push(l);else if("close"===u.type){for(let e=d.length-1;e>0;e--)if(d[e].name.toLowerCase()===f){d[e].close=u,d=d.slice(0,e);break}}else I(d).addChild(new i(n,u.type,u));else n.next();var p;return o}},2915:(e,t,n)=>{"use strict";function r(e){return e>47&&e<58}function o(e){return 32===e||9===e||160===e}function i(e){return o(e)||10===e||13===e}n.r(t),n.d(t,{default:()=>S,extract:()=>x,parse:()=>u});class s{constructor(e,t,n){null==n&&"string"==typeof e&&(n=e.length),this.string=e,this.pos=this.start=t||0,this.end=n||0}eof(){return this.pos>=this.end}limit(e,t){return new s(this.string,e,t)}peek(){return this.string.charCodeAt(this.pos)}next(){if(this.pos<this.string.length)return this.string.charCodeAt(this.pos++)}eat(e){const t=this.peek(),n="function"==typeof e?e(t):t===e;return n&&this.next(),n}eatWhile(e){const t=this.pos;for(;!this.eof()&&this.eat(e););return this.pos!==t}backUp(e){this.pos-=e||1}current(){return this.substring(this.start,this.pos)}substring(e,t){return this.string.slice(e,t)}error(e,t=this.pos){return new a(`${e} at ${t+1}`,t,this.string)}}class a extends Error{constructor(e,t,n){super(e),this.pos=t,this.string=n}}const c=v("null",0);function u(e){const t="string"==typeof e?new s(e):e;let n,r=0,i=21;const a=[];for(;!t.eof();)t.eatWhile(o),t.start=t.pos,l(t)?(1&i||h("Unexpected number",t),a.push(f(t.current())),i=10):b(t.peek())?(n=t.next(),m(n)&&16&i?(g(n)&&a.push(d(n,r)),i=21):(2&i||h("Unexpected operator",t),a.push(p(n,r)),i=21)):t.eat(40)?(4&i||h('Unexpected "("',t),r+=10,i=53):t.eat(41)?(r-=10,32&i?a.push(c):8&i||h('Unexpected ")"',t),i=14):h("Unknown character",t);(r<0||r>=10)&&h('Unmatched "()"',t);const u=function(e){const t=[],n=[];let r=0;for(let o=0;o<e.length;o++){const i=e[o];if("num"===i.type)n.push(i);else{for(r+="op1"===i.type?1:2;t.length&&i.priority<=t[t.length-1].priority;)n.push(t.pop());t.push(i)}}return r+1===n.length+t.length?n.concat(t.reverse()):null}(a);return null===u&&h("Parity",t),u}function l(e){const t=e.pos;return!(!e.eat(46)||!e.eatWhile(r))||!(!e.eatWhile(r)||e.eat(46)&&!e.eatWhile(r))||(e.pos=t,!1)}function f(e,t){return v("num",parseFloat(e),t)}function d(e,t=0){return 45===e&&(t+=2),v("op1",e,t)}function p(e,t=0){return 42===e?t+=1:47!==e&&92!==e||(t+=2),v("op2",e,t)}function h(e,t){throw t&&(e+=` at column ${t.pos} of expression`),new Error(e)}function m(e){return function(e){return 43===e}(e)||g(e)}function g(e){return 45===e}function b(e){return 43===e||45===e||42===e||47===e||92===e}function v(e,t,n=0){return{type:e,value:t,priority:n}}const y={lookAhead:!0,whitespace:!0};function x(e,t=e.length,n){const r=Object.assign(Object.assign({},y),n),o={text:e,pos:t};let s;if(r.lookAhead&&41===C(o)){o.pos++;const t=e.length;for(;o.pos<t&&(s=C(o),41===s||r.whitespace&&i(s));)o.pos++}const a=o.pos;let c=0;for(;o.pos>=0;)if(!w(o)){if(s=k(o),41===s)c++;else if(40===s){if(!c)break;c--}else if(!(r.whitespace&&i(s)||m(s)||b(s)))break;o.pos--}if(o.pos!==a&&!c){for(;i(C(o));)o.pos++;return[o.pos,a]}return null}function w(e){if(r(k(e))){e.pos--;let t,n=!1;for(;e.pos>=0;){if(t=k(e),46===t){if(n)break;n=!0}else if(!r(t))break;e.pos--}return!0}return!1}function k(e){return e.text.charCodeAt(e.pos-1)}function C(e){return e.text.charCodeAt(e.pos)}const _={45:e=>-e},T={43:(e,t)=>e+t,45:(e,t)=>e-t,42:(e,t)=>e*t,47:(e,t)=>e/t,92:(e,t)=>Math.floor(e/t)},S=function(e){if(Array.isArray(e)||(e=u(e)),!e||!e.length)return null;const t=[];let n,r,o;for(let i=0,s=e.length;i<s;i++){const s=e[i];if("num"===s.type)t.push(s.value);else if("op2"===s.type)r=t.pop(),n=t.pop(),o=T[s.value],t.push(o(n,r));else{if("op1"!==s.type)throw new Error("Invalid expression");n=t.pop(),o=_[s.value],t.push(o(n))}}if(t.length>1)throw new Error("Invalid Expression (parity)");return t[0]}},6613:(e,t,n)=>{"use strict";n.d(t,{Et:()=>c,Ji:()=>p,R5:()=>u,gA:()=>l,vG:()=>a,vP:()=>s,xC:()=>f});const r=39,o=34,i={escape:92,throws:!1};var s=function(e,t){t=t?Object.assign({},i,t):i;const n=e.pos,r=e.peek();if(e.eat(a)){for(;!e.eof();)switch(e.next()){case r:return e.start=n,!0;case t.escape:e.next()}if(e.pos=n,t.throws)throw e.error("Unable to consume quoted string")}return!1};function a(e){return e===r||e===o}function c(e){return e>47&&e<58}function u(e,t,n){return n=n||90,(e&=-33)>=(t=t||65)&&e<=n}function l(e){return c(e)||u(e)}function f(e){return function(e){return 32===e||9===e||160===e}(e)||10===e||13===e}const d={escape:92,throws:!1};function p(e,t,n,r){r=r?Object.assign({},d,r):d;const o=e.pos;if(e.eat(t)){let i,a=1;for(;!e.eof();)if(!s(e,r))if(i=e.next(),i===t)a++;else if(i===n){if(a--,!a)return e.start=o,!0}else i===r.escape&&e.next();if(e.pos=o,r.throws)throw e.error(`Unable to find matching pair for ${String.fromCharCode(t)}`)}return!1}},8769:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});const r=class{constructor(e,t,n){null==n&&"string"==typeof e&&(n=e.length),this.string=e,this.pos=this.start=t||0,this.end=n}eof(){return this.pos>=this.end}limit(e,t){return new this.constructor(this.string,e,t)}peek(){return this.string.charCodeAt(this.pos)}next(){if(this.pos<this.string.length)return this.string.charCodeAt(this.pos++)}eat(e){const t=this.peek(),n="function"==typeof e?e(t):t===e;return n&&this.next(),n}eatWhile(e){const t=this.pos;for(;!this.eof()&&this.eat(e););return this.pos!==t}backUp(e){this.pos-=e||1}current(){return this.substring(this.start,this.pos)}substring(e,t){return this.string.slice(e,t)}error(e){const t=new Error(`${e} at char ${this.pos+1}`);return t.originalMessage=e,t.pos=this.pos,t.string=this.string,t}}},3438:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.syntaxes=t.parseSnippets=void 0,t.parseSnippets=function(e){const t={};return Object.keys(e).forEach((n=>{for(const r of n.split("|"))t[r]=e[n]})),t},t.syntaxes={markup:["html","xml","xsl","jsx","js","pug","slim","haml","vue"],stylesheet:["css","sass","scss","less","sss","stylus"]}},8554:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.htmlData=t.cssData=void 0,t.cssData={properties:["additive-symbols","align-content","align-items","justify-items","justify-self","justify-items","align-self","all","alt","animation","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-timing-function","backface-visibility","background","background-attachment","background-blend-mode","background-clip","background-color","background-image","background-origin","background-position","background-position-x","background-position-y","background-repeat","background-size","behavior","block-size","border","border-block-end","border-block-start","border-block-end-color","border-block-start-color","border-block-end-style","border-block-start-style","border-block-end-width","border-block-start-width","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-inline-end","border-inline-start","border-inline-end-color","border-inline-start-color","border-inline-end-style","border-inline-start-style","border-inline-end-width","border-inline-start-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-decoration-break","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","caret-color","clear","clip","clip-path","clip-rule","color","color-interpolation-filters","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","columns","column-span","column-width","contain","content","counter-increment","counter-reset","cursor","direction","display","empty-cells","enable-background","fallback","fill","fill-opacity","fill-rule","filter","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","flood-color","flood-opacity","font","font-family","font-feature-settings","font-kerning","font-language-override","font-size","font-size-adjust","font-stretch","font-style","font-synthesis","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-ligatures","font-variant-numeric","font-variant-position","font-weight","glyph-orientation-horizontal","glyph-orientation-vertical","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-column","grid-column-end","grid-column-gap","grid-column-start","grid-gap","grid-row","grid-row-end","grid-row-gap","grid-row-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","height","hyphens","image-orientation","image-rendering","ime-mode","inline-size","isolation","justify-content","kerning","left","letter-spacing","lighting-color","line-break","line-height","list-style","list-style-image","list-style-position","list-style-type","margin","margin-block-end","margin-block-start","margin-bottom","margin-inline-end","margin-inline-start","margin-left","margin-right","margin-top","marker","marker-end","marker-mid","marker-start","mask-type","max-block-size","max-height","max-inline-size","max-width","min-block-size","min-height","min-inline-size","min-width","mix-blend-mode","motion","motion-offset","motion-path","motion-rotation","-moz-animation","-moz-animation-delay","-moz-animation-direction","-moz-animation-duration","-moz-animation-iteration-count","-moz-animation-name","-moz-animation-play-state","-moz-animation-timing-function","-moz-appearance","-moz-backface-visibility","-moz-background-clip","-moz-background-inline-policy","-moz-background-origin","-moz-border-bottom-colors","-moz-border-image","-moz-border-left-colors","-moz-border-right-colors","-moz-border-top-colors","-moz-box-align","-moz-box-direction","-moz-box-flex","-moz-box-flexgroup","-moz-box-ordinal-group","-moz-box-orient","-moz-box-pack","-moz-box-sizing","-moz-column-count","-moz-column-gap","-moz-column-rule","-moz-column-rule-color","-moz-column-rule-style","-moz-column-rule-width","-moz-columns","-moz-column-width","-moz-font-feature-settings","-moz-hyphens","-moz-perspective","-moz-perspective-origin","-moz-text-align-last","-moz-text-decoration-color","-moz-text-decoration-line","-moz-text-decoration-style","-moz-text-size-adjust","-moz-transform","-moz-transform-origin","-moz-transition","-moz-transition-delay","-moz-transition-duration","-moz-transition-property","-moz-transition-timing-function","-moz-user-focus","-moz-user-select","-ms-accelerator","-ms-behavior","-ms-block-progression","-ms-content-zoom-chaining","-ms-content-zooming","-ms-content-zoom-limit","-ms-content-zoom-limit-max","-ms-content-zoom-limit-min","-ms-content-zoom-snap","-ms-content-zoom-snap-points","-ms-content-zoom-snap-type","-ms-filter","-ms-flex","-ms-flex-align","-ms-flex-direction","-ms-flex-flow","-ms-flex-item-align","-ms-flex-line-pack","-ms-flex-order","-ms-flex-pack","-ms-flex-wrap","-ms-flow-from","-ms-flow-into","-ms-grid-column","-ms-grid-column-align","-ms-grid-columns","-ms-grid-column-span","-ms-grid-layer","-ms-grid-row","-ms-grid-row-align","-ms-grid-rows","-ms-grid-row-span","-ms-high-contrast-adjust","-ms-hyphenate-limit-chars","-ms-hyphenate-limit-lines","-ms-hyphenate-limit-zone","-ms-hyphens","-ms-ime-mode","-ms-interpolation-mode","-ms-layout-grid","-ms-layout-grid-char","-ms-layout-grid-line","-ms-layout-grid-mode","-ms-layout-grid-type","-ms-line-break","-ms-overflow-style","-ms-perspective","-ms-perspective-origin","-ms-perspective-origin-x","-ms-perspective-origin-y","-ms-progress-appearance","-ms-scrollbar-3dlight-color","-ms-scrollbar-arrow-color","-ms-scrollbar-base-color","-ms-scrollbar-darkshadow-color","-ms-scrollbar-face-color","-ms-scrollbar-highlight-color","-ms-scrollbar-shadow-color","-ms-scrollbar-track-color","-ms-scroll-chaining","-ms-scroll-limit","-ms-scroll-limit-x-max","-ms-scroll-limit-x-min","-ms-scroll-limit-y-max","-ms-scroll-limit-y-min","-ms-scroll-rails","-ms-scroll-snap-points-x","-ms-scroll-snap-points-y","-ms-scroll-snap-type","-ms-scroll-snap-x","-ms-scroll-snap-y","-ms-scroll-translation","-ms-text-align-last","-ms-text-autospace","-ms-text-combine-horizontal","-ms-text-justify","-ms-text-kashida-space","-ms-text-overflow","-ms-text-size-adjust","-ms-text-underline-position","-ms-touch-action","-ms-touch-select","-ms-transform","-ms-transform-origin","-ms-transform-origin-x","-ms-transform-origin-y","-ms-transform-origin-z","-ms-user-select","-ms-word-break","-ms-word-wrap","-ms-wrap-flow","-ms-wrap-margin","-ms-wrap-through","-ms-writing-mode","-ms-zoom","-ms-zoom-animation","nav-down","nav-index","nav-left","nav-right","nav-up","negative","-o-animation","-o-animation-delay","-o-animation-direction","-o-animation-duration","-o-animation-fill-mode","-o-animation-iteration-count","-o-animation-name","-o-animation-play-state","-o-animation-timing-function","object-fit","object-position","-o-border-image","-o-object-fit","-o-object-position","opacity","order","orphans","-o-table-baseline","-o-tab-size","-o-text-overflow","-o-transform","-o-transform-origin","-o-transition","-o-transition-delay","-o-transition-duration","-o-transition-property","-o-transition-timing-function","offset-block-end","offset-block-start","offset-inline-end","offset-inline-start","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-wrap","overflow-x","overflow-y","pad","padding","padding-bottom","padding-block-end","padding-block-start","padding-inline-end","padding-inline-start","padding-left","padding-right","padding-top","page-break-after","page-break-before","page-break-inside","paint-order","perspective","perspective-origin","pointer-events","position","prefix","quotes","range","resize","right","ruby-align","ruby-overhang","ruby-position","ruby-span","scrollbar-3dlight-color","scrollbar-arrow-color","scrollbar-base-color","scrollbar-darkshadow-color","scrollbar-face-color","scrollbar-highlight-color","scrollbar-shadow-color","scrollbar-track-color","scroll-behavior","scroll-snap-coordinate","scroll-snap-destination","scroll-snap-points-x","scroll-snap-points-y","scroll-snap-type","shape-image-threshold","shape-margin","shape-outside","shape-rendering","size","src","stop-color","stop-opacity","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","suffix","system","symbols","table-layout","tab-size","text-align","text-align-last","text-anchor","text-decoration","text-decoration-color","text-decoration-line","text-decoration-style","text-indent","text-justify","text-orientation","text-overflow","text-rendering","text-shadow","text-transform","text-underline-position","top","touch-action","transform","transform-origin","transform-style","transition","transition-delay","transition-duration","transition-property","transition-timing-function","unicode-bidi","unicode-range","user-select","vertical-align","visibility","-webkit-animation","-webkit-animation-delay","-webkit-animation-direction","-webkit-animation-duration","-webkit-animation-fill-mode","-webkit-animation-iteration-count","-webkit-animation-name","-webkit-animation-play-state","-webkit-animation-timing-function","-webkit-appearance","-webkit-backdrop-filter","-webkit-backface-visibility","-webkit-background-clip","-webkit-background-composite","-webkit-background-origin","-webkit-border-image","-webkit-box-align","-webkit-box-direction","-webkit-box-flex","-webkit-box-flex-group","-webkit-box-ordinal-group","-webkit-box-orient","-webkit-box-pack","-webkit-box-reflect","-webkit-box-sizing","-webkit-break-after","-webkit-break-before","-webkit-break-inside","-webkit-column-break-after","-webkit-column-break-before","-webkit-column-break-inside","-webkit-column-count","-webkit-column-gap","-webkit-column-rule","-webkit-column-rule-color","-webkit-column-rule-style","-webkit-column-rule-width","-webkit-columns","-webkit-column-span","-webkit-column-width","-webkit-filter","-webkit-flow-from","-webkit-flow-into","-webkit-font-feature-settings","-webkit-hyphens","-webkit-line-break","-webkit-margin-bottom-collapse","-webkit-margin-collapse","-webkit-margin-start","-webkit-margin-top-collapse","-webkit-mask-clip","-webkit-mask-image","-webkit-mask-origin","-webkit-mask-repeat","-webkit-mask-size","-webkit-nbsp-mode","-webkit-overflow-scrolling","-webkit-padding-start","-webkit-perspective","-webkit-perspective-origin","-webkit-region-fragment","-webkit-tap-highlight-color","-webkit-text-fill-color","-webkit-text-size-adjust","-webkit-text-stroke","-webkit-text-stroke-color","-webkit-text-stroke-width","-webkit-touch-callout","-webkit-transform","-webkit-transform-origin","-webkit-transform-origin-x","-webkit-transform-origin-y","-webkit-transform-origin-z","-webkit-transform-style","-webkit-transition","-webkit-transition-delay","-webkit-transition-duration","-webkit-transition-property","-webkit-transition-timing-function","-webkit-user-drag","-webkit-user-modify","-webkit-user-select","white-space","widows","width","will-change","word-break","word-spacing","word-wrap","writing-mode","z-index","zoom"]},t.htmlData={tags:["body","head","html","address","blockquote","dd","div","section","article","aside","header","footer","nav","menu","dl","dt","fieldset","form","frame","frameset","h1","h2","h3","h4","h5","h6","iframe","noframes","object","ol","p","ul","applet","center","dir","hr","pre","a","abbr","acronym","area","b","base","basefont","bdo","big","br","button","caption","cite","code","col","colgroup","del","dfn","em","font","i","img","input","ins","isindex","kbd","label","legend","li","link","map","meta","noscript","optgroup","option","param","q","s","samp","script","select","small","span","strike","strong","style","sub","sup","table","tbody","td","textarea","tfoot","th","thead","title","tr","tt","u","var","canvas","main","figure","plaintext","figcaption","hgroup","details","summary"]}},8708:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t},s=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.getEmmetMode=t.updateExtensionsPath=t.expandAbbreviation=t.parseAbbreviation=t.getExpandOptions=t.isAbbreviationValid=t.extractAbbreviationFromText=t.extractAbbreviation=t.getDefaultSnippets=t.getDefaultSyntax=t.getSyntaxType=t.isStyleSheet=t.emmetSnippetField=t.doComplete=t.FileType=void 0;const a=i(n(5887)),c=n(9023),u=n(7806),l=n(4756),f=n(8554),d=n(4709);Object.defineProperty(t,"FileType",{enumerable:!0,get:function(){return d.FileType}});const p=i(n(7483)),h=n(3438);let m;try{m=n(1398).l10n}catch(e){m={t:e=>e}}const g=new Map;let b;const v=new Map,y=/^[a-z,A-Z,!,(,[,#,\.\{]/,x=/^[a-z,A-Z,!,(,[,#,\.]/,w=/^-?[a-z,A-Z,!,@,#]/,k=/[a-z,A-Z\.]/,C=[...f.htmlData.tags,"lorem"],_="bem",T="|",S="t",O="c",E=3;function A(e,t,n,r,o,i,s=!0){if(!t||!e)return[];const a=[];return e.forEach((e=>{if(!e.startsWith(t.toLowerCase())||s&&e===t.toLowerCase())return;const c=n+e.substr(t.length);let l;try{l=(0,p.default)(c,o)}catch(e){}if(!l)return;const f=u.CompletionItem.create(t+e.substr(t.length));f.documentation=j(l),f.detail=i,f.textEdit=u.TextEdit.replace(r,P(I(l))),f.insertTextFormat=u.InsertTextFormat.Snippet,a.push(f)})),a}function j(e){return e.replace(/([^\\])\$\{\d+\}/g,"$1|").replace(/\$\{\d+:([^\}]+)\}/g,"$1")}function P(e){return e?e.replace(/([^\\])(\$)([^\{])/g,"$1\\$2$3"):e}function I(e){if(!e||!e.trim())return e;let t=-1,n=[],r=!1,o=!1,i=0;const s=e.length;try{for(;i<s&&!r;){if("$"!=e[i++]||"{"!=e[i++])continue;let a=-1,c=-1;for(;i<s&&/\d/.test(e[i]);)a=a<0?i:a,c=i+1,i++;if(-1===a||-1===c||i>=s||"}"!=e[i]&&":"!=e[i])continue;const u=e.substring(a,c);if(r="0"===u,r)break;let l=!1;if(":"==e[i++])for(;i<s;){if("}"==e[i]){l=!0;break}i++}Number(u)>Number(t)?(t=Number(u),n=[{numberStart:a,numberEnd:c}],o=!l):Number(u)===t&&n.push({numberStart:a,numberEnd:c})}}catch(e){}if(o&&!r)for(let t=0;t<n.length;t++){const r=n[t].numberStart,o=n[t].numberEnd;e=e.substr(0,r)+"0"+e.substr(o)}return e}function $(e,t){const n=e.offsetAt(t),r=e.getText();let o=0,i=r.length;for(let e=n-1;e>=0;e--)if("\n"===r[e]){o=e+1;break}for(let e=n;e<r.length;e++)if("\n"===r[e]){i=e;break}return r.substring(o,i)}t.doComplete=function(e,t,n,r){var o,i;if("never"===r.showExpandedAbbreviation||!X(n,r.excludeLanguages))return;const s=L(n);if(!s){if(!g.has(n)){const e=Object.assign(Object.assign({},F(n)),R[n]);g.set(n,Object.keys(e))}b=null!==(o=g.get(n))&&void 0!==o?o:[]}const a=U(e,t,{lookAhead:!s,type:s?"stylesheet":"markup"});if(!a)return;const{abbreviationRange:c,abbreviation:l,filter:d}=a,y=$(e,t).substr(0,t.character);if(function(e){if(e){const t=e.match(/[\w,:,-,\.]*$/);if(t)return t[0]}}(y)===l&&y.endsWith(`<${l}`)&&h.syntaxes.markup.includes(n))return;const x=V(n,r,d);let w,k="",_=[];const T=(e,t)=>{if(q(e,l)){try{k=(0,p.default)(t,x),s&&"!important".startsWith(t)&&(k="!important")}catch(e){}k&&!function(e,t,n,r){var o,i;if(L(e)&&r){const e=null!==(o=r["stylesheet.between"])&&void 0!==o?o:": ",s=null!==(i=r["stylesheet.after"])&&void 0!==i?i:";";let a=t.indexOf(e[0],Math.max(t.length-e.length,0));return a=a>=0?a:t.length,n===`${t.substring(0,a)}${e}\${0}${s}`||n.replace(/\s/g,"")===t.replace(/\s/g,"")+s}if("xml"===e&&C.some((e=>e.startsWith(t.toLowerCase()))))return!0;if(C.includes(t.toLowerCase())||b.includes(t))return!1;if(/[-,:]/.test(t)&&!/--|::/.test(t)&&!t.endsWith(":"))return!1;if(/^\.{2,}$/.test(t))return!0;if("."===t)return!1;const s=t.match(/^([a-z,A-Z,\d]*)\.$/);return s?!s[1]||!f.htmlData.tags.includes(s[1]):("jsx"!==e||!/^([A-Z][A-Za-z0-9]*)+$/.test(t))&&n.toLowerCase()===`<${t.toLowerCase()}>\${1}</${t.toLowerCase()}>`}(e,t,k,x.options)&&(w=u.CompletionItem.create(t),w.textEdit=u.TextEdit.replace(c,P(I(k))),w.documentation=j(k),w.insertTextFormat=u.InsertTextFormat.Snippet,w.detail=m.t("Emmet Abbreviation"),w.label=l,w.label+=d?"|"+d.replace(",","|"):"",_=[w])}};if(L(n)){if(T(n,l),l.length>4&&f.cssData.properties.find((e=>e.startsWith(l))))return u.CompletionList.create([],!0);if(w&&k.length){w.textEdit=u.TextEdit.replace(c,P(I(k))),w.documentation=j(k),w.label=k.replace(/([^\\])\$\{\d+\}/g,"$1").replace(/\$\{\d+:([^\}]+)\}/g,"$1"),w.filterText=l;const e=v.has(n)?v.get(n):v.get("css");if(_=A(null!=e?e:[],l,l,c,x,"Emmet Custom Snippet",!1),!_.find((e=>{var t,n,r;return(null===(t=e.textEdit)||void 0===t?void 0:t.newText)&&(null===(n=e.textEdit)||void 0===n?void 0:n.newText)===(null===(r=null==w?void 0:w.textEdit)||void 0===r?void 0:r.newText)}))){const e=new RegExp(".*"+l.split("").map((e=>"$"===e||"+"===e?"\\"+e:e)).join(".*")+".*","i");(/\d/.test(l)||e.test(w.label))&&_.push(w)}}}else{T(n,l);let e=l;const t=l.match(/(>|\+)([\w:-]+)$/);if(t&&3===t.length&&(e=t[2]),"xml"!==n){const t=A(C,e,l,c,x,"Emmet Abbreviation");_=_.concat(t)}if(!0===r.showAbbreviationSuggestions){const t=A(b.filter((e=>!C.includes(e))),e,l,c,x,"Emmet Abbreviation");w&&t.length>0&&e!==l&&(w.sortText="0"+w.label,t.forEach((e=>{e.filterText=l,e.sortText="9"+l}))),_=_.concat(t)}"html"===n&&_.length>=2&&l.includes(":")&&(null===(i=null==w?void 0:w.textEdit)||void 0===i?void 0:i.newText)===`<${l}>\${0}</${l}>`&&(_=_.filter((e=>e.label!==l)))}return!0===r.showSuggestionsAsSnippets&&_.forEach((e=>e.kind=u.CompletionItemKind.Snippet)),_.length?u.CompletionList.create(_,!0):void 0};let R={},M={},N={};function L(e){return h.syntaxes.stylesheet.includes(e)}function z(e){return L(e)?"stylesheet":"markup"}function D(e){return L(e)?"css":"html"}function F(e){const t={type:z(e),syntax:e},n=(0,p.resolveConfig)(t);return"xml"===e?{}:n.snippets}function B(e,t){let n;for(let r=0;r<E;r++)if(e.endsWith(`${T}${_}`,t))t-=_.length+1,n=n?_+","+n:_;else if(e.endsWith(`${T}${O}`,t))t-=O.length+1,n=n?O+","+n:O;else{if(!e.endsWith(`${T}${S}`,t))break;t-=S.length+1,n=n?S+","+n:S}return{pos:t,filter:n}}function U(e,t,n){const r=$(e,t),o=r.substr(0,t.character),{pos:i,filter:s}=B(o,t.character),a=s?s.length+1:0,c=(0,p.extract)(r,i,n);if(c)return{abbreviationRange:u.Range.create(t.line,c.location,t.line,c.location+c.abbreviation.length+a),abbreviation:c.abbreviation,filter:s}}function q(e,t){if(!t)return!1;if(L(e)){if(t.includes("#")){if(t.startsWith("#"))return/^#[\d,a-f,A-F]{1,6}$/.test(t);if(C.includes(t.substring(0,t.indexOf("#"))))return!1}return w.test(t)}return t.startsWith("!")?!/[^!]/.test(t):!!(!/\(/.test(t)&&!/\)/.test(t)||/\{[^\}\{]*[\(\)]+[^\}\{]*\}(?:[>\+\*\^]|$)/.test(t)||/\(.*\)[>\+\*\^]/.test(t)||/\[[^\[\]\(\)]+=".*"\]/.test(t)||/[>\+\*\^]\(.*\)/.test(t))&&("jsx"===e?x.test(t)&&k.test(t):!/^{%|{#|{{/.test(t)&&y.test(t)&&k.test(t))}function V(e,n,r){var o,i,s,a,c,u,l,f,d,p;(n=null!=n?n:{}).preferences=null!==(o=n.preferences)&&void 0!==o?o:{};const h=n.preferences,m=L(e)?e:"css",g=function(e,t){t||(t={});const n=Object.assign({},N,t)[e];if(!n||"string"==typeof n)return"xhtml"===n?{selfClosingStyle:"xhtml"}:{};const r={};for(const e in n)switch(e){case"tag_case":r.tagCase="lower"===n[e]||"upper"===n[e]?n[e]:"";break;case"attr_case":r.attributeCase="lower"===n[e]||"upper"===n[e]?n[e]:"";break;case"attr_quotes":r.attributeQuotes=n[e];break;case"tag_nl":r.format=!0!==n[e]&&!1!==n[e]||n[e];break;case"inline_break":r.inlineBreak=n[e];break;case"self_closing_tag":if(!0===n[e]){r.selfClosingStyle="xml";break}if(!1===n[e]){r.selfClosingStyle="html";break}r.selfClosingStyle=n[e];break;case"compact_bool":r.compactBooleanAttributes=n[e];break;default:r[e]=n[e]}return r}(e,null!==(i=n.syntaxProfiles)&&void 0!==i?i:{}),b=(g&&g.filters?g.filters.split(","):[]).map((e=>e.trim())),v=r&&r.split(",").some((e=>"bem"===e.trim()))||b.includes("bem"),y=r&&r.split(",").some((e=>"c"===e.trim()))||b.includes("c"),x=function(e,t){if(!t||"object"!=typeof t)return{};if(!L(e)){const e={};for(const n in t)switch(n){case"filter.commentAfter":e.after=t[n];break;case"filter.commentBefore":e.before=t[n];break;case"filter.commentTrigger":e.trigger=t[n]}return{comment:e}}let n="number"==typeof(null==t?void 0:t["css.fuzzySearchMinScore"])?t["css.fuzzySearchMinScore"]:.3;n>1?n=1:n<0&&(n=0);const r={fuzzySearchMinScore:n};for(const n in t)switch(n){case"css.floatUnit":r.floatUnit=t[n];break;case"css.intUnit":r.intUnit=t[n];break;case"css.unitAliases":const o={};t[n].split(",").forEach((e=>{if(!e||!e.trim()||!e.includes(":"))return;const t=e.substr(0,e.indexOf(":")),n=e.substr(t.length+1);t.trim()&&n&&(o[t.trim()]=n)})),r.unitAliases=o;break;case`${e}.valueSeparator`:r.between=t[n];break;case`${e}.propertyEnd`:r.after=t[n]}return{stylesheet:r}}(e,n.preferences),w=(null==x?void 0:x.stylesheet)&&x.stylesheet.unitAliases||{},k={"output.formatSkip":["html"],"output.formatForce":["body"],"output.inlineBreak":0,"output.compactBoolean":!1,"output.reverseAttributes":!1,"output.field":t.emmetSnippetField,"markup.href":!0,"comment.enabled":!1,"comment.trigger":["id","class"],"comment.before":"","comment.after":"\n\x3c!-- /[#ID][.CLASS] --\x3e","bem.enabled":!1,"bem.element":"__","bem.modifier":"_","jsx.enabled":"jsx"===e,"stylesheet.shortHex":!0,"stylesheet.between":"stylus"===e?" ":": ","stylesheet.after":"sass"===e||"stylus"===e?"":";","stylesheet.intUnit":"px","stylesheet.floatUnit":"em","stylesheet.unitAliases":{e:"em",p:"%",x:"ex",r:"rem"},"stylesheet.fuzzySearchMinScore":.3};let C={"output.tagCase":g.tagCase,"output.attributeCase":g.attributeCase,"output.attributeQuotes":g.attributeQuotes,"output.format":null===(s=g.format)||void 0===s||s,"output.formatSkip":h["format.noIndentTags"],"output.formatForce":h["format.forceIndentationForTags"],"output.inlineBreak":null!==(a=g.inlineBreak)&&void 0!==a?a:h["output.inlineBreak"],"output.compactBoolean":null!==(c=g.compactBooleanAttributes)&&void 0!==c?c:h["profile.allowCompactBoolean"],"output.reverseAttributes":h["output.reverseAttributes"],"output.selfClosingStyle":null!==(l=null!==(u=g.selfClosingStyle)&&void 0!==u?u:h["output.selfClosingStyle"])&&void 0!==l?l:W(e),"output.field":t.emmetSnippetField,"comment.enabled":y,"comment.trigger":h["filter.commentTrigger"],"comment.before":h["filter.commentBefore"],"comment.after":h["filter.commentAfter"],"bem.enabled":v,"bem.element":null!==(f=h["bem.elementSeparator"])&&void 0!==f?f:"__","bem.modifier":null!==(d=h["bem.modifierSeparator"])&&void 0!==d?d:"_","jsx.enabled":"jsx"===e,"stylesheet.shortHex":h["css.color.short"],"stylesheet.between":h[`${m}.valueSeparator`],"stylesheet.after":h[`${m}.propertyEnd`],"stylesheet.intUnit":h["css.intUnit"],"stylesheet.floatUnit":h["css.floatUnit"],"stylesheet.unitAliases":w,"stylesheet.fuzzySearchMinScore":h["css.fuzzySearchMinScore"]};if("jsx"===e){const e={class:"className","class*":"styleName",for:"htmlFor"},t={"class*":"styles"};g["markup.attributes"]&&(C["markup.attributes"]=Object.assign(Object.assign({},e),g["markup.attributes"])),g["markup.valuePrefix"]&&(C["markup.valuePrefix"]=Object.assign(Object.assign({},t),g["markup.valuePrefix"]))}if("vue"===e){const e={"class*":":class"},t={"class*":"$style"};g["markup.attributes"]&&(C["markup.attributes"]=Object.assign(Object.assign({},e),g["markup.attributes"])),g["markup.valuePrefix"]&&(C["markup.valuePrefix"]=Object.assign(Object.assign({},t),g["markup.valuePrefix"]))}const _={};[...Object.keys(k),...Object.keys(C)].forEach((e=>{var t;const n=e;_[n]=null!==(t=C[n])&&void 0!==t?t:k[n]}));const T=Object.assign(Object.assign({},k["stylesheet.unitAliases"]),C["stylesheet.unitAliases"]);_["stylesheet.unitAliases"]=T;const S=z(e),O=(j=n.variables)?Object.assign({},M,j):M,E=D(e),A="stylesheet"===S?null!==(p=R[e])&&void 0!==p?p:R[E]:R[e];var j;return{type:S,options:_,variables:O,snippets:A,syntax:e,text:void 0,maxRepeat:1e3}}function W(e){switch(e){case"xhtml":case"jsx":return"xhtml";case"xml":case"xsl":return"xml";default:return"html"}}function H(e,t){let n=[];const r=a.parse(t,n);if(n.length)throw new Error(`Found error ${a.printParseErrorCode(n[0].error)} while parsing the file ${e} at offset ${n[0].offset}`);return r}function G(e){if("object"!=typeof e||!e)throw new Error(m.t("Invalid emmet.variables field. See https://code.visualstudio.com/docs/editor/emmet#_emmet-configuration for a valid example."));M=Object.assign({},M,e)}function J(e){if("object"!=typeof e||!e)throw new Error(m.t("Invalid syntax profile. See https://code.visualstudio.com/docs/editor/emmet#_emmet-configuration for a valid example."));N=Object.assign({},N,e)}function Q(e){if("object"!=typeof e||!e)throw new Error(m.t("Invalid snippets file. See https://code.visualstudio.com/docs/editor/emmet#_using-custom-emmet-snippets for a valid example."));Object.keys(e).forEach((t=>{if(!e[t].snippets)return;const n=D(t);let r=e[t].snippets;if(e[n]&&e[n].snippets&&n!==t&&(r=Object.assign({},e[n].snippets,e[t].snippets)),L(t)){const e=v.get(t),n=Object.assign([],e,Object.keys(r));v.set(t,n)}else for(const e in r)r.hasOwnProperty(e)&&r[e].startsWith("<")&&r[e].endsWith(">")&&(r[e]=`{${r[e]}}`);const o=R[t],i=(0,h.parseSnippets)(r),s=Object.assign({},o,i);R[t]=s}))}function K(){R={},g.clear(),v.clear(),N={},M={}}function X(e,t=[]){if(e&&!t.includes(e))return/\b(typescriptreact|javascriptreact|jsx-tags)\b/.test(e)?"jsx":"sass-indented"===e?"sass":"jade"===e?"pug":h.syntaxes.markup.includes(e)||h.syntaxes.stylesheet.includes(e)?e:void 0}t.emmetSnippetField=(e,t)=>`\${${e}${t?":"+t:""}}`,t.isStyleSheet=L,t.getSyntaxType=z,t.getDefaultSyntax=D,t.getDefaultSnippets=F,t.extractAbbreviation=U,t.extractAbbreviationFromText=function(e,t){if(!e)return;const{pos:n,filter:r}=B(e,e.length),o=L(t)||"stylesheet"===t?{syntax:"stylesheet",lookAhead:!1}:{lookAhead:!0},i=(0,p.extract)(e,n,o);return i?{abbreviation:i.abbreviation,filter:r}:void 0},t.isAbbreviationValid=q,t.getExpandOptions=V,t.parseAbbreviation=function(e,t){const n=(0,p.resolveConfig)(t);return"stylesheet"===t.type?(0,p.parseStylesheet)(e,n):(0,p.parseMarkup)(e,n)},t.expandAbbreviation=function(e,t){let n;const r=(0,p.resolveConfig)(t);return n="stylesheet"===t.type?"string"==typeof e?(0,p.default)(e,r):(0,p.stringifyStylesheet)(e,r):"string"==typeof e?(0,p.default)(e,r):(0,p.stringifyMarkup)(e,r),P(I(n))},t.updateExtensionsPath=function(e,t,n,r){return s(this,void 0,void 0,(function*(){if(K(),!e.length)return;const o=[];for(let t of e)if("string"==typeof t){if(t=t.trim(),t.length&&"~"===t[0])r&&o.push((0,d.joinPath)(r,t.substring(1)));else if((0,d.isAbsolutePath)(t))o.push(l.URI.file(t));else if(n)for(const e of n)o.push((0,d.joinPath)(e,t))}else console.warn("The following emmetExtensionsPath isn't a string: "+JSON.stringify(t));for(const e of o){try{if((yield t.stat(e)).type!==d.FileType.Directory)continue}catch(e){continue}const n=(0,d.joinPath)(e,"snippets.json"),r=(0,d.joinPath)(e,"syntaxProfiles.json");let o;o="function"==typeof globalThis.TextDecoder?new globalThis.TextDecoder:new c.TextDecoder;let i="";try{const e=yield t.readFile(n);i=o.decode(e)}catch(e){}if(i.length)try{const e=H(n,i);e.variables&&G(e.variables),Q(e)}catch(e){throw K(),e}let s="";try{const e=yield t.readFile(r);s=o.decode(e)}catch(e){}if(s.length)try{J(H(r,s))}catch(e){throw K(),e}}}))},t.getEmmetMode=X},4709:(e,t)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.joinPath=t.normalizePath=t.resolvePath=t.isAbsolutePath=t.FileType=void 0,(n=t.FileType||(t.FileType={}))[n.Unknown=0]="Unknown",n[n.File=1]="File",n[n.Directory=2]="Directory",n[n.SymbolicLink=64]="SymbolicLink";const r=new RegExp("^(/|//|\\\\\\\\|[A-Za-z]:(/|\\\\))"),o=".".charCodeAt(0);function i(e){return r.test(e)}function s(e){const t=[];for(const n of e)0===n.length||1===n.length&&n.charCodeAt(0)===o||(2===n.length&&n.charCodeAt(0)===o&&n.charCodeAt(1)===o?t.pop():t.push(n));e.length>1&&0===e[e.length-1].length&&t.push("");let n=t.join("/");return 0===e[0].length&&(n="/"+n),n}function a(e,...t){const n=e.path.split("/");for(const e of t)n.push(...e.split("/"));return e.with({path:s(n)})}t.isAbsolutePath=i,t.resolvePath=function(e,t){return i(t)?e.with({path:s(t.split("/"))}):a(e,t)},t.normalizePath=s,t.joinPath=a},2647:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.detector=void 0;const r=n(2308),o=Object.keys(r.typeHandlers),i={56:"psd",66:"bmp",68:"dds",71:"gif",73:"tiff",77:"tiff",82:"webp",105:"icns",137:"png",255:"jpg"};t.detector=function(e){const t=e[0];if(t in i){const n=i[t];if(n&&r.typeHandlers[n].validate(e))return n}return o.find((t=>r.typeHandlers[t].validate(e)))}},5949:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.types=t.setConcurrency=t.disableTypes=t.disableFS=t.imageSize=void 0;const o=n(9896),i=n(6928),s=n(9792),a=n(2308),c=n(2647),u=524288,l=new s.default({concurrency:100,autostart:!0}),f={disabledFS:!1,disabledTypes:[]};function d(e,t){const n=c.detector(e);if(void 0!==n){if(f.disabledTypes.indexOf(n)>-1)throw new TypeError("disabled file type: "+n);if(n in a.typeHandlers){const r=a.typeHandlers[n].calculate(e,t);if(void 0!==r)return r.type=n,r}}throw new TypeError("unsupported file type: "+n+" (file: "+t+")")}function p(e,t){if(Buffer.isBuffer(e))return d(e);if("string"!=typeof e||f.disabledFS)throw new TypeError("invalid invocation. input should be a Buffer");const n=i.resolve(e);if("function"!=typeof t){const e=function(e){const t=o.openSync(e,"r"),{size:n}=o.fstatSync(t);if(n<=0)throw o.closeSync(t),new Error("Empty file");const r=Math.min(n,u),i=Buffer.alloc(r);return o.readSync(t,i,0,r,0),o.closeSync(t),i}(n);return d(e,n)}l.push((()=>function(e){return r(this,void 0,void 0,(function*(){const t=yield o.promises.open(e,"r"),{size:n}=yield t.stat();if(n<=0)throw yield t.close(),new Error("Empty file");const r=Math.min(n,u),i=Buffer.alloc(r);return yield t.read(i,0,r,0),yield t.close(),i}))}(n).then((e=>process.nextTick(t,null,d(e,n)))).catch(t)))}e.exports=t=p,t.default=p,t.imageSize=p,t.disableFS=e=>{f.disabledFS=e},t.disableTypes=e=>{f.disabledTypes=e},t.setConcurrency=e=>{l.concurrency=e},t.types=Object.keys(a.typeHandlers)},7533:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.readUInt=void 0,t.readUInt=function(e,t,n,r){return n=n||0,e["readUInt"+t+(r?"BE":"LE")].call(e,n)}},2308:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.typeHandlers=void 0;const r=n(9648),o=n(941),i=n(7562),s=n(7857),a=n(4280),c=n(4270),u=n(4262),l=n(3879),f=n(3068),d=n(4782),p=n(8628),h=n(54),m=n(3564),g=n(7961),b=n(6004),v=n(2093);t.typeHandlers={bmp:r.BMP,cur:o.CUR,dds:i.DDS,gif:s.GIF,icns:a.ICNS,ico:c.ICO,j2c:u.J2C,jp2:l.JP2,jpg:f.JPG,ktx:d.KTX,png:p.PNG,pnm:h.PNM,psd:m.PSD,svg:g.SVG,tiff:b.TIFF,webp:v.WEBP}},9648:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.BMP=void 0,t.BMP={validate:e=>"BM"===e.toString("ascii",0,2),calculate:e=>({height:Math.abs(e.readInt32LE(22)),width:e.readUInt32LE(18)})}},941:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CUR=void 0;const r=n(4270);t.CUR={validate:e=>0===e.readUInt16LE(0)&&2===e.readUInt16LE(2),calculate:e=>r.ICO.calculate(e)}},7562:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DDS=void 0,t.DDS={validate:e=>542327876===e.readUInt32LE(0),calculate:e=>({height:e.readUInt32LE(12),width:e.readUInt32LE(16)})}},7857:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GIF=void 0;const n=/^GIF8[79]a/;t.GIF={validate(e){const t=e.toString("ascii",0,6);return n.test(t)},calculate:e=>({height:e.readUInt16LE(8),width:e.readUInt16LE(6)})}},4280:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ICNS=void 0;const n={ICON:32,"ICN#":32,"icm#":16,icm4:16,icm8:16,"ics#":16,ics4:16,ics8:16,is32:16,s8mk:16,icp4:16,icl4:32,icl8:32,il32:32,l8mk:32,icp5:32,ic11:32,ich4:48,ich8:48,ih32:48,h8mk:48,icp6:64,ic12:32,it32:128,t8mk:128,ic07:128,ic08:256,ic13:256,ic09:512,ic14:512,ic10:1024};function r(e,t){const n=t+4;return[e.toString("ascii",t,n),e.readUInt32BE(n)]}function o(e){const t=n[e];return{width:t,height:t,type:e}}t.ICNS={validate:e=>"icns"===e.toString("ascii",0,4),calculate(e){const t=e.length,n=e.readUInt32BE(4);let i=8,s=r(e,i),a=o(s[0]);if(i+=s[1],i===n)return a;const c={height:a.height,images:[a],width:a.width};for(;i<n&&i<t;)s=r(e,i),a=o(s[0]),i+=s[1],c.images.push(a);return c}}},4270:(e,t)=>{"use strict";function n(e,t){const n=e.readUInt8(t);return 0===n?256:n}function r(e,t){const r=6+16*t;return{height:n(e,r+1),width:n(e,r)}}Object.defineProperty(t,"__esModule",{value:!0}),t.ICO=void 0,t.ICO={validate:e=>0===e.readUInt16LE(0)&&1===e.readUInt16LE(2),calculate(e){const t=e.readUInt16LE(4),n=r(e,0);if(1===t)return n;const o=[n];for(let n=1;n<t;n+=1)o.push(r(e,n));return{height:n.height,images:o,width:n.width}}}},4262:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.J2C=void 0,t.J2C={validate:e=>"ff4fff51"===e.toString("hex",0,4),calculate:e=>({height:e.readUInt32BE(12),width:e.readUInt32BE(8)})}},3879:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.JP2=void 0;const n=e=>({height:e.readUInt32BE(4),width:e.readUInt32BE(8)});t.JP2={validate(e){const t=e.toString("hex",4,8),n=e.readUInt32BE(0);if("6a502020"!==t||n<1)return!1;const r=n+4,o=e.readUInt32BE(n);return"66747970"===e.slice(r,r+o).toString("hex",0,4)},calculate(e){const t=e.readUInt32BE(0);let r=t+4+e.readUInt16BE(t+2);switch(e.toString("hex",r,r+4)){case"72726571":return r=r+4+4+(e=>{const t=e.readUInt8(0);let n=1+2*t;return n=n+2+e.readUInt16BE(n)*(2+t),n+2+e.readUInt16BE(n)*(16+t)})(e.slice(r+4)),n(e.slice(r+8,r+24));case"6a703268":return n(e.slice(r+8,r+24));default:throw new TypeError("Unsupported header found: "+e.toString("ascii",r,r+4))}}}},3068:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.JPG=void 0;const r=n(7533);function o(e){return"45786966"===e.toString("hex",2,6)}function i(e,t){return{height:e.readUInt16BE(t),width:e.readUInt16BE(t+2)}}function s(e,t){const n=e.slice(2,t),o=n.toString("hex",6,8),i="4d4d"===o;if(i||"4949"===o)return function(e,t){const n=r.readUInt(e,16,14,t);for(let o=0;o<n;o++){const n=16+12*o,i=n+12;if(n>e.length)return;const s=e.slice(n,i);if(274===r.readUInt(s,16,0,t)){if(3!==r.readUInt(s,16,2,t))return;if(1!==r.readUInt(s,32,4,t))return;return r.readUInt(s,16,8,t)}}}(n,i)}function a(e,t){if(t>e.length)throw new TypeError("Corrupt JPG, exceeded buffer limits");if(255!==e[t])throw new TypeError("Invalid JPG, marker table corrupted")}t.JPG={validate:e=>"ffd8"===e.toString("hex",0,2),calculate(e){let t,n;for(e=e.slice(4);e.length;){const r=e.readUInt16BE(0);if(o(e)&&(t=s(e,r)),a(e,r),n=e[r+1],192===n||193===n||194===n){const n=i(e,r+5);return t?{height:n.height,orientation:t,width:n.width}:n}e=e.slice(r+2)}throw new TypeError("Invalid JPG, no size found")}}},4782:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.KTX=void 0,t.KTX={validate:e=>"KTX 11"===e.toString("ascii",1,7),calculate:e=>({height:e.readUInt32LE(40),width:e.readUInt32LE(36)})}},8628:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PNG=void 0;const n="CgBI";t.PNG={validate(e){if("PNG\r\n\n"===e.toString("ascii",1,8)){let t=e.toString("ascii",12,16);if(t===n&&(t=e.toString("ascii",28,32)),"IHDR"!==t)throw new TypeError("Invalid PNG");return!0}return!1},calculate:e=>e.toString("ascii",12,16)===n?{height:e.readUInt32BE(36),width:e.readUInt32BE(32)}:{height:e.readUInt32BE(20),width:e.readUInt32BE(16)}}},54:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PNM=void 0;const n={P1:"pbm/ascii",P2:"pgm/ascii",P3:"ppm/ascii",P4:"pbm",P5:"pgm",P6:"ppm",P7:"pam",PF:"pfm"},r=Object.keys(n),o={default:e=>{let t=[];for(;e.length>0;){const n=e.shift();if("#"!==n[0]){t=n.split(" ");break}}if(2===t.length)return{height:parseInt(t[1],10),width:parseInt(t[0],10)};throw new TypeError("Invalid PNM")},pam:e=>{const t={};for(;e.length>0;){const n=e.shift();if(n.length>16||n.charCodeAt(0)>128)continue;const[r,o]=n.split(" ");if(r&&o&&(t[r.toLowerCase()]=parseInt(o,10)),t.height&&t.width)break}if(t.height&&t.width)return{height:t.height,width:t.width};throw new TypeError("Invalid PAM")}};t.PNM={validate(e){const t=e.toString("ascii",0,2);return r.includes(t)},calculate(e){const t=e.toString("ascii",0,2),r=n[t],i=e.toString("ascii",3).split(/[\r\n]+/);return(o[r]||o.default)(i)}}},3564:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PSD=void 0,t.PSD={validate:e=>"8BPS"===e.toString("ascii",0,4),calculate:e=>({height:e.readUInt32BE(14),width:e.readUInt32BE(18)})}},7961:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SVG=void 0;const n=/<svg\s([^>"']|"[^"]*"|'[^']*')*>/,r={height:/\sheight=(['"])([^%]+?)\1/,root:n,viewbox:/\sviewBox=(['"])(.+?)\1/i,width:/\swidth=(['"])([^%]+?)\1/},o=2.54,i={in:96,cm:96/o,em:16,ex:8,m:96/o*100,mm:96/o/10,pc:96/72/12,pt:96/72,px:1},s=new RegExp(`^([0-9.]+(?:e\\d+)?)(${Object.keys(i).join("|")})?$`);function a(e){const t=s.exec(e);if(t)return Math.round(Number(t[1])*(i[t[2]]||1))}function c(e){const t=e.split(" ");return{height:a(t[3]),width:a(t[2])}}t.SVG={validate(e){const t=String(e);return n.test(t)},calculate(e){const t=e.toString("utf8").match(r.root);if(t){const e=function(e){const t=e.match(r.width),n=e.match(r.height),o=e.match(r.viewbox);return{height:n&&a(n[2]),viewbox:o&&c(o[2]),width:t&&a(t[2])}}(t[0]);if(e.width&&e.height)return function(e){return{height:e.height,width:e.width}}(e);if(e.viewbox)return function(e,t){const n=t.width/t.height;return e.width?{height:Math.floor(e.width/n),width:e.width}:e.height?{height:e.height,width:Math.floor(e.height*n)}:{height:t.height,width:t.width}}(e,e.viewbox)}throw new TypeError("Invalid SVG")}}},6004:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TIFF=void 0;const r=n(9896),o=n(7533);function i(e,t){const n=o.readUInt(e,16,8,t);return(o.readUInt(e,16,10,t)<<16)+n}function s(e){if(e.length>24)return e.slice(12)}const a=["49492a00","4d4d002a"];t.TIFF={validate:e=>a.includes(e.toString("hex",0,4)),calculate(e,t){if(!t)throw new TypeError("Tiff doesn't support buffer");const n="BE"===function(e){const t=e.toString("ascii",0,2);return"II"===t?"LE":"MM"===t?"BE":void 0}(e),a=function(e,t,n){const i=o.readUInt(e,32,4,n);let s=1024;const a=r.statSync(t).size;i+s>a&&(s=a-i-10);const c=Buffer.alloc(s),u=r.openSync(t,"r");return r.readSync(u,c,0,s,i),r.closeSync(u),c.slice(2)}(e,t,n),c=function(e,t){const n={};let r=e;for(;r&&r.length;){const e=o.readUInt(r,16,0,t),a=o.readUInt(r,16,2,t),c=o.readUInt(r,32,4,t);if(0===e)break;1!==c||3!==a&&4!==a||(n[e]=i(r,t)),r=s(r)}return n}(a,n),u=c[256],l=c[257];if(!u||!l)throw new TypeError("Invalid Tiff. Missing tags");return{height:l,width:u}}}},2093:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WEBP=void 0,t.WEBP={validate(e){const t="RIFF"===e.toString("ascii",0,4),n="WEBP"===e.toString("ascii",8,12),r="VP8"===e.toString("ascii",12,15);return t&&n&&r},calculate(e){const t=e.toString("ascii",12,16);if(e=e.slice(20,30),"VP8X"===t){const t=e[0];if(!(192&t||1&t))return function(e){return{height:1+e.readUIntLE(7,3),width:1+e.readUIntLE(4,3)}}(e);throw new TypeError("Invalid WebP")}if("VP8 "===t&&47!==e[0])return function(e){return{height:16383&e.readInt16LE(8),width:16383&e.readInt16LE(6)}}(e);const n=e.toString("hex",3,6);if("VP8L"===t&&"9d012a"!==n)return function(e){return{height:1+((15&e[4])<<10|e[3]<<2|(192&e[2])>>6),width:1+((63&e[2])<<8|e[1])}}(e);throw new TypeError("Invalid WebP")}}},2017:(e,t,n)=>{try{var r=n(9023);if("function"!=typeof r.inherits)throw"";e.exports=r.inherits}catch(t){e.exports=n(6698)}},6698:e=>{"function"==typeof Object.create?e.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:e.exports=function(e,t){if(t){e.super_=t;var n=function(){};n.prototype=t.prototype,e.prototype=new n,e.prototype.constructor=e}}},5887:(e,t,n)=>{"use strict";function r(e,t){void 0===t&&(t=!1);var n=e.length,r=0,a="",c=0,u=16,l=0,f=0,d=0,p=0,h=0;function m(t,n){for(var o=0,i=0;o<t||!n;){var s=e.charCodeAt(r);if(s>=48&&s<=57)i=16*i+s-48;else if(s>=65&&s<=70)i=16*i+s-65+10;else{if(!(s>=97&&s<=102))break;i=16*i+s-97+10}r++,o++}return o<t&&(i=-1),i}function g(){if(a="",h=0,c=r,f=l,p=d,r>=n)return c=n,u=17;var t=e.charCodeAt(r);if(o(t)){do{r++,a+=String.fromCharCode(t),t=e.charCodeAt(r)}while(o(t));return u=15}if(i(t))return r++,a+=String.fromCharCode(t),13===t&&10===e.charCodeAt(r)&&(r++,a+="\n"),l++,d=r,u=14;switch(t){case 123:return r++,u=1;case 125:return r++,u=2;case 91:return r++,u=3;case 93:return r++,u=4;case 58:return r++,u=6;case 44:return r++,u=5;case 34:return r++,a=function(){for(var t="",o=r;;){if(r>=n){t+=e.substring(o,r),h=2;break}var s=e.charCodeAt(r);if(34===s){t+=e.substring(o,r),r++;break}if(92!==s){if(s>=0&&s<=31){if(i(s)){t+=e.substring(o,r),h=2;break}h=6}r++}else{if(t+=e.substring(o,r),++r>=n){h=2;break}switch(e.charCodeAt(r++)){case 34:t+='"';break;case 92:t+="\\";break;case 47:t+="/";break;case 98:t+="\b";break;case 102:t+="\f";break;case 110:t+="\n";break;case 114:t+="\r";break;case 116:t+="\t";break;case 117:var a=m(4,!0);a>=0?t+=String.fromCharCode(a):h=4;break;default:h=5}o=r}}return t}(),u=10;case 47:var g=r-1;if(47===e.charCodeAt(r+1)){for(r+=2;r<n&&!i(e.charCodeAt(r));)r++;return a=e.substring(g,r),u=12}if(42===e.charCodeAt(r+1)){r+=2;for(var v=n-1,y=!1;r<v;){var x=e.charCodeAt(r);if(42===x&&47===e.charCodeAt(r+1)){r+=2,y=!0;break}r++,i(x)&&(13===x&&10===e.charCodeAt(r)&&r++,l++,d=r)}return y||(r++,h=1),a=e.substring(g,r),u=13}return a+=String.fromCharCode(t),r++,u=16;case 45:if(a+=String.fromCharCode(t),++r===n||!s(e.charCodeAt(r)))return u=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return a+=function(){var t=r;if(48===e.charCodeAt(r))r++;else for(r++;r<e.length&&s(e.charCodeAt(r));)r++;if(r<e.length&&46===e.charCodeAt(r)){if(!(++r<e.length&&s(e.charCodeAt(r))))return h=3,e.substring(t,r);for(r++;r<e.length&&s(e.charCodeAt(r));)r++}var n=r;if(r<e.length&&(69===e.charCodeAt(r)||101===e.charCodeAt(r)))if((++r<e.length&&43===e.charCodeAt(r)||45===e.charCodeAt(r))&&r++,r<e.length&&s(e.charCodeAt(r))){for(r++;r<e.length&&s(e.charCodeAt(r));)r++;n=r}else h=3;return e.substring(t,n)}(),u=11;default:for(;r<n&&b(t);)r++,t=e.charCodeAt(r);if(c!==r){switch(a=e.substring(c,r)){case"true":return u=8;case"false":return u=9;case"null":return u=7}return u=16}return a+=String.fromCharCode(t),r++,u=16}}function b(e){if(o(e)||i(e))return!1;switch(e){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return!1}return!0}return{setPosition:function(e){r=e,a="",c=0,u=16,h=0},getPosition:function(){return r},scan:t?function(){var e;do{e=g()}while(e>=12&&e<=15);return e}:g,getToken:function(){return u},getTokenValue:function(){return a},getTokenOffset:function(){return c},getTokenLength:function(){return r-c},getTokenStartLine:function(){return f},getTokenStartCharacter:function(){return c-p},getTokenError:function(){return h}}}function o(e){return 32===e||9===e||11===e||12===e||160===e||5760===e||e>=8192&&e<=8203||8239===e||8287===e||12288===e||65279===e}function i(e){return 10===e||13===e||8232===e||8233===e}function s(e){return e>=48&&e<=57}function a(e,t,n){var o,i,s,a,l;if(t){for(a=t.offset,l=a+t.length,s=a;s>0&&!u(e,s-1);)s--;for(var f=l;f<e.length&&!u(e,f);)f++;i=e.substring(s,f),o=function(e,t){for(var n=0,r=0,o=t.tabSize||4;n<e.length;){var i=e.charAt(n);if(" "===i)r++;else{if("\t"!==i)break;r+=o}n++}return Math.floor(r/o)}(i,n)}else i=e,o=0,s=0,a=0,l=e.length;var d,p=function(e,t){for(var n=0;n<t.length;n++){var r=t.charAt(n);if("\r"===r)return n+1<t.length&&"\n"===t.charAt(n+1)?"\r\n":"\r";if("\n"===r)return"\n"}return e&&e.eol||"\n"}(n,e),h=!1,m=0;d=n.insertSpaces?c(" ",n.tabSize||4):"\t";var g=r(i,!1),b=!1;function v(){return p+c(d,o+m)}function y(){var e=g.scan();for(h=!1;15===e||14===e;)h=h||14===e,e=g.scan();return b=16===e||0!==g.getTokenError(),e}var x=[];function w(t,n,r){!b&&n<l&&r>a&&e.substring(n,r)!==t&&x.push({offset:n,length:r-n,content:t})}var k=y();if(17!==k){var C=g.getTokenOffset()+s;w(c(d,o),s,C)}for(;17!==k;){for(var _=g.getTokenOffset()+g.getTokenLength()+s,T=y(),S="";!h&&(12===T||13===T);)w(" ",_,g.getTokenOffset()+s),_=g.getTokenOffset()+g.getTokenLength()+s,S=12===T?v():"",T=y();if(2===T)1!==k&&(m--,S=v());else if(4===T)3!==k&&(m--,S=v());else{switch(k){case 3:case 1:m++,S=v();break;case 5:case 12:S=v();break;case 13:S=h?v():" ";break;case 6:S=" ";break;case 10:if(6===T){S="";break}case 7:case 8:case 9:case 11:case 2:case 4:12===T||13===T?S=" ":5!==T&&17!==T&&(b=!0);break;case 16:b=!0}!h||12!==T&&13!==T||(S=v())}w(S,_,g.getTokenOffset()+s),k=T}return x}function c(e,t){for(var n="",r=0;r<t;r++)n+=e;return n}function u(e,t){return-1!=="\r\n".indexOf(e.charAt(t))}var l;function f(e,t,n){void 0===t&&(t=[]),void 0===n&&(n=l.DEFAULT);var r={type:"array",offset:-1,length:-1,children:[],parent:void 0};function o(e){"property"===r.type&&(r.length=e-r.offset,r=r.parent)}function i(e){return r.children.push(e),e}p(e,{onObjectBegin:function(e){r=i({type:"object",offset:e,length:-1,parent:r,children:[]})},onObjectProperty:function(e,t,n){(r=i({type:"property",offset:t,length:-1,parent:r,children:[]})).children.push({type:"string",value:e,offset:t,length:n,parent:r})},onObjectEnd:function(e,t){o(e+t),r.length=e+t-r.offset,r=r.parent,o(e+t)},onArrayBegin:function(e,t){r=i({type:"array",offset:e,length:-1,parent:r,children:[]})},onArrayEnd:function(e,t){r.length=e+t-r.offset,r=r.parent,o(e+t)},onLiteralValue:function(e,t,n){i({type:h(e),offset:t,length:n,parent:r,value:e}),o(t+n)},onSeparator:function(e,t,n){"property"===r.type&&(":"===e?r.colonOffset=t:","===e&&o(t))},onError:function(e,n,r){t.push({error:e,offset:n,length:r})}},n);var s=r.children[0];return s&&delete s.parent,s}function d(e,t){if(e){for(var n=e,r=0,o=t;r<o.length;r++){var i=o[r];if("string"==typeof i){if("object"!==n.type||!Array.isArray(n.children))return;for(var s=!1,a=0,c=n.children;a<c.length;a++){var u=c[a];if(Array.isArray(u.children)&&u.children[0].value===i){n=u.children[1],s=!0;break}}if(!s)return}else{var l=i;if("array"!==n.type||l<0||!Array.isArray(n.children)||l>=n.children.length)return;n=n.children[l]}}return n}}function p(e,t,n){void 0===n&&(n=l.DEFAULT);var o=r(e,!1);function i(e){return e?function(){return e(o.getTokenOffset(),o.getTokenLength(),o.getTokenStartLine(),o.getTokenStartCharacter())}:function(){return!0}}function s(e){return e?function(t){return e(t,o.getTokenOffset(),o.getTokenLength(),o.getTokenStartLine(),o.getTokenStartCharacter())}:function(){return!0}}var a=i(t.onObjectBegin),c=s(t.onObjectProperty),u=i(t.onObjectEnd),f=i(t.onArrayBegin),d=i(t.onArrayEnd),p=s(t.onLiteralValue),h=s(t.onSeparator),m=i(t.onComment),g=s(t.onError),b=n&&n.disallowComments,v=n&&n.allowTrailingComma;function y(){for(;;){var e=o.scan();switch(o.getTokenError()){case 4:x(14);break;case 5:x(15);break;case 3:x(13);break;case 1:b||x(11);break;case 2:x(12);break;case 6:x(16)}switch(e){case 12:case 13:b?x(10):m();break;case 16:x(1);break;case 15:case 14:break;default:return e}}}function x(e,t,n){if(void 0===t&&(t=[]),void 0===n&&(n=[]),g(e),t.length+n.length>0)for(var r=o.getToken();17!==r;){if(-1!==t.indexOf(r)){y();break}if(-1!==n.indexOf(r))break;r=y()}}function w(e){var t=o.getTokenValue();return e?p(t):c(t),y(),!0}return y(),17===o.getToken()?!!n.allowEmptyContent||(x(4,[],[]),!1):function e(){switch(o.getToken()){case 3:return function(){f(),y();for(var t=!1;4!==o.getToken()&&17!==o.getToken();){if(5===o.getToken()){if(t||x(4,[],[]),h(","),y(),4===o.getToken()&&v)break}else t&&x(6,[],[]);e()||x(4,[],[4,5]),t=!0}return d(),4!==o.getToken()?x(8,[4],[]):y(),!0}();case 1:return function(){a(),y();for(var t=!1;2!==o.getToken()&&17!==o.getToken();){if(5===o.getToken()){if(t||x(4,[],[]),h(","),y(),2===o.getToken()&&v)break}else t&&x(6,[],[]);(10!==o.getToken()?(x(3,[],[2,5]),0):(w(!1),6===o.getToken()?(h(":"),y(),e()||x(4,[],[2,5])):x(5,[],[2,5]),1))||x(4,[],[2,5]),t=!0}return u(),2!==o.getToken()?x(7,[2],[]):y(),!0}();case 10:return w(!0);default:return function(){switch(o.getToken()){case 11:var e=o.getTokenValue(),t=Number(e);isNaN(t)&&(x(2),t=0),p(t);break;case 7:p(null);break;case 8:p(!0);break;case 9:p(!1);break;default:return!1}return y(),!0}()}}()?(17!==o.getToken()&&x(9,[],[]),!0):(x(4,[],[]),!1)}function h(e){switch(typeof e){case"boolean":return"boolean";case"number":return"number";case"string":return"string";case"object":return e?Array.isArray(e)?"array":"object":"null";default:return"null"}}function m(e,t,n){if(!n.formattingOptions)return[t];var r=g(e,t),o=t.offset,i=t.offset+t.content.length;if(0===t.length||0===t.content.length){for(;o>0&&!u(r,o-1);)o--;for(;i<r.length&&!u(r,i);)i++}for(var s=a(r,{offset:o,length:i-o},n.formattingOptions),c=s.length-1;c>=0;c--){var l=s[c];r=g(r,l),o=Math.min(o,l.offset),i=Math.max(i,l.offset+l.length),i+=l.content.length-l.length}return[{offset:o,length:e.length-(r.length-i)-o,content:r.substring(o,i)}]}function g(e,t){return e.substring(0,t.offset)+t.content+e.substring(t.offset+t.length)}n.r(t),n.d(t,{applyEdits:()=>j,createScanner:()=>b,findNodeAtLocation:()=>w,findNodeAtOffset:()=>k,format:()=>E,getLocation:()=>v,getNodePath:()=>C,getNodeValue:()=>_,modify:()=>A,parse:()=>y,parseTree:()=>x,printParseErrorCode:()=>O,stripComments:()=>S,visit:()=>T}),function(e){e.DEFAULT={allowTrailingComma:!1}}(l||(l={}));var b=r,v=function(e,t){var n=[],r=new Object,o=void 0,i={value:{},offset:0,length:0,type:"object",parent:void 0},s=!1;function a(e,t,n,r){i.value=e,i.offset=t,i.length=n,i.type=r,i.colonOffset=void 0,o=i}try{p(e,{onObjectBegin:function(e,i){if(t<=e)throw r;o=void 0,s=t>e,n.push("")},onObjectProperty:function(e,o,i){if(t<o)throw r;if(a(e,o,i,"property"),n[n.length-1]=e,t<=o+i)throw r},onObjectEnd:function(e,i){if(t<=e)throw r;o=void 0,n.pop()},onArrayBegin:function(e,i){if(t<=e)throw r;o=void 0,n.push(0)},onArrayEnd:function(e,i){if(t<=e)throw r;o=void 0,n.pop()},onLiteralValue:function(e,n,o){if(t<n)throw r;if(a(e,n,o,h(e)),t<=n+o)throw r},onSeparator:function(e,i,a){if(t<=i)throw r;if(":"===e&&o&&"property"===o.type)o.colonOffset=i,s=!1,o=void 0;else if(","===e){var c=n[n.length-1];"number"==typeof c?n[n.length-1]=c+1:(s=!0,n[n.length-1]=""),o=void 0}}})}catch(e){if(e!==r)throw e}return{path:n,previousNode:o,isAtPropertyKey:s,matches:function(e){for(var t=0,r=0;t<e.length&&r<n.length;r++)if(e[t]===n[r]||"*"===e[t])t++;else if("**"!==e[t])return!1;return t===e.length}}},y=function(e,t,n){void 0===t&&(t=[]),void 0===n&&(n=l.DEFAULT);var r=null,o=[],i=[];function s(e){Array.isArray(o)?o.push(e):null!==r&&(o[r]=e)}return p(e,{onObjectBegin:function(){var e={};s(e),i.push(o),o=e,r=null},onObjectProperty:function(e){r=e},onObjectEnd:function(){o=i.pop()},onArrayBegin:function(){var e=[];s(e),i.push(o),o=e,r=null},onArrayEnd:function(){o=i.pop()},onLiteralValue:s,onError:function(e,n,r){t.push({error:e,offset:n,length:r})}},n),o[0]},x=f,w=d,k=function e(t,n,r){if(void 0===r&&(r=!1),function(e,t,n){return void 0===n&&(n=!1),t>=e.offset&&t<e.offset+e.length||n&&t===e.offset+e.length}(t,n,r)){var o=t.children;if(Array.isArray(o))for(var i=0;i<o.length&&o[i].offset<=n;i++){var s=e(o[i],n,r);if(s)return s}return t}},C=function e(t){if(!t.parent||!t.parent.children)return[];var n=e(t.parent);if("property"===t.parent.type){var r=t.parent.children[0].value;n.push(r)}else if("array"===t.parent.type){var o=t.parent.children.indexOf(t);-1!==o&&n.push(o)}return n},_=function e(t){switch(t.type){case"array":return t.children.map(e);case"object":for(var n=Object.create(null),r=0,o=t.children;r<o.length;r++){var i=o[r],s=i.children[1];s&&(n[i.children[0].value]=e(s))}return n;case"null":case"string":case"number":case"boolean":return t.value;default:return}},T=p,S=function(e,t){var n,o,i=r(e),s=[],a=0;do{switch(o=i.getPosition(),n=i.scan()){case 12:case 13:case 17:a!==o&&s.push(e.substring(a,o)),void 0!==t&&s.push(i.getTokenValue().replace(/[^\r\n]/g,t)),a=i.getPosition()}}while(17!==n);return s.join("")};function O(e){switch(e){case 1:return"InvalidSymbol";case 2:return"InvalidNumberFormat";case 3:return"PropertyNameExpected";case 4:return"ValueExpected";case 5:return"ColonExpected";case 6:return"CommaExpected";case 7:return"CloseBraceExpected";case 8:return"CloseBracketExpected";case 9:return"EndOfFileExpected";case 10:return"InvalidCommentToken";case 11:return"UnexpectedEndOfComment";case 12:return"UnexpectedEndOfString";case 13:return"UnexpectedEndOfNumber";case 14:return"InvalidUnicode";case 15:return"InvalidEscapeCharacter";case 16:return"InvalidCharacter"}return"<unknown ParseErrorCode>"}function E(e,t,n){return a(e,t,n)}function A(e,t,n,r){return function(e,t,n,r){for(var o,i=t.slice(),s=f(e,[]),a=void 0,c=void 0;i.length>0&&(c=i.pop(),void 0===(a=d(s,i))&&void 0!==n);)"string"==typeof c?((o={})[c]=n,n=o):n=[n];if(a){if("object"===a.type&&"string"==typeof c&&Array.isArray(a.children)){var u=d(a,[c]);if(void 0!==u){if(void 0===n){if(!u.parent)throw new Error("Malformed AST");var l=a.children.indexOf(u.parent),p=void 0,h=u.parent.offset+u.parent.length;return l>0?p=(C=a.children[l-1]).offset+C.length:(p=a.offset+1,a.children.length>1&&(h=a.children[1].offset)),m(e,{offset:p,length:h-p,content:""},r)}return m(e,{offset:u.offset,length:u.length,content:JSON.stringify(n)},r)}if(void 0===n)return[];var g=JSON.stringify(c)+": "+JSON.stringify(n),b=void 0;return m(e,b=(_=r.getInsertionIndex?r.getInsertionIndex(a.children.map((function(e){return e.children[0].value}))):a.children.length)>0?{offset:(C=a.children[_-1]).offset+C.length,length:0,content:","+g}:0===a.children.length?{offset:a.offset+1,length:0,content:g}:{offset:a.offset+1,length:0,content:g+","},r)}if("array"===a.type&&"number"==typeof c&&Array.isArray(a.children)){var v=c;if(-1===v)return g=""+JSON.stringify(n),b=void 0,m(e,b=0===a.children.length?{offset:a.offset+1,length:0,content:g}:{offset:(C=a.children[a.children.length-1]).offset+C.length,length:0,content:","+g},r);if(void 0===n&&a.children.length>=0){var y=c,x=a.children[y];if(b=void 0,1===a.children.length)b={offset:a.offset+1,length:a.length-2,content:""};else if(a.children.length-1===y){var w=(C=a.children[y-1]).offset+C.length;b={offset:w,length:a.offset+a.length-2-w,content:""}}else b={offset:x.offset,length:a.children[y+1].offset-x.offset,content:""};return m(e,b,r)}if(void 0!==n){if(b=void 0,g=""+JSON.stringify(n),!r.isArrayInsertion&&a.children.length>c){var k=a.children[c];b={offset:k.offset,length:k.length,content:g}}else if(0===a.children.length||0===c)b={offset:a.offset+1,length:0,content:0===a.children.length?g:g+","};else{var C,_=c>a.children.length?a.children.length:c;b={offset:(C=a.children[_-1]).offset+C.length,length:0,content:","+g}}return m(e,b,r)}throw new Error("Can not "+(void 0===n?"remove":r.isArrayInsertion?"insert":"modify")+" Array index "+v+" as length is not sufficient")}throw new Error("Can not add "+("number"!=typeof c?"index":"property")+" to parent of type "+a.type)}if(void 0===n)throw new Error("Can not delete in empty document");return m(e,{offset:s?s.offset:0,length:s?s.length:0,content:JSON.stringify(n)},r)}(e,t,n,r)}function j(e,t){for(var n=t.length-1;n>=0;n--)e=g(e,t[n]);return e}},9792:(e,t,n)=>{var r=n(2017),o=n(4434).EventEmitter;function i(e){if(!(this instanceof i))return new i(e);o.call(this),e=e||{},this.concurrency=e.concurrency||1/0,this.timeout=e.timeout||0,this.autostart=e.autostart||!1,this.results=e.results||null,this.pending=0,this.session=0,this.running=!1,this.jobs=[],this.timers={}}function s(){for(var e in this.timers){var t=this.timers[e];delete this.timers[e],clearTimeout(t)}}function a(e){var t=this;function n(e){t.end(e)}this.on("error",n),this.on("end",(function r(o){t.removeListener("error",n),t.removeListener("end",r),e(o,this.results)}))}function c(e){this.session++,this.running=!1,this.emit("end",e)}e.exports=i,e.exports.default=i,r(i,o),["pop","shift","indexOf","lastIndexOf"].forEach((function(e){i.prototype[e]=function(){return Array.prototype[e].apply(this.jobs,arguments)}})),i.prototype.slice=function(e,t){return this.jobs=this.jobs.slice(e,t),this},i.prototype.reverse=function(){return this.jobs.reverse(),this},["push","unshift","splice"].forEach((function(e){i.prototype[e]=function(){var t=Array.prototype[e].apply(this.jobs,arguments);return this.autostart&&this.start(),t}})),Object.defineProperty(i.prototype,"length",{get:function(){return this.pending+this.jobs.length}}),i.prototype.start=function(e){if(e&&a.call(this,e),this.running=!0,!(this.pending>=this.concurrency))if(0!==this.jobs.length){var t=this,n=this.jobs.shift(),r=!0,o=this.session,i=null,s=!1,u=null,l=n.hasOwnProperty("timeout")?n.timeout:this.timeout;l&&(i=setTimeout((function(){s=!0,t.listeners("timeout").length>0?t.emit("timeout",d,n):d()}),l),this.timers[i]=i),this.results&&(u=this.results.length,this.results[u]=null),this.pending++,t.emit("start",n);var f=n(d);f&&f.then&&"function"==typeof f.then&&f.then((function(e){return d(null,e)})).catch((function(e){return d(e||!0)})),this.running&&this.jobs.length>0&&this.start()}else 0===this.pending&&c.call(this);function d(e,a){r&&t.session===o&&(r=!1,t.pending--,null!==i&&(delete t.timers[i],clearTimeout(i)),e?t.emit("error",e,n):!1===s&&(null!==u&&(t.results[u]=Array.prototype.slice.call(arguments,1)),t.emit("success",a,n)),t.session===o&&(0===t.pending&&0===t.jobs.length?c.call(t):t.running&&t.start()))}},i.prototype.stop=function(){this.running=!1},i.prototype.end=function(e){s.call(this),this.jobs.length=0,this.pending=0,c.call(this,e)}},2367:(e,t,n)=>{"use strict";n.r(t),n.d(t,{TextDocument:()=>o});class r{constructor(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){const t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content}update(e,t){for(let t of e)if(r.isIncremental(t)){const e=a(t.range),n=this.offsetAt(e.start),r=this.offsetAt(e.end);this._content=this._content.substring(0,n)+t.text+this._content.substring(r,this._content.length);const o=Math.max(e.start.line,0),i=Math.max(e.end.line,0);let c=this._lineOffsets;const u=s(t.text,!1,n);if(i-o===u.length)for(let e=0,t=u.length;e<t;e++)c[e+o+1]=u[e];else u.length<1e4?c.splice(o+1,i-o,...u):this._lineOffsets=c=c.slice(0,o+1).concat(u,c.slice(i+1));const l=t.text.length-(r-n);if(0!==l)for(let e=o+1+u.length,t=c.length;e<t;e++)c[e]=c[e]+l}else{if(!r.isFull(t))throw new Error("Unknown change event received");this._content=t.text,this._lineOffsets=void 0}this._version=t}getLineOffsets(){return void 0===this._lineOffsets&&(this._lineOffsets=s(this._content,!0)),this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let t=this.getLineOffsets(),n=0,r=t.length;if(0===r)return{line:0,character:e};for(;n<r;){let o=Math.floor((n+r)/2);t[o]>e?r=o:n=o+1}let o=n-1;return{line:o,character:e-t[o]}}offsetAt(e){let t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;let n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)}get lineCount(){return this.getLineOffsets().length}static isIncremental(e){let t=e;return null!=t&&"string"==typeof t.text&&void 0!==t.range&&(void 0===t.rangeLength||"number"==typeof t.rangeLength)}static isFull(e){let t=e;return null!=t&&"string"==typeof t.text&&void 0===t.range&&void 0===t.rangeLength}}var o;function i(e,t){if(e.length<=1)return e;const n=e.length/2|0,r=e.slice(0,n),o=e.slice(n);i(r,t),i(o,t);let s=0,a=0,c=0;for(;s<r.length&&a<o.length;){let n=t(r[s],o[a]);e[c++]=n<=0?r[s++]:o[a++]}for(;s<r.length;)e[c++]=r[s++];for(;a<o.length;)e[c++]=o[a++];return e}function s(e,t,n=0){const r=t?[n]:[];for(let t=0;t<e.length;t++){let o=e.charCodeAt(t);13!==o&&10!==o||(13===o&&t+1<e.length&&10===e.charCodeAt(t+1)&&t++,r.push(n+t+1))}return r}function a(e){const t=e.start,n=e.end;return t.line>n.line||t.line===n.line&&t.character>n.character?{start:n,end:t}:e}function c(e){const t=a(e.range);return t!==e.range?{newText:e.newText,range:t}:e}!function(e){e.create=function(e,t,n,o){return new r(e,t,n,o)},e.update=function(e,t,n){if(e instanceof r)return e.update(t,n),e;throw new Error("TextDocument.update: document must be created by TextDocument.create")},e.applyEdits=function(e,t){let n=e.getText(),r=i(t.map(c),((e,t)=>{let n=e.range.start.line-t.range.start.line;return 0===n?e.range.start.character-t.range.start.character:n})),o=0;const s=[];for(const t of r){let r=e.offsetAt(t.range.start);if(r<o)throw new Error("Overlapping edit");r>o&&s.push(n.substring(o,r)),t.newText.length&&s.push(t.newText),o=e.offsetAt(t.range.end)}return s.push(n.substr(o)),s.join("")}}(o||(o={}))},7806:(e,t,n)=>{"use strict";var r,o,i,s,a,c,u,l,f,d,p,h,m,g,b,v,y,x,w,k,C,_,T,S,O,E,A,j;n.r(t),n.d(t,{AnnotatedTextEdit:()=>T,ChangeAnnotation:()=>C,ChangeAnnotationIdentifier:()=>_,CodeAction:()=>ie,CodeActionContext:()=>oe,CodeActionKind:()=>ne,CodeActionTriggerKind:()=>re,CodeDescription:()=>y,CodeLens:()=>se,Color:()=>f,ColorInformation:()=>d,ColorPresentation:()=>p,Command:()=>w,CompletionItem:()=>q,CompletionItemKind:()=>L,CompletionItemLabelDetails:()=>U,CompletionItemTag:()=>D,CompletionList:()=>V,CreateFile:()=>O,DeleteFile:()=>A,Diagnostic:()=>x,DiagnosticRelatedInformation:()=>g,DiagnosticSeverity:()=>b,DiagnosticTag:()=>v,DocumentHighlight:()=>K,DocumentHighlightKind:()=>Q,DocumentLink:()=>ce,DocumentSymbol:()=>te,DocumentUri:()=>r,EOL:()=>Te,FoldingRange:()=>m,FoldingRangeKind:()=>h,FormattingOptions:()=>ae,Hover:()=>H,InlayHint:()=>ye,InlayHintKind:()=>be,InlayHintLabelPart:()=>ve,InlineValueContext:()=>ge,InlineValueEvaluatableExpression:()=>me,InlineValueText:()=>pe,InlineValueVariableLookup:()=>he,InsertReplaceEdit:()=>F,InsertTextFormat:()=>z,InsertTextMode:()=>B,Location:()=>u,LocationLink:()=>l,MarkedString:()=>W,MarkupContent:()=>N,MarkupKind:()=>M,OptionalVersionedTextDocumentIdentifier:()=>$,ParameterInformation:()=>G,Position:()=>a,Range:()=>c,RenameFile:()=>E,SelectionRange:()=>ue,SemanticTokenModifiers:()=>fe,SemanticTokenTypes:()=>le,SemanticTokens:()=>de,SignatureInformation:()=>J,SymbolInformation:()=>Y,SymbolKind:()=>X,SymbolTag:()=>Z,TextDocument:()=>_e,TextDocumentEdit:()=>S,TextDocumentIdentifier:()=>P,TextDocumentItem:()=>R,TextEdit:()=>k,URI:()=>o,VersionedTextDocumentIdentifier:()=>I,WorkspaceChange:()=>Ce,WorkspaceEdit:()=>j,WorkspaceFolder:()=>xe,WorkspaceSymbol:()=>ee,integer:()=>i,uinteger:()=>s}),function(e){e.is=function(e){return"string"==typeof e}}(r||(r={})),function(e){e.is=function(e){return"string"==typeof e}}(o||(o={})),function(e){e.MIN_VALUE=-2147483648,e.MAX_VALUE=2147483647,e.is=function(t){return"number"==typeof t&&e.MIN_VALUE<=t&&t<=e.MAX_VALUE}}(i||(i={})),function(e){e.MIN_VALUE=0,e.MAX_VALUE=2147483647,e.is=function(t){return"number"==typeof t&&e.MIN_VALUE<=t&&t<=e.MAX_VALUE}}(s||(s={})),function(e){e.create=function(e,t){return e===Number.MAX_VALUE&&(e=s.MAX_VALUE),t===Number.MAX_VALUE&&(t=s.MAX_VALUE),{line:e,character:t}},e.is=function(e){var t=e;return Se.objectLiteral(t)&&Se.uinteger(t.line)&&Se.uinteger(t.character)}}(a||(a={})),function(e){e.create=function(e,t,n,r){if(Se.uinteger(e)&&Se.uinteger(t)&&Se.uinteger(n)&&Se.uinteger(r))return{start:a.create(e,t),end:a.create(n,r)};if(a.is(e)&&a.is(t))return{start:e,end:t};throw new Error("Range#create called with invalid arguments[".concat(e,", ").concat(t,", ").concat(n,", ").concat(r,"]"))},e.is=function(e){var t=e;return Se.objectLiteral(t)&&a.is(t.start)&&a.is(t.end)}}(c||(c={})),function(e){e.create=function(e,t){return{uri:e,range:t}},e.is=function(e){var t=e;return Se.objectLiteral(t)&&c.is(t.range)&&(Se.string(t.uri)||Se.undefined(t.uri))}}(u||(u={})),function(e){e.create=function(e,t,n,r){return{targetUri:e,targetRange:t,targetSelectionRange:n,originSelectionRange:r}},e.is=function(e){var t=e;return Se.objectLiteral(t)&&c.is(t.targetRange)&&Se.string(t.targetUri)&&c.is(t.targetSelectionRange)&&(c.is(t.originSelectionRange)||Se.undefined(t.originSelectionRange))}}(l||(l={})),function(e){e.create=function(e,t,n,r){return{red:e,green:t,blue:n,alpha:r}},e.is=function(e){var t=e;return Se.objectLiteral(t)&&Se.numberRange(t.red,0,1)&&Se.numberRange(t.green,0,1)&&Se.numberRange(t.blue,0,1)&&Se.numberRange(t.alpha,0,1)}}(f||(f={})),function(e){e.create=function(e,t){return{range:e,color:t}},e.is=function(e){var t=e;return Se.objectLiteral(t)&&c.is(t.range)&&f.is(t.color)}}(d||(d={})),function(e){e.create=function(e,t,n){return{label:e,textEdit:t,additionalTextEdits:n}},e.is=function(e){var t=e;return Se.objectLiteral(t)&&Se.string(t.label)&&(Se.undefined(t.textEdit)||k.is(t))&&(Se.undefined(t.additionalTextEdits)||Se.typedArray(t.additionalTextEdits,k.is))}}(p||(p={})),function(e){e.Comment="comment",e.Imports="imports",e.Region="region"}(h||(h={})),function(e){e.create=function(e,t,n,r,o,i){var s={startLine:e,endLine:t};return Se.defined(n)&&(s.startCharacter=n),Se.defined(r)&&(s.endCharacter=r),Se.defined(o)&&(s.kind=o),Se.defined(i)&&(s.collapsedText=i),s},e.is=function(e){var t=e;return Se.objectLiteral(t)&&Se.uinteger(t.startLine)&&Se.uinteger(t.startLine)&&(Se.undefined(t.startCharacter)||Se.uinteger(t.startCharacter))&&(Se.undefined(t.endCharacter)||Se.uinteger(t.endCharacter))&&(Se.undefined(t.kind)||Se.string(t.kind))}}(m||(m={})),function(e){e.create=function(e,t){return{location:e,message:t}},e.is=function(e){var t=e;return Se.defined(t)&&u.is(t.location)&&Se.string(t.message)}}(g||(g={})),function(e){e.Error=1,e.Warning=2,e.Information=3,e.Hint=4}(b||(b={})),function(e){e.Unnecessary=1,e.Deprecated=2}(v||(v={})),function(e){e.is=function(e){var t=e;return Se.objectLiteral(t)&&Se.string(t.href)}}(y||(y={})),function(e){e.create=function(e,t,n,r,o,i){var s={range:e,message:t};return Se.defined(n)&&(s.severity=n),Se.defined(r)&&(s.code=r),Se.defined(o)&&(s.source=o),Se.defined(i)&&(s.relatedInformation=i),s},e.is=function(e){var t,n=e;return Se.defined(n)&&c.is(n.range)&&Se.string(n.message)&&(Se.number(n.severity)||Se.undefined(n.severity))&&(Se.integer(n.code)||Se.string(n.code)||Se.undefined(n.code))&&(Se.undefined(n.codeDescription)||Se.string(null===(t=n.codeDescription)||void 0===t?void 0:t.href))&&(Se.string(n.source)||Se.undefined(n.source))&&(Se.undefined(n.relatedInformation)||Se.typedArray(n.relatedInformation,g.is))}}(x||(x={})),function(e){e.create=function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var o={title:e,command:t};return Se.defined(n)&&n.length>0&&(o.arguments=n),o},e.is=function(e){var t=e;return Se.defined(t)&&Se.string(t.title)&&Se.string(t.command)}}(w||(w={})),function(e){e.replace=function(e,t){return{range:e,newText:t}},e.insert=function(e,t){return{range:{start:e,end:e},newText:t}},e.del=function(e){return{range:e,newText:""}},e.is=function(e){var t=e;return Se.objectLiteral(t)&&Se.string(t.newText)&&c.is(t.range)}}(k||(k={})),function(e){e.create=function(e,t,n){var r={label:e};return void 0!==t&&(r.needsConfirmation=t),void 0!==n&&(r.description=n),r},e.is=function(e){var t=e;return Se.objectLiteral(t)&&Se.string(t.label)&&(Se.boolean(t.needsConfirmation)||void 0===t.needsConfirmation)&&(Se.string(t.description)||void 0===t.description)}}(C||(C={})),function(e){e.is=function(e){var t=e;return Se.string(t)}}(_||(_={})),function(e){e.replace=function(e,t,n){return{range:e,newText:t,annotationId:n}},e.insert=function(e,t,n){return{range:{start:e,end:e},newText:t,annotationId:n}},e.del=function(e,t){return{range:e,newText:"",annotationId:t}},e.is=function(e){var t=e;return k.is(t)&&(C.is(t.annotationId)||_.is(t.annotationId))}}(T||(T={})),function(e){e.create=function(e,t){return{textDocument:e,edits:t}},e.is=function(e){var t=e;return Se.defined(t)&&$.is(t.textDocument)&&Array.isArray(t.edits)}}(S||(S={})),function(e){e.create=function(e,t,n){var r={kind:"create",uri:e};return void 0===t||void 0===t.overwrite&&void 0===t.ignoreIfExists||(r.options=t),void 0!==n&&(r.annotationId=n),r},e.is=function(e){var t=e;return t&&"create"===t.kind&&Se.string(t.uri)&&(void 0===t.options||(void 0===t.options.overwrite||Se.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||Se.boolean(t.options.ignoreIfExists)))&&(void 0===t.annotationId||_.is(t.annotationId))}}(O||(O={})),function(e){e.create=function(e,t,n,r){var o={kind:"rename",oldUri:e,newUri:t};return void 0===n||void 0===n.overwrite&&void 0===n.ignoreIfExists||(o.options=n),void 0!==r&&(o.annotationId=r),o},e.is=function(e){var t=e;return t&&"rename"===t.kind&&Se.string(t.oldUri)&&Se.string(t.newUri)&&(void 0===t.options||(void 0===t.options.overwrite||Se.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||Se.boolean(t.options.ignoreIfExists)))&&(void 0===t.annotationId||_.is(t.annotationId))}}(E||(E={})),function(e){e.create=function(e,t,n){var r={kind:"delete",uri:e};return void 0===t||void 0===t.recursive&&void 0===t.ignoreIfNotExists||(r.options=t),void 0!==n&&(r.annotationId=n),r},e.is=function(e){var t=e;return t&&"delete"===t.kind&&Se.string(t.uri)&&(void 0===t.options||(void 0===t.options.recursive||Se.boolean(t.options.recursive))&&(void 0===t.options.ignoreIfNotExists||Se.boolean(t.options.ignoreIfNotExists)))&&(void 0===t.annotationId||_.is(t.annotationId))}}(A||(A={})),function(e){e.is=function(e){var t=e;return t&&(void 0!==t.changes||void 0!==t.documentChanges)&&(void 0===t.documentChanges||t.documentChanges.every((function(e){return Se.string(e.kind)?O.is(e)||E.is(e)||A.is(e):S.is(e)})))}}(j||(j={}));var P,I,$,R,M,N,L,z,D,F,B,U,q,V,W,H,G,J,Q,K,X,Z,Y,ee,te,ne,re,oe,ie,se,ae,ce,ue,le,fe,de,pe,he,me,ge,be,ve,ye,xe,we=function(){function e(e,t){this.edits=e,this.changeAnnotations=t}return e.prototype.insert=function(e,t,n){var r,o;if(void 0===n?r=k.insert(e,t):_.is(n)?(o=n,r=T.insert(e,t,n)):(this.assertChangeAnnotations(this.changeAnnotations),o=this.changeAnnotations.manage(n),r=T.insert(e,t,o)),this.edits.push(r),void 0!==o)return o},e.prototype.replace=function(e,t,n){var r,o;if(void 0===n?r=k.replace(e,t):_.is(n)?(o=n,r=T.replace(e,t,n)):(this.assertChangeAnnotations(this.changeAnnotations),o=this.changeAnnotations.manage(n),r=T.replace(e,t,o)),this.edits.push(r),void 0!==o)return o},e.prototype.delete=function(e,t){var n,r;if(void 0===t?n=k.del(e):_.is(t)?(r=t,n=T.del(e,t)):(this.assertChangeAnnotations(this.changeAnnotations),r=this.changeAnnotations.manage(t),n=T.del(e,r)),this.edits.push(n),void 0!==r)return r},e.prototype.add=function(e){this.edits.push(e)},e.prototype.all=function(){return this.edits},e.prototype.clear=function(){this.edits.splice(0,this.edits.length)},e.prototype.assertChangeAnnotations=function(e){if(void 0===e)throw new Error("Text edit change is not configured to manage change annotations.")},e}(),ke=function(){function e(e){this._annotations=void 0===e?Object.create(null):e,this._counter=0,this._size=0}return e.prototype.all=function(){return this._annotations},Object.defineProperty(e.prototype,"size",{get:function(){return this._size},enumerable:!1,configurable:!0}),e.prototype.manage=function(e,t){var n;if(_.is(e)?n=e:(n=this.nextId(),t=e),void 0!==this._annotations[n])throw new Error("Id ".concat(n," is already in use."));if(void 0===t)throw new Error("No annotation provided for id ".concat(n));return this._annotations[n]=t,this._size++,n},e.prototype.nextId=function(){return this._counter++,this._counter.toString()},e}(),Ce=function(){function e(e){var t=this;this._textEditChanges=Object.create(null),void 0!==e?(this._workspaceEdit=e,e.documentChanges?(this._changeAnnotations=new ke(e.changeAnnotations),e.changeAnnotations=this._changeAnnotations.all(),e.documentChanges.forEach((function(e){if(S.is(e)){var n=new we(e.edits,t._changeAnnotations);t._textEditChanges[e.textDocument.uri]=n}}))):e.changes&&Object.keys(e.changes).forEach((function(n){var r=new we(e.changes[n]);t._textEditChanges[n]=r}))):this._workspaceEdit={}}return Object.defineProperty(e.prototype,"edit",{get:function(){return this.initDocumentChanges(),void 0!==this._changeAnnotations&&(0===this._changeAnnotations.size?this._workspaceEdit.changeAnnotations=void 0:this._workspaceEdit.changeAnnotations=this._changeAnnotations.all()),this._workspaceEdit},enumerable:!1,configurable:!0}),e.prototype.getTextEditChange=function(e){if($.is(e)){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var t={uri:e.uri,version:e.version};if(!(r=this._textEditChanges[t.uri])){var n={textDocument:t,edits:o=[]};this._workspaceEdit.documentChanges.push(n),r=new we(o,this._changeAnnotations),this._textEditChanges[t.uri]=r}return r}if(this.initChanges(),void 0===this._workspaceEdit.changes)throw new Error("Workspace edit is not configured for normal text edit changes.");var r;if(!(r=this._textEditChanges[e])){var o=[];this._workspaceEdit.changes[e]=o,r=new we(o),this._textEditChanges[e]=r}return r},e.prototype.initDocumentChanges=function(){void 0===this._workspaceEdit.documentChanges&&void 0===this._workspaceEdit.changes&&(this._changeAnnotations=new ke,this._workspaceEdit.documentChanges=[],this._workspaceEdit.changeAnnotations=this._changeAnnotations.all())},e.prototype.initChanges=function(){void 0===this._workspaceEdit.documentChanges&&void 0===this._workspaceEdit.changes&&(this._workspaceEdit.changes=Object.create(null))},e.prototype.createFile=function(e,t,n){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var r,o,i;if(C.is(t)||_.is(t)?r=t:n=t,void 0===r?o=O.create(e,n):(i=_.is(r)?r:this._changeAnnotations.manage(r),o=O.create(e,n,i)),this._workspaceEdit.documentChanges.push(o),void 0!==i)return i},e.prototype.renameFile=function(e,t,n,r){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var o,i,s;if(C.is(n)||_.is(n)?o=n:r=n,void 0===o?i=E.create(e,t,r):(s=_.is(o)?o:this._changeAnnotations.manage(o),i=E.create(e,t,r,s)),this._workspaceEdit.documentChanges.push(i),void 0!==s)return s},e.prototype.deleteFile=function(e,t,n){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var r,o,i;if(C.is(t)||_.is(t)?r=t:n=t,void 0===r?o=A.create(e,n):(i=_.is(r)?r:this._changeAnnotations.manage(r),o=A.create(e,n,i)),this._workspaceEdit.documentChanges.push(o),void 0!==i)return i},e}();!function(e){e.create=function(e){return{uri:e}},e.is=function(e){var t=e;return Se.defined(t)&&Se.string(t.uri)}}(P||(P={})),function(e){e.create=function(e,t){return{uri:e,version:t}},e.is=function(e){var t=e;return Se.defined(t)&&Se.string(t.uri)&&Se.integer(t.version)}}(I||(I={})),function(e){e.create=function(e,t){return{uri:e,version:t}},e.is=function(e){var t=e;return Se.defined(t)&&Se.string(t.uri)&&(null===t.version||Se.integer(t.version))}}($||($={})),function(e){e.create=function(e,t,n,r){return{uri:e,languageId:t,version:n,text:r}},e.is=function(e){var t=e;return Se.defined(t)&&Se.string(t.uri)&&Se.string(t.languageId)&&Se.integer(t.version)&&Se.string(t.text)}}(R||(R={})),function(e){e.PlainText="plaintext",e.Markdown="markdown",e.is=function(t){var n=t;return n===e.PlainText||n===e.Markdown}}(M||(M={})),function(e){e.is=function(e){var t=e;return Se.objectLiteral(e)&&M.is(t.kind)&&Se.string(t.value)}}(N||(N={})),function(e){e.Text=1,e.Method=2,e.Function=3,e.Constructor=4,e.Field=5,e.Variable=6,e.Class=7,e.Interface=8,e.Module=9,e.Property=10,e.Unit=11,e.Value=12,e.Enum=13,e.Keyword=14,e.Snippet=15,e.Color=16,e.File=17,e.Reference=18,e.Folder=19,e.EnumMember=20,e.Constant=21,e.Struct=22,e.Event=23,e.Operator=24,e.TypeParameter=25}(L||(L={})),function(e){e.PlainText=1,e.Snippet=2}(z||(z={})),function(e){e.Deprecated=1}(D||(D={})),function(e){e.create=function(e,t,n){return{newText:e,insert:t,replace:n}},e.is=function(e){var t=e;return t&&Se.string(t.newText)&&c.is(t.insert)&&c.is(t.replace)}}(F||(F={})),function(e){e.asIs=1,e.adjustIndentation=2}(B||(B={})),function(e){e.is=function(e){var t=e;return t&&(Se.string(t.detail)||void 0===t.detail)&&(Se.string(t.description)||void 0===t.description)}}(U||(U={})),function(e){e.create=function(e){return{label:e}}}(q||(q={})),function(e){e.create=function(e,t){return{items:e||[],isIncomplete:!!t}}}(V||(V={})),function(e){e.fromPlainText=function(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")},e.is=function(e){var t=e;return Se.string(t)||Se.objectLiteral(t)&&Se.string(t.language)&&Se.string(t.value)}}(W||(W={})),function(e){e.is=function(e){var t=e;return!!t&&Se.objectLiteral(t)&&(N.is(t.contents)||W.is(t.contents)||Se.typedArray(t.contents,W.is))&&(void 0===e.range||c.is(e.range))}}(H||(H={})),function(e){e.create=function(e,t){return t?{label:e,documentation:t}:{label:e}}}(G||(G={})),function(e){e.create=function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var o={label:e};return Se.defined(t)&&(o.documentation=t),Se.defined(n)?o.parameters=n:o.parameters=[],o}}(J||(J={})),function(e){e.Text=1,e.Read=2,e.Write=3}(Q||(Q={})),function(e){e.create=function(e,t){var n={range:e};return Se.number(t)&&(n.kind=t),n}}(K||(K={})),function(e){e.File=1,e.Module=2,e.Namespace=3,e.Package=4,e.Class=5,e.Method=6,e.Property=7,e.Field=8,e.Constructor=9,e.Enum=10,e.Interface=11,e.Function=12,e.Variable=13,e.Constant=14,e.String=15,e.Number=16,e.Boolean=17,e.Array=18,e.Object=19,e.Key=20,e.Null=21,e.EnumMember=22,e.Struct=23,e.Event=24,e.Operator=25,e.TypeParameter=26}(X||(X={})),function(e){e.Deprecated=1}(Z||(Z={})),function(e){e.create=function(e,t,n,r,o){var i={name:e,kind:t,location:{uri:r,range:n}};return o&&(i.containerName=o),i}}(Y||(Y={})),function(e){e.create=function(e,t,n,r){return void 0!==r?{name:e,kind:t,location:{uri:n,range:r}}:{name:e,kind:t,location:{uri:n}}}}(ee||(ee={})),function(e){e.create=function(e,t,n,r,o,i){var s={name:e,detail:t,kind:n,range:r,selectionRange:o};return void 0!==i&&(s.children=i),s},e.is=function(e){var t=e;return t&&Se.string(t.name)&&Se.number(t.kind)&&c.is(t.range)&&c.is(t.selectionRange)&&(void 0===t.detail||Se.string(t.detail))&&(void 0===t.deprecated||Se.boolean(t.deprecated))&&(void 0===t.children||Array.isArray(t.children))&&(void 0===t.tags||Array.isArray(t.tags))}}(te||(te={})),function(e){e.Empty="",e.QuickFix="quickfix",e.Refactor="refactor",e.RefactorExtract="refactor.extract",e.RefactorInline="refactor.inline",e.RefactorRewrite="refactor.rewrite",e.Source="source",e.SourceOrganizeImports="source.organizeImports",e.SourceFixAll="source.fixAll"}(ne||(ne={})),function(e){e.Invoked=1,e.Automatic=2}(re||(re={})),function(e){e.create=function(e,t,n){var r={diagnostics:e};return null!=t&&(r.only=t),null!=n&&(r.triggerKind=n),r},e.is=function(e){var t=e;return Se.defined(t)&&Se.typedArray(t.diagnostics,x.is)&&(void 0===t.only||Se.typedArray(t.only,Se.string))&&(void 0===t.triggerKind||t.triggerKind===re.Invoked||t.triggerKind===re.Automatic)}}(oe||(oe={})),function(e){e.create=function(e,t,n){var r={title:e},o=!0;return"string"==typeof t?(o=!1,r.kind=t):w.is(t)?r.command=t:r.edit=t,o&&void 0!==n&&(r.kind=n),r},e.is=function(e){var t=e;return t&&Se.string(t.title)&&(void 0===t.diagnostics||Se.typedArray(t.diagnostics,x.is))&&(void 0===t.kind||Se.string(t.kind))&&(void 0!==t.edit||void 0!==t.command)&&(void 0===t.command||w.is(t.command))&&(void 0===t.isPreferred||Se.boolean(t.isPreferred))&&(void 0===t.edit||j.is(t.edit))}}(ie||(ie={})),function(e){e.create=function(e,t){var n={range:e};return Se.defined(t)&&(n.data=t),n},e.is=function(e){var t=e;return Se.defined(t)&&c.is(t.range)&&(Se.undefined(t.command)||w.is(t.command))}}(se||(se={})),function(e){e.create=function(e,t){return{tabSize:e,insertSpaces:t}},e.is=function(e){var t=e;return Se.defined(t)&&Se.uinteger(t.tabSize)&&Se.boolean(t.insertSpaces)}}(ae||(ae={})),function(e){e.create=function(e,t,n){return{range:e,target:t,data:n}},e.is=function(e){var t=e;return Se.defined(t)&&c.is(t.range)&&(Se.undefined(t.target)||Se.string(t.target))}}(ce||(ce={})),function(e){e.create=function(e,t){return{range:e,parent:t}},e.is=function(t){var n=t;return Se.objectLiteral(n)&&c.is(n.range)&&(void 0===n.parent||e.is(n.parent))}}(ue||(ue={})),function(e){e.namespace="namespace",e.type="type",e.class="class",e.enum="enum",e.interface="interface",e.struct="struct",e.typeParameter="typeParameter",e.parameter="parameter",e.variable="variable",e.property="property",e.enumMember="enumMember",e.event="event",e.function="function",e.method="method",e.macro="macro",e.keyword="keyword",e.modifier="modifier",e.comment="comment",e.string="string",e.number="number",e.regexp="regexp",e.operator="operator",e.decorator="decorator"}(le||(le={})),function(e){e.declaration="declaration",e.definition="definition",e.readonly="readonly",e.static="static",e.deprecated="deprecated",e.abstract="abstract",e.async="async",e.modification="modification",e.documentation="documentation",e.defaultLibrary="defaultLibrary"}(fe||(fe={})),function(e){e.is=function(e){var t=e;return Se.objectLiteral(t)&&(void 0===t.resultId||"string"==typeof t.resultId)&&Array.isArray(t.data)&&(0===t.data.length||"number"==typeof t.data[0])}}(de||(de={})),function(e){e.create=function(e,t){return{range:e,text:t}},e.is=function(e){var t=e;return null!=t&&c.is(t.range)&&Se.string(t.text)}}(pe||(pe={})),function(e){e.create=function(e,t,n){return{range:e,variableName:t,caseSensitiveLookup:n}},e.is=function(e){var t=e;return null!=t&&c.is(t.range)&&Se.boolean(t.caseSensitiveLookup)&&(Se.string(t.variableName)||void 0===t.variableName)}}(he||(he={})),function(e){e.create=function(e,t){return{range:e,expression:t}},e.is=function(e){var t=e;return null!=t&&c.is(t.range)&&(Se.string(t.expression)||void 0===t.expression)}}(me||(me={})),function(e){e.create=function(e,t){return{frameId:e,stoppedLocation:t}},e.is=function(e){var t=e;return Se.defined(t)&&c.is(e.stoppedLocation)}}(ge||(ge={})),function(e){e.Type=1,e.Parameter=2,e.is=function(e){return 1===e||2===e}}(be||(be={})),function(e){e.create=function(e){return{value:e}},e.is=function(e){var t=e;return Se.objectLiteral(t)&&(void 0===t.tooltip||Se.string(t.tooltip)||N.is(t.tooltip))&&(void 0===t.location||u.is(t.location))&&(void 0===t.command||w.is(t.command))}}(ve||(ve={})),function(e){e.create=function(e,t,n){var r={position:e,label:t};return void 0!==n&&(r.kind=n),r},e.is=function(e){var t=e;return Se.objectLiteral(t)&&a.is(t.position)&&(Se.string(t.label)||Se.typedArray(t.label,ve.is))&&(void 0===t.kind||be.is(t.kind))&&void 0===t.textEdits||Se.typedArray(t.textEdits,k.is)&&(void 0===t.tooltip||Se.string(t.tooltip)||N.is(t.tooltip))&&(void 0===t.paddingLeft||Se.boolean(t.paddingLeft))&&(void 0===t.paddingRight||Se.boolean(t.paddingRight))}}(ye||(ye={})),function(e){e.is=function(e){var t=e;return Se.objectLiteral(t)&&o.is(t.uri)&&Se.string(t.name)}}(xe||(xe={}));var _e,Te=["\n","\r\n","\r"];!function(e){function t(e,n){if(e.length<=1)return e;var r=e.length/2|0,o=e.slice(0,r),i=e.slice(r);t(o,n),t(i,n);for(var s=0,a=0,c=0;s<o.length&&a<i.length;){var u=n(o[s],i[a]);e[c++]=u<=0?o[s++]:i[a++]}for(;s<o.length;)e[c++]=o[s++];for(;a<i.length;)e[c++]=i[a++];return e}e.create=function(e,t,n,r){return new Oe(e,t,n,r)},e.is=function(e){var t=e;return!!(Se.defined(t)&&Se.string(t.uri)&&(Se.undefined(t.languageId)||Se.string(t.languageId))&&Se.uinteger(t.lineCount)&&Se.func(t.getText)&&Se.func(t.positionAt)&&Se.func(t.offsetAt))},e.applyEdits=function(e,n){for(var r=e.getText(),o=t(n,(function(e,t){var n=e.range.start.line-t.range.start.line;return 0===n?e.range.start.character-t.range.start.character:n})),i=r.length,s=o.length-1;s>=0;s--){var a=o[s],c=e.offsetAt(a.range.start),u=e.offsetAt(a.range.end);if(!(u<=i))throw new Error("Overlapping edit");r=r.substring(0,c)+a.newText+r.substring(u,r.length),i=c}return r}}(_e||(_e={}));var Se,Oe=function(){function e(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}return Object.defineProperty(e.prototype,"uri",{get:function(){return this._uri},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"languageId",{get:function(){return this._languageId},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"version",{get:function(){return this._version},enumerable:!1,configurable:!0}),e.prototype.getText=function(e){if(e){var t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content},e.prototype.update=function(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0},e.prototype.getLineOffsets=function(){if(void 0===this._lineOffsets){for(var e=[],t=this._content,n=!0,r=0;r<t.length;r++){n&&(e.push(r),n=!1);var o=t.charAt(r);n="\r"===o||"\n"===o,"\r"===o&&r+1<t.length&&"\n"===t.charAt(r+1)&&r++}n&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets},e.prototype.positionAt=function(e){e=Math.max(Math.min(e,this._content.length),0);var t=this.getLineOffsets(),n=0,r=t.length;if(0===r)return a.create(0,e);for(;n<r;){var o=Math.floor((n+r)/2);t[o]>e?r=o:n=o+1}var i=n-1;return a.create(i,e-t[i])},e.prototype.offsetAt=function(e){var t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;var n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)},Object.defineProperty(e.prototype,"lineCount",{get:function(){return this.getLineOffsets().length},enumerable:!1,configurable:!0}),e}();!function(e){var t=Object.prototype.toString;e.defined=function(e){return void 0!==e},e.undefined=function(e){return void 0===e},e.boolean=function(e){return!0===e||!1===e},e.string=function(e){return"[object String]"===t.call(e)},e.number=function(e){return"[object Number]"===t.call(e)},e.numberRange=function(e,n,r){return"[object Number]"===t.call(e)&&n<=e&&e<=r},e.integer=function(e){return"[object Number]"===t.call(e)&&-2147483648<=e&&e<=2147483647},e.uinteger=function(e){return"[object Number]"===t.call(e)&&0<=e&&e<=2147483647},e.func=function(e){return"[object Function]"===t.call(e)},e.objectLiteral=function(e){return null!==e&&"object"==typeof e},e.typedArray=function(e,t){return Array.isArray(e)&&e.every(t)}}(Se||(Se={}))},4756:(e,t,n)=>{"use strict";n.r(t),n.d(t,{URI:()=>h,uriToFsPath:()=>x});var r,o,i,s=(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},r(e,t)},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});if("object"==typeof process)i="win32"===process.platform;else if("object"==typeof navigator){var a=navigator.userAgent;i=a.indexOf("Windows")>=0}var c=/^\w[\w\d+.-]*$/,u=/^\//,l=/^\/\//,f="",d="/",p=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,h=function(){function e(e,t,n,r,o,i){void 0===i&&(i=!1),"object"==typeof e?(this.scheme=e.scheme||f,this.authority=e.authority||f,this.path=e.path||f,this.query=e.query||f,this.fragment=e.fragment||f):(this.scheme=function(e,t){return e||t?e:"file"}(e,i),this.authority=t||f,this.path=function(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==d&&(t=d+t):t=d}return t}(this.scheme,n||f),this.query=r||f,this.fragment=o||f,function(e,t){if(!e.scheme&&t)throw new Error('[UriError]: Scheme is missing: {scheme: "", authority: "'+e.authority+'", path: "'+e.path+'", query: "'+e.query+'", fragment: "'+e.fragment+'"}');if(e.scheme&&!c.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path)if(e.authority){if(!u.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(l.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}(this,i))}return e.isUri=function(t){return t instanceof e||!!t&&"string"==typeof t.authority&&"string"==typeof t.fragment&&"string"==typeof t.path&&"string"==typeof t.query&&"string"==typeof t.scheme&&"function"==typeof t.fsPath&&"function"==typeof t.with&&"function"==typeof t.toString},Object.defineProperty(e.prototype,"fsPath",{get:function(){return x(this,!1)},enumerable:!0,configurable:!0}),e.prototype.with=function(e){if(!e)return this;var t=e.scheme,n=e.authority,r=e.path,o=e.query,i=e.fragment;return void 0===t?t=this.scheme:null===t&&(t=f),void 0===n?n=this.authority:null===n&&(n=f),void 0===r?r=this.path:null===r&&(r=f),void 0===o?o=this.query:null===o&&(o=f),void 0===i?i=this.fragment:null===i&&(i=f),t===this.scheme&&n===this.authority&&r===this.path&&o===this.query&&i===this.fragment?this:new g(t,n,r,o,i)},e.parse=function(e,t){void 0===t&&(t=!1);var n=p.exec(e);return n?new g(n[2]||f,_(n[4]||f),_(n[5]||f),_(n[7]||f),_(n[9]||f),t):new g(f,f,f,f,f)},e.file=function(e){var t=f;if(i&&(e=e.replace(/\\/g,d)),e[0]===d&&e[1]===d){var n=e.indexOf(d,2);-1===n?(t=e.substring(2),e=d):(t=e.substring(2,n),e=e.substring(n)||d)}return new g("file",t,e,f,f)},e.from=function(e){return new g(e.scheme,e.authority,e.path,e.query,e.fragment)},e.prototype.toString=function(e){return void 0===e&&(e=!1),w(this,e)},e.prototype.toJSON=function(){return this},e.revive=function(t){if(t){if(t instanceof e)return t;var n=new g(t);return n._formatted=t.external,n._fsPath=t._sep===m?t.fsPath:null,n}return t},e}(),m=i?1:void 0,g=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._formatted=null,t._fsPath=null,t}return s(t,e),Object.defineProperty(t.prototype,"fsPath",{get:function(){return this._fsPath||(this._fsPath=x(this,!1)),this._fsPath},enumerable:!0,configurable:!0}),t.prototype.toString=function(e){return void 0===e&&(e=!1),e?w(this,!0):(this._formatted||(this._formatted=w(this,!1)),this._formatted)},t.prototype.toJSON=function(){var e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=m),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e},t}(h),b=((o={})[58]="%3A",o[47]="%2F",o[63]="%3F",o[35]="%23",o[91]="%5B",o[93]="%5D",o[64]="%40",o[33]="%21",o[36]="%24",o[38]="%26",o[39]="%27",o[40]="%28",o[41]="%29",o[42]="%2A",o[43]="%2B",o[44]="%2C",o[59]="%3B",o[61]="%3D",o[32]="%20",o);function v(e,t){for(var n=void 0,r=-1,o=0;o<e.length;o++){var i=e.charCodeAt(o);if(i>=97&&i<=122||i>=65&&i<=90||i>=48&&i<=57||45===i||46===i||95===i||126===i||t&&47===i)-1!==r&&(n+=encodeURIComponent(e.substring(r,o)),r=-1),void 0!==n&&(n+=e.charAt(o));else{void 0===n&&(n=e.substr(0,o));var s=b[i];void 0!==s?(-1!==r&&(n+=encodeURIComponent(e.substring(r,o)),r=-1),n+=s):-1===r&&(r=o)}}return-1!==r&&(n+=encodeURIComponent(e.substring(r))),void 0!==n?n:e}function y(e){for(var t=void 0,n=0;n<e.length;n++){var r=e.charCodeAt(n);35===r||63===r?(void 0===t&&(t=e.substr(0,n)),t+=b[r]):void 0!==t&&(t+=e[n])}return void 0!==t?t:e}function x(e,t){var n;return n=e.authority&&e.path.length>1&&"file"===e.scheme?"//"+e.authority+e.path:47===e.path.charCodeAt(0)&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&58===e.path.charCodeAt(2)?t?e.path.substr(1):e.path[1].toLowerCase()+e.path.substr(2):e.path,i&&(n=n.replace(/\//g,"\\")),n}function w(e,t){var n=t?y:v,r="",o=e.scheme,i=e.authority,s=e.path,a=e.query,c=e.fragment;if(o&&(r+=o,r+=":"),(i||"file"===o)&&(r+=d,r+=d),i){var u=i.indexOf("@");if(-1!==u){var l=i.substr(0,u);i=i.substr(u+1),-1===(u=l.indexOf(":"))?r+=n(l,!1):(r+=n(l.substr(0,u),!1),r+=":",r+=n(l.substr(u+1),!1)),r+="@"}-1===(u=(i=i.toLowerCase()).indexOf(":"))?r+=n(i,!1):(r+=n(i.substr(0,u),!1),r+=i.substr(u))}if(s){if(s.length>=3&&47===s.charCodeAt(0)&&58===s.charCodeAt(2))(f=s.charCodeAt(1))>=65&&f<=90&&(s="/"+String.fromCharCode(f+32)+":"+s.substr(3));else if(s.length>=2&&58===s.charCodeAt(1)){var f;(f=s.charCodeAt(0))>=65&&f<=90&&(s=String.fromCharCode(f+32)+":"+s.substr(2))}r+=n(s,!0)}return a&&(r+="?",r+=n(a,!1)),c&&(r+="#",r+=t?c:v(c,!1)),r}function k(e){try{return decodeURIComponent(e)}catch(t){return e.length>3?e.substr(0,3)+k(e.substr(3)):e}}var C=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function _(e){return e.match(C)?e.replace(C,(function(e){return k(e)})):e}},2698:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.wrapWithAbbreviation=async function(e){if(!(0,c.validate)(!1))return!1;const t=a.window.activeTextEditor,n=t.document;(e=e||{}).language||(e.language=n.languageId);const r=g(e)||"html",o=(0,u.getRootNode)(n,!0),i=(0,c.getEmmetHelper)(),s=Array.from(t.selections).sort(((e,t)=>e.start.compareTo(t.start))).map((e=>{let t=e;{let{start:e,end:r}=t;const i=n.offsetAt(e),s=n.getText(),u=(0,c.getHtmlFlatNode)(s,o,i,!0);if(u&&(0,c.isOffsetInsideOpenOrCloseTag)(u,i)){e=n.positionAt(u.start);const t=n.positionAt(u.end);r=t.isAfter(r)?t:r}const l=n.offsetAt(r),f=(0,c.getHtmlFlatNode)(s,o,l,!0);if(f&&(0,c.isOffsetInsideOpenOrCloseTag)(f,l)){const t=n.positionAt(f.start);e=t.isBefore(e)?t:e;const o=n.positionAt(f.end);r=o.isAfter(r)?o:r}t=new a.Range(e,r)}if(!t.isSingleLine&&0===t.end.character){const e=t.end.line-1;t=new a.Range(t.start,n.lineAt(e).range.end)}t.isEmpty&&(t=n.lineAt(t.start).range);const r=n.lineAt(t.start);return!r.isEmptyOrWhitespace&&r.firstNonWhitespaceCharacterIndex>t.start.character&&(t=t.with(new a.Position(t.start.line,r.firstNonWhitespaceCharacterIndex))),t})).reduce(((e,t)=>(e.length>0&&t.intersection(e[e.length-1])?e.push(t.union(e.pop())):e.push(t),e)),[]),l=t.selections;t.selections=s.map((e=>new a.Selection(e.start,e.end)));const f=s.map((e=>{let t;const r=n.getText(e),o=n.lineAt(e.start).text.match(/^(\s*)/),i=o?o[1]:"";return t=e.isSingleLine?[r]:r.split("\n"+i).map((e=>e.trimEnd())),t=t.map((e=>e.replace(/(\$\d)/g,"\\$1"))),{previewRange:e,originalRange:e,originalContent:r,textToWrapInPreview:t,baseIndent:i}})),{tabSize:d,insertSpaces:p}=t.options,b=p?" ".repeat(d):"\t";function v(){return t.edit((e=>{for(const t of f)e.replace(t.previewRange,t.originalContent),t.previewRange=t.originalRange}),{undoStopBefore:!1,undoStopAfter:!1})}let y=!1;async function x(e,n){const o=e&&e.trim()&&i.isAbbreviationValid(r,e)?i.extractAbbreviationFromText(e,r):void 0;if(!o)return y&&(y=!1,await v()),!1;const{abbreviation:s,filter:c}=o;if(n){const e=f.map((e=>({syntax:r,abbreviation:s,rangeToReplace:e.originalRange,textToWrap:e.textToWrapInPreview,filter:c,indent:b,baseIndent:e.baseIndent})));return y=!0,function(e){let n=new a.Range(0,0,0,0),r=new a.Range(0,0,0,0),o=0;return t.edit((t=>{for(let i=0;i<f.length;i++){const s=m(e[i])||"";if(!s)break;const c=f[i].previewRange,u=s.replace(/\$\{[\d]*\}/g,"|").replace(/\$\{[\d]*:([^}]*)\}/g,((e,t)=>t)).replace(/\\\$/g,"$");t.replace(c,u);const l=u.split("\n"),d=c.end.line-c.start.line+1,p=l.length-d,h=c.start.line+o;let g=c.start.character;const b=c.end.line+o+p;let v=l[l.length-1].length;i>0&&b===r.end.line?(g=r.end.character+(c.start.character-n.end.character),v+=g):i>0&&h===r.end.line?g=r.end.character+(c.start.character-n.end.character):1===l.length&&(v+=c.start.character),n=f[i].previewRange,r=new a.Range(h,g,b,v),f[i].previewRange=r,o+=p}}),{undoStopBefore:!1,undoStopAfter:!1})}(e)}const u=f.map((e=>({syntax:r,abbreviation:s,rangeToReplace:e.originalRange,textToWrap:e.textToWrapInPreview,filter:c,indent:b})));return y&&(y=!1,await v()),h(t,u,!1)}let w="";const k=a.l10n.t("Enter Abbreviation"),C=e&&e.abbreviation?e.abbreviation:await a.window.showInputBox({prompt:k,validateInput:async function(e){return e!==w&&(w=e,await x(e,!0)),""}}),_=await x(C,!1);return _||(t.selections=l),_},t.expandEmmetAbbreviation=function(e){if(!(0,c.validate)()||!a.window.activeTextEditor)return d();if(1===a.window.activeTextEditor.selections.length&&a.window.activeTextEditor.selection.isEmpty){const e=a.window.activeTextEditor.selection.anchor;if(0===e.character)return d();const t=e.translate(0,-1),n=a.window.activeTextEditor.document.getText(new a.Range(t,e));if(" "===n||"\t"===n)return d()}if((e=e||{}).language){if((a.workspace.getConfiguration("emmet").excludeLanguages?a.workspace.getConfiguration("emmet").excludeLanguages:[]).includes(a.window.activeTextEditor.document.languageId))return d()}else e.language=a.window.activeTextEditor.document.languageId;const t=g(e);if(!t)return d();const n=a.window.activeTextEditor;if(!0===a.workspace.getConfiguration("emmet").triggerExpansionOnTab&&n.selections.find((e=>!e.isEmpty)))return d();const r=[];let o,i=!0;const s=(0,c.getEmmetHelper)(),l=n.selections.slice(0);let f;function m(){if(f)return f;const e=!0===a.workspace.getConfiguration("emmet").optimizeStylesheetParsing;return f=1===n.selections.length&&(0,c.isStyleSheet)(n.document.languageId)&&e&&n.document.lineCount>1e3?(0,c.parsePartialStylesheet)(n.document,n.selection.isReversed?n.selection.anchor:n.selection.active):(0,u.getRootNode)(n.document,!0),f}return l.sort(((e,t)=>{const n=e.isReversed?e.anchor:e.active,r=t.isReversed?t.anchor:t.active;return-1*n.compareTo(r)})),l.forEach((e=>{const u=e.isReversed?e.anchor:e.active,[l,f,d]=((e,t,r,o)=>{r=e.validatePosition(r);let i=t,u=e.getText(i);if(!i.isEmpty){const e=s.extractAbbreviationFromText(u,o);return e?[i,e.abbreviation,e.filter]:[null,"",""]}const l=n.document.lineAt(r.line).text.substr(0,r.character);if("html"===o){const e=l.match(/<(\w+)$/);if(e)return u=e[1],i=new a.Range(r.translate(0,-(u.length+1)),r),[i,u,""]}const f=s.extractAbbreviation((0,c.toLSTextDocument)(n.document),r,{lookAhead:!1});if(!f)return[null,"",""];const{abbreviationRange:d,abbreviation:p,filter:h}=f;return[new a.Range(d.start.line,d.start.character,d.end.line,d.end.character),p,h]})(n.document,e,u,t);if(!l)return;if(!s.isAbbreviationValid(t,f))return;if((0,c.isStyleSheet)(t)&&f.endsWith(":"))return;const h=n.document.offsetAt(u);let g=(0,c.getFlatNode)(m(),h,!0),b=!0,v=t;if("html"===n.document.languageId)if((0,c.isStyleAttribute)(g,h))v="css",b=!1;else{const e=(0,c.getEmbeddedCssNodeIfAny)(n.document,g,u);e&&(g=(0,c.getFlatNode)(e,h,!0),v="css")}b&&!p(n.document,m(),g,v,h,l)||(o?i&&o!==f&&(i=!1):o=f,r.push({syntax:v,abbreviation:f,rangeToReplace:l,filter:d}))})),h(n,r,i).then((e=>e?Promise.resolve(void 0):d()))},t.isValidLocationForEmmetAbbreviation=p,t.getSyntaxFromArgs=g;const a=s(n(1398)),c=n(7937),u=n(6647),l=/[\u00a0]*[\d#\-\*\u2022]+\.?/,f=/^#[\da-fA-F]{0,6}$/;function d(){return!0===a.workspace.getConfiguration("emmet").triggerExpansionOnTab?a.commands.executeCommand("tab"):Promise.resolve(!0)}function p(e,t,n,r,o,i){if((0,c.isStyleSheet)(r)){if(t&&(t.comments||[]).some((e=>o>=e.start&&o<=e.end)))return!1;if(!n)return!0;const s=e.getText(new a.Range(i.start.line,i.start.character,i.end.line,i.end.character));if(s.startsWith("@"))return!0;if("sass"!==r&&"stylus"!==r&&"property"===n.type){if(n.parent&&"rule"!==n.parent.type&&"at-rule"!==n.parent.type)return!1;const e=n;if(e.terminatorToken&&e.separator&&o>=e.separatorToken.end&&o<=e.terminatorToken.start&&!s.includes(":"))return f.test(s)||"!"===s;if(!e.terminatorToken&&e.separator&&o>=e.separatorToken.end&&!s.includes(":"))return f.test(s)||"!"===s;if(f.test(s)||"!"===s)return!1}if("rule"!==n.type&&"at-rule"!==n.type)return!0;const c=n;if(o>c.contentStartToken.end)return!0;if(c.parent&&("rule"===c.parent.type||"at-rule"===c.parent.type)&&c.selectorToken){const t=e.positionAt(o),n=e.positionAt(c.selectorToken.start),r=e.positionAt(c.selectorToken.end);if(t.line!==r.line&&n.character===i.start.character&&n.line===i.start.line)return!0}return!1}const s="<",u=n;let l=0;if(u){if("script"===u.name){const e=(u.attributes||[]).filter((e=>"type"===e.name.toString()))[0],t=e?e.value.toString():"";return!!c.allowedMimeTypesInScriptTag.includes(t)||!(t&&"application/javascript"!==t&&"text/javascript"!==t||!g({language:"javascript"}))}if(!u.open||!u.close||!(u.open.end<=o&&o<=u.close.start))return!1;l=u.open.end;let e=u.firstChild;for(;e&&!(e.end>o);)l=e.end,e=e.nextSibling}const d=e.positionAt(l);let p=e.getText(new a.Range(d.line,d.character,i.start.line,i.start.character));if(p.length>500&&(p=p.substr(p.length-500)),!p.trim())return!0;let h=!0,m=!1,b=p.length-1;if(p[b]===s)return!1;for(;b>=0;){const e=p[b];if(b--,m||!/\s/.test(e))if("?"!==e||p[b]!==s){if(/\s/.test(e)&&p[b]===s)b--;else if(e===s||">"===e)if(b>=0&&"\\"===p[b])b--;else{if(">"===e){if(b>=0&&"="===p[b])continue;break}if(e===s){h=!m;break}}}else b--;else m=!0}return h}async function h(e,t,n){if(!t||0===t.length)return!1;let r=0;if(!n){t.sort(((e,t)=>t.rangeToReplace.start.compareTo(e.rangeToReplace.start)));for(const n of t){const t=m(n);t&&(await e.insertSnippet(new a.SnippetString(t),n.rangeToReplace,{undoStopBefore:!1,undoStopAfter:!1}),r++)}return r>0}const o=m(t[0]),i=t.map((e=>e.rangeToReplace));return!!o&&e.insertSnippet(new a.SnippetString(o),i)}function m(e){const t=(0,c.getEmmetHelper)(),n=t.getExpandOptions(e.syntax,(0,c.getEmmetConfiguration)(e.syntax),e.filter);let r;e.textToWrap&&(e.textToWrap=e.textToWrap.map((e=>e.replace(/\$\{/g,"\\${"))),e.filter&&e.filter.includes("t")&&(e.textToWrap=e.textToWrap.map((e=>e.replace(l,"").trim()))),n.text=e.textToWrap,n.options&&(e.rangeToReplace.isSingleLine||(n.options["output.inlineBreak"]=1),e.indent&&(n.options["output.indent"]=e.indent),e.baseIndent&&(n.options["output.baseIndent"]=e.baseIndent)));try{r=t.expandAbbreviation(e.abbreviation,n)}catch(e){a.window.showErrorMessage("Failed to expand abbreviation")}return r}function g(e){const t=(0,c.getMappingForIncludedLanguages)(),n=e.language,r=e.parentMode,o=a.workspace.getConfiguration("emmet").excludeLanguages?a.workspace.getConfiguration("emmet").excludeLanguages:[];if(o.includes(n))return;let i=(0,c.getEmmetMode)(t[n]??n,t,o);return i||(i=(0,c.getEmmetMode)(t[r]??r,t,o)),i}},5921:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.balanceOut=function(){d(!0)},t.balanceIn=function(){d(!1)};const a=s(n(1398)),c=n(7937),u=n(6647);let l=[],f=[];function d(e){if(!(0,c.validate)(!1)||!a.window.activeTextEditor)return;const t=a.window.activeTextEditor,n=t.document,r=(0,u.getRootNode)(n,!0);if(!r)return;const o=e?p:h;let i=t.selections.map((e=>o(n,r,e)));m(f,t.selections)?e?m(t.selections,i)||l.push(t.selections):l.length&&(i=l.pop()):l=e?[t.selections]:[],t.selections=i,f=t.selections}function p(e,t,n){const r=e.offsetAt(n.start),o=(0,c.getHtmlFlatNode)(e.getText(),t,r,!1);if(!o)return n;if(!o.open||!o.close)return(0,c.offsetRangeToSelection)(e,o.start,o.end);let i,s;return o.close.start<=r&&o.close.end>r?(i=(0,c.offsetRangeToSelection)(e,o.close.start,o.open.end),s=(0,c.offsetRangeToSelection)(e,o.close.end,o.open.start)):(i=(0,c.offsetRangeToSelection)(e,o.open.end,o.close.start),s=(0,c.offsetRangeToSelection)(e,o.open.start,o.close.end)),i.contains(n)&&!i.isEqual(n)?i:s.contains(n)&&!s.isEqual(n)?s:n}function h(e,t,n){const r=e.offsetAt(n.start),o=(0,c.getHtmlFlatNode)(e.getText(),t,r,!0);if(!o)return n;const i=e.offsetAt(n.start),s=e.offsetAt(n.end);if(o.open&&o.close){const t=i===o.start&&s===o.end,n=i>o.open.start&&i<o.open.end,r=i>o.close.start&&i<o.close.end;if(t||n||r)return(0,c.offsetRangeToSelection)(e,o.open.end,o.close.start)}if(!o.firstChild)return n;const a=o.firstChild;return i===a.start&&s===a.end&&a.open&&a.close?(0,c.offsetRangeToSelection)(e,a.open.end,a.close.start):(0,c.offsetRangeToSelection)(e,a.start,a.end)}function m(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(!e[n].isEqual(t[n]))return!1;return!0}},4367:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DocumentStreamReader=void 0;class n{constructor(e,t,n){this.document=e,this.start=this.pos=t||0,this._sof=n?n[0]:0,this._eof=n?n[1]:e.getText().length}sof(){return this.pos<=this._sof}eof(){return this.pos>=this._eof}limit(e,t){return new n(this.document,e,[e,t])}peek(){return this.eof()?NaN:this.document.getText().charCodeAt(this.pos)}next(){if(this.eof())return NaN;const e=this.document.getText().charCodeAt(this.pos);return this.pos++,this.eof()&&(this.pos=this._eof),e}backUp(e){return this.pos-=e,this.pos<0&&(this.pos=0),this.peek()}current(){return this.substring(this.start,this.pos)}substring(e,t){return this.document.getText().substring(e,t)}error(e){return new Error(`${e} at offset ${this.pos}`)}eat(e){const t=this.peek(),n="function"==typeof e?e(t):t===e;return n&&this.next(),n}eatWhile(e){const t=this.pos;for(;!this.eof()&&this.eat(e););return this.pos!==t}}t.DocumentStreamReader=n},5315:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.DefaultCompletionItemProvider=void 0;const a=s(n(1398)),c=n(2698),u=n(7937),l=n(6647);t.DefaultCompletionItemProvider=class{provideCompletionItems(e,t,n,r){const o=this.provideCompletionItemsInternal(e,t,r);if(o)return o.then((e=>{if(!e||!e.items.length)return this.lastCompletionType=void 0,e;const t=e.items[0],n=t.documentation?t.documentation.toString():"";return n.startsWith("<")?this.lastCompletionType="html":n.indexOf(":")>0&&n.endsWith(";")?this.lastCompletionType="css":this.lastCompletionType=void 0,e}));this.lastCompletionType=void 0}provideCompletionItemsInternal(e,t,n){const r=a.workspace.getConfiguration("emmet"),o=r.excludeLanguages?r.excludeLanguages:[];if(o.includes(e.languageId))return;const i=(0,u.getMappingForIncludedLanguages)(),s=!!i[e.languageId],f=(0,u.getEmmetMode)(s?i[e.languageId]:e.languageId,i,o);if(!f||"never"===r.showExpandedAbbreviation||(s||"jsx"===f)&&"always"!==r.showExpandedAbbreviation)return;let d,p,h=f,m="html"===h||"jsx"===h||"xml"===h;const g=(0,u.toLSTextDocument)(e);t=e.validatePosition(t);const b=new a.Range(t.line,0,t.line,t.character);if(e.getText(b).trimStart().startsWith("//"))return;const v=(0,u.getEmmetHelper)();if("html"===h){if(n.triggerKind===a.CompletionTriggerKind.TriggerForIncompleteCompletions)switch(this.lastCompletionType){case"html":m=!1;break;case"css":m=!1,h="css"}if(m){const n=e.offsetAt(t),r=(0,l.getRootNode)(e,!0),o=(0,u.getHtmlFlatNode)(e.getText(),r,n,!1);if(o)if("script"===o.name){const e=o.attributes.find((e=>"type"===e.name.toString()));if(!e)return;{const t=e.value.toString();if("application/javascript"===t||"text/javascript"===t){if(!(0,c.getSyntaxFromArgs)({language:"javascript"}))return;m=!1}else u.allowedMimeTypesInScriptTag.includes(t)&&(m=!1)}}else if("style"===o.name)h="css",m=!1;else{const e=o.attributes.find((e=>"style"===e.name.toString()));e&&e.value.start<=n&&n<=e.value.end&&(h="css",m=!1)}}}const y=(0,u.isStyleSheet)(h)?{lookAhead:!1,syntax:"stylesheet"}:{lookAhead:!0,syntax:"markup"},x=v.extractAbbreviation(g,t,y);if(!x||!v.isAbbreviationValid(h,x.abbreviation))return;const w=e.offsetAt(t);if((0,u.isStyleSheet)(e.languageId)&&n.triggerKind!==a.CompletionTriggerKind.TriggerForIncompleteCompletions){if(m=!0,d=!0===a.workspace.getConfiguration("emmet").optimizeStylesheetParsing&&e.lineCount>1e3?(0,u.parsePartialStylesheet)(e,t):(0,l.getRootNode)(e,!0),!d)return;p=(0,u.getFlatNode)(d,w,!0)}if(!(0,u.isStyleSheet)(e.languageId)&&(0,u.isStyleSheet)(h)&&n.triggerKind!==a.CompletionTriggerKind.TriggerForIncompleteCompletions){if(m=!0,d=(0,l.getRootNode)(e,!0),!d)return;const n=(0,u.getFlatNode)(d,w,!0),r=(0,u.getEmbeddedCssNodeIfAny)(e,n,t);p=(0,u.getFlatNode)(r,w,!0)}if(m&&!(0,c.isValidLocationForEmmetAbbreviation)(e,d,p,h,w,(k=x.abbreviationRange,new a.Range(k.start.line,k.start.character,k.end.line,k.end.character))))return;var k;let C=Promise.resolve(!1);if(!(0,u.isStyleSheet)(h)&&("javascript"===e.languageId||"javascriptreact"===e.languageId||"typescript"===e.languageId||"typescriptreact"===e.languageId)){const t=x.abbreviation;C=t.startsWith("this.")||/\[[^\]=]*\]/.test(t)?Promise.resolve(!0):a.commands.executeCommand("vscode.executeDocumentSymbolProvider",e.uri).then((e=>!!e&&e.some((e=>t===e.name||t.startsWith(e.name+".")&&!/>|\*|\+/.test(t)))))}return C.then((n=>{if(n)return;const o=(0,u.getEmmetConfiguration)(h),i=v.doComplete((0,u.toLSTextDocument)(e),t,h,o),s=[];return i&&i.items&&i.items.forEach((e=>{const t=new a.CompletionItem(e.label);t.documentation=e.documentation,t.detail=e.detail,t.insertText=new a.SnippetString(e.textEdit.newText);const n=e.textEdit.range;t.range=new a.Range(n.start.line,n.start.character,n.end.line,n.end.character),t.filterText=e.filterText,t.sortText=e.sortText,!0===r.showSuggestionsAsSnippets&&(t.kind=a.CompletionItemKind.Snippet),s.push(t)})),new a.CompletionList(s,!0)}))}}},2727:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.fetchEditPoint=function(e){if(!(0,c.validate)()||!a.window.activeTextEditor)return;const t=a.window.activeTextEditor,n=[];t.selections.forEach((r=>{const o="next"===e?function(e,t){for(let n=e.anchor.line;n<t.document.lineCount;n++){const r=u(n,t,e.anchor,"next");if(r)return r}return e}(r,t):function(e,t){for(let n=e.anchor.line;n>=0;n--){const r=u(n,t,e.anchor,"prev");if(r)return r}return e}(r,t);n.push(o)})),t.selections=n,t.revealRange(t.selections[t.selections.length-1])};const a=s(n(1398)),c=n(7937);function u(e,t,n,r){const o=t.document.lineAt(e);let i=o.text;if(e!==n.line&&o.isEmptyOrWhitespace&&i.length)return new a.Selection(e,i.length,e,i.length);e===n.line&&"prev"===r&&(i=i.substr(0,n.character));const s="next"===r?i.indexOf('""',e===n.line?n.character:0):i.lastIndexOf('""'),c="next"===r?i.indexOf("><",e===n.line?n.character:0):i.lastIndexOf("><");let u=-1;return u=s>-1&&c>-1?"next"===r?Math.min(s,c):Math.max(s,c):s>-1?s:c,u>-1?new a.Selection(e,u+1,e,u+1):void 0}},1212:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.activateEmmetExtension=function(e){(0,w.migrateEmmetExtensionsPath)(),S(),(0,w.updateEmmetExtensionsPath)(),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.wrapWithAbbreviation",(e=>{(0,u.wrapWithAbbreviation)(e)}))),e.subscriptions.push(a.commands.registerCommand("emmet.expandAbbreviation",(e=>{(0,u.expandEmmetAbbreviation)(e)}))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.removeTag",(()=>(0,l.removeTag)()))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.updateTag",(e=>e&&"string"==typeof e?(0,f.updateTag)(e):(0,f.updateTag)(void 0)))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.matchTag",(()=>{(0,d.matchTag)()}))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.balanceOut",(()=>{(0,p.balanceOut)()}))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.balanceIn",(()=>{(0,p.balanceIn)()}))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.splitJoinTag",(()=>(0,h.splitJoinTag)()))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.mergeLines",(()=>{(0,m.mergeLines)()}))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.toggleComment",(()=>{(0,g.toggleComment)()}))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.nextEditPoint",(()=>{(0,b.fetchEditPoint)("next")}))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.prevEditPoint",(()=>{(0,b.fetchEditPoint)("prev")}))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.selectNextItem",(()=>{(0,v.fetchSelectItem)("next")}))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.selectPrevItem",(()=>{(0,v.fetchSelectItem)("prev")}))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.evaluateMathExpression",(()=>{(0,y.evaluateMathExpression)()}))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.incrementNumberByOneTenth",(()=>(0,x.incrementDecrement)(.1)))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.incrementNumberByOne",(()=>(0,x.incrementDecrement)(1)))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.incrementNumberByTen",(()=>(0,x.incrementDecrement)(10)))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.decrementNumberByOneTenth",(()=>(0,x.incrementDecrement)(-.1)))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.decrementNumberByOne",(()=>(0,x.incrementDecrement)(-1)))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.decrementNumberByTen",(()=>(0,x.incrementDecrement)(-10)))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.reflectCSSValue",(()=>(0,k.reflectCssValue)()))),e.subscriptions.push(a.commands.registerCommand("workbench.action.showEmmetCommands",(()=>{a.commands.executeCommand("workbench.action.quickOpen",">Emmet: ")}))),e.subscriptions.push(a.workspace.onDidChangeConfiguration((e=>{(e.affectsConfiguration("emmet.includeLanguages")||e.affectsConfiguration("emmet.useInlineCompletions"))&&S(),e.affectsConfiguration("emmet.extensionsPath")&&(0,w.updateEmmetExtensionsPath)()}))),e.subscriptions.push(a.workspace.onDidSaveTextDocument((e=>{const t=(0,w.getPathBaseName)(e.fileName);t.startsWith("snippets")&&t.endsWith(".json")&&(0,w.updateEmmetExtensionsPath)(!0)}))),e.subscriptions.push(a.workspace.onDidOpenTextDocument((e=>{const t=(0,w.getEmmetMode)(e.languageId,{},[])??"",n=(0,w.getSyntaxes)();(n.markup.includes(t)||n.stylesheet.includes(t))&&(0,C.addFileToParseCache)(e)}))),e.subscriptions.push(a.workspace.onDidCloseTextDocument((e=>{const t=(0,w.getEmmetMode)(e.languageId,{},[])??"",n=(0,w.getSyntaxes)();(n.markup.includes(t)||n.stylesheet.includes(t))&&(0,C.removeFileFromParseCache)(e)})))},t.deactivate=function(){O(),(0,C.clearParseCache)()};const a=s(n(1398)),c=n(5315),u=n(2698),l=n(5893),f=n(8306),d=n(3642),p=n(5921),h=n(5197),m=n(4754),g=n(6676),b=n(2727),v=n(5186),y=n(5774),x=n(8727),w=n(7937),k=n(7284),C=n(6647),_=new Map,T=[];function S(e){O();const t=new c.DefaultCompletionItemProvider,n={async provideInlineCompletionItems(e,n,r,o){const i=await t.provideCompletionItems(e,n,o,{triggerCharacter:void 0,triggerKind:a.CompletionTriggerKind.Invoke});if(!i)return;const s=i.items[0];if(!s)return;const c=s.range;return e.getText(c)===s.label?[{insertText:s.insertText,filterText:s.label,range:c}]:void 0}},r=a.workspace.getConfiguration("emmet").get("useInlineCompletions"),o=(0,w.getMappingForIncludedLanguages)();Object.keys(o).forEach((e=>{if(_.has(e)&&_.get(e)===o[e])return;if(r){const t=a.languages.registerInlineCompletionItemProvider({language:e,scheme:"*"},n);T.push(t)}const i=a.languages.registerCompletionItemProvider({language:e,scheme:"*"},t,...w.LANGUAGE_MODES[o[e]]);T.push(i),_.set(e,o[e])})),Object.keys(w.LANGUAGE_MODES).forEach((e=>{if(!_.has(e)){if(r){const t=a.languages.registerInlineCompletionItemProvider({language:e,scheme:"*"},n);T.push(t)}const o=a.languages.registerCompletionItemProvider({language:e,scheme:"*"},t,...w.LANGUAGE_MODES[e]);T.push(o),_.set(e,e)}}))}function O(){let e;for(_.clear();e=T.pop();)e.dispose()}},5774:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.evaluateMathExpression=function(){if(!a.window.activeTextEditor)return a.window.showInformationMessage("No editor is active"),Promise.resolve(!1);const e=a.window.activeTextEditor;return e.edit((t=>{e.selections.forEach((n=>{const r=n.isReversed?n.active:n.anchor,o=n.isReversed?n.anchor:n.active,i=e.document.getText(new a.Range(r,o));try{if(i){const e=String((0,c.default)(i));t.replace(new a.Range(r,o),e)}else{const r=e.document.getText(new a.Range(new a.Position(n.end.line,0),o)),i=(0,c.extract)(r);if(!i)throw new Error("Invalid extracted indices");const s=String((0,c.default)(r.substr(i[0],i[1]))),u=new a.Range(new a.Position(n.end.line,i[0]),new a.Position(n.end.line,i[1]));t.replace(u,s)}}catch(e){a.window.showErrorMessage("Could not evaluate expression"),console.warn("Math evaluation error",e)}}))}))};const a=s(n(1398)),c=s(n(2915))},2745:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.getImageSize=function(e){return e=e.replace(/^file:\/\//,""),d.test(e)?(t=e,new Promise(((e,n)=>{const r=new l.URL(t),o="https:"===r.protocol?u.get:c.get;if(!r.pathname)return n("Given url doesnt have pathname property");const i=r.pathname;o(r,(t=>{const r=[];let o=0;const s=n=>{try{const r=(0,f.imageSize)(Buffer.concat(n,o));t.removeListener("data",c),t.destroy(),e(p(a.basename(i),r))}catch(e){}},c=e=>{o+=e.length,r.push(e),s(r)};t.on("data",c).on("end",(()=>s(r))).once("error",(e=>{t.removeListener("data",c),n(e)}))})).once("error",n)}))):function(e){return new Promise(((t,n)=>{const r=e.match(/^data:.+?;base64,/);if(r)try{const n=Buffer.from(e.slice(r[0].length),"base64");return t(p("",(0,f.imageSize)(n)))}catch(e){return n(e)}(0,f.imageSize)(e,((r,o)=>{r?n(r):t(p(a.basename(e),o))}))}))}(e);var t};const a=s(n(6928)),c=s(n(8611)),u=s(n(5692)),l=n(7016),f=n(5949),d=/^https?:/;function p(e,t){const n=e.match(/@(\d+)x\./),r=n?+n[1]:1;if(t&&t.width&&t.height)return{realWidth:t.width,realHeight:t.height,width:Math.floor(t.width/r),height:Math.floor(t.height/r)}}},8727:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.incrementDecrement=function(e){if(!a.window.activeTextEditor)return void a.window.showInformationMessage("No editor is active");const t=a.window.activeTextEditor;return t.edit((n=>{t.selections.forEach((r=>{const o=l(t.document,r.isReversed?r.anchor:r.active);if(!o)return;const i=t.document.getText(o);f(i)&&n.replace(o,u(i,e))}))}))},t.update=u,t.locate=l;const a=s(n(1398)),c=/[0-9]/;function u(e,t){let n;const r=(n=e.match(/\.(\d+)$/))?n[1].length:1;let o=String((parseFloat(e)+t).toFixed(r)).replace(/\.0+$/,"");return(n=e.match(/^\-?(0\d+)/))&&(o=o.replace(/^(\-?)(\d+)/,((e,t,r)=>t+"0".repeat(Math.max(0,(n?n[1].length:0)-r.length))+r))),/^\-?\./.test(e)&&(o=o.replace(/^(\-?)0+/,"$1")),o}function l(e,t){const n=e.lineAt(t.line).text;let r,o=t.character,i=t.character,s=!1,u=!1;for(;o>0;){if(r=n[--o],"-"===r){u=!0;break}if("."!==r||s){if(!c.test(r)){o++;break}}else s=!0}for("-"!==n[i]||u||i++;i<n.length;)if(r=n[i++],"."===r&&!s&&c.test(n[i]))s=!0;else if(!c.test(r)){i--;break}if(o!==i&&f(n.slice(o,i)))return new a.Range(t.line,o,t.line,i)}function f(e){return!!e&&!isNaN(parseFloat(e))}},2361:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.locateFile=function(e,t){return/^\w+:/.test(t)?Promise.resolve(t):(t=a.normalize(t),u.test(t)?function(e,t){return new Promise(((n,r)=>{t=t.replace(u,"");const o=e=>{l(a.resolve(e,t)).then(n,(()=>{const n=a.dirname(e);if(!n||n===e)return r(`Unable to locate absolute file ${t}`);o(n)}))};o(e)}))}(e,t):function(e,t){return l(a.resolve(e,t))}(e,t))};const a=s(n(6928)),c=s(n(9896)),u="/"===a.sep?/^\/+/:/^\\+/;function l(e){return new Promise(((t,n)=>{c.stat(e,((r,o)=>r?n(r):o.isFile()?void t(e):n(new Error(`${e} is not a file`))))}))}},3642:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.matchTag=function(){if(!(0,c.validate)(!1)||!a.window.activeTextEditor)return;const e=a.window.activeTextEditor,t=e.document,n=(0,u.getRootNode)(t,!0);if(!n)return;const r=[];e.selections.forEach((e=>{const o=function(e,t,n){const r=e.offsetAt(n),o=(0,c.getHtmlFlatNode)(e.getText(),t,r,!0);if(!o)return;if(!o.open||!o.close||r>o.open.end&&r<o.close.start)return;const i=r<=o.open.end?o.close.start+2:o.start+1;return(0,c.offsetRangeToSelection)(e,i,i)}(t,n,e.start);o&&r.push(o)})),r.length&&(e.selections=r,e.revealRange(e.selections[r.length-1]))};const a=s(n(1398)),c=n(7937),u=n(6647)},4754:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.mergeLines=function(){if(!(0,c.validate)(!1)||!a.window.activeTextEditor)return;const e=a.window.activeTextEditor,t=(0,u.getRootNode)(e.document,!0);return t?e.edit((n=>{Array.from(e.selections).reverse().forEach((r=>{const o=function(e,t,n){let r,o;const i=e.offsetAt(t.start),s=e.offsetAt(t.end);if(t.isEmpty?r=o=(0,c.getFlatNode)(n,i,!0):(r=(0,c.getFlatNode)(n,i,!0),o=(0,c.getFlatNode)(n,s,!0)),!r||!o)return;const u=e.positionAt(r.start),l=u.line,f=u.character,d=e.positionAt(o.end).line;if(l===d)return;const p=(0,c.offsetRangeToVsRange)(e,r.start,o.end);let h=e.lineAt(l).text.substr(f);for(let t=l+1;t<=d;t++)h+=e.lineAt(t).text.trim();return new a.TextEdit(p,h)}(e.document,r,t);o&&n.replace(o.range,o.newText)}))})):void 0};const a=s(n(1398)),c=n(7937),u=n(6647)},4359:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.activate=function(e){e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.updateImageSize",(()=>Promise.resolve().then((()=>s(n(530)))).then((e=>e.updateImageSize()))))),(0,l.setHomeDir)(a.Uri.file((0,c.homedir)())),(0,u.activateEmmetExtension)(e)};const a=s(n(1398)),c=n(857),u=n(1212),l=n(7937)},6647:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.getRootNode=function(e,t){const n=e.uri.toString(),r=a.get(n),c=e.version;if(t&&r&&c===r.key)return r.value;const u=((0,s.isStyleSheet)(e.languageId)?i.default:o.default)(e.getText());return t&&a.set(n,{key:c,value:u}),u},t.addFileToParseCache=function(e){const t=e.uri.toString();a.set(t,void 0)},t.removeFileFromParseCache=function(e){const t=e.uri.toString();a.delete(t)},t.clearParseCache=function(){a.clear()};const o=r(n(1253)),i=r(n(7545)),s=n(7937),a=new Map},7284:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.reflectCssValue=function(){const e=r.window.activeTextEditor;if(!e)return void r.window.showInformationMessage("No editor is active.");const t=(0,o.getCssPropertyFromDocument)(e,e.selection.active);return t?function(e,t){const n=t.parent;let r="";for(const e of i)if(t.name.startsWith(e)){r=e;break}const s=t.name.substr(r.length),a=t.value;return e.edit((t=>{i.forEach((i=>{if(i===r)return;const c=(0,o.getCssPropertyFromRule)(n,i+s);if(c){const n=(0,o.offsetRangeToVsRange)(e.document,c.valueToken.start,c.valueToken.end);t.replace(n,a)}}))}))}(e,t):void 0};const r=n(1398),o=n(7937),i=["-webkit-","-moz-","-ms-","-o-",""]},5893:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.removeTag=function(){if(!(0,u.validate)(!1)||!a.window.activeTextEditor)return;const e=a.window.activeTextEditor,t=e.document,n=(0,c.getRootNode)(t,!0);if(!n)return;const r=Array.from(e.selections).reverse().reduce(((t,r)=>t.concat(function(e,t,n){const r=e.offsetAt(n.start),o=(0,u.getHtmlFlatNode)(e.getText(),t,r,!0);if(!o)return[];let i,s;if(o.open&&(i=(0,u.offsetRangeToVsRange)(e,o.open.start,o.open.end)),o.close&&(s=(0,u.offsetRangeToVsRange)(e,o.close.start,o.close.end)),i&&s){const t=new a.Range(i.end.line,i.end.character,s.start.line,s.start.character),n=new a.Range(i.start.line,i.start.character,s.end.line,s.end.character);if(""===e.getText(t).trim()&&"pre"!==o.name)return[n]}const c=[];if(i&&(c.push(i),s)){const t=function(e,t,n){const r=t.start.line,o=n.start.line,i=e.lineAt(r).firstNonWhitespaceCharacterIndex,s=e.lineAt(o).firstNonWhitespaceCharacterIndex;let a;for(let t=r+1;t<o;t++){const n=e.lineAt(t);if(!n.isEmptyOrWhitespace){const e=n.firstNonWhitespaceCharacterIndex;a=a?Math.min(a,e):e}}let c=0;return a&&(c=a<i||a<s?0:Math.min(a-i,a-s)),c}(e,i,s);let n,r;for(let o=i.start.line+1;o<s.start.line;o++)e.lineAt(o).isEmptyOrWhitespace||(c.push(new a.Range(o,0,o,t)),void 0===n&&(n=o),r=o);l(e,s)&&r?c.push(new a.Range(r,e.lineAt(r).range.end.character,s.end.line,s.end.character)):c.push(s),l(e,i)&&n&&(c[1]=new a.Range(i.start.line,i.start.character,n,e.lineAt(n).firstNonWhitespaceCharacterIndex),c.shift())}return c}(e.document,n,r))),[]);return e.edit((e=>{r.forEach((t=>{e.delete(t)}))}))};const a=s(n(1398)),c=n(6647),u=n(7937);function l(e,t){if(t.start.line===t.end.line){const n=e.lineAt(t.start).text,r=e.getText(t);if(n.trim()===r)return!0}return!1}},5186:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.fetchSelectItem=function(e){if(!(0,c.validate)()||!a.window.activeTextEditor)return;const t=a.window.activeTextEditor,n=t.document,r=(0,f.getRootNode)(n,!0);if(!r)return;const o=[];t.selections.forEach((i=>{const s=i.isReversed?i.active:i.anchor,a=i.isReversed?i.anchor:i.active;let f;f=(0,c.isStyleSheet)(t.document.languageId)?"next"===e?(0,l.nextItemStylesheet)(n,s,a,r):(0,l.prevItemStylesheet)(n,s,a,r):"next"===e?(0,u.nextItemHTML)(n,s,a,r):(0,u.prevItemHTML)(n,s,a,r),o.push(f||i)})),t.selections=o,t.revealRange(t.selections[t.selections.length-1])};const a=s(n(1398)),c=n(7937),u=n(2703),l=n(4950),f=n(6647)},2703:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.nextItemHTML=function(e,t,n,i){const s=e.offsetAt(n);let a,c=(0,r.getHtmlFlatNode)(e.getText(),i,s,!1);if(c){if("comment"!==c.type){if(c.open&&s<=c.open.start+c.name.length)return o(e,c);if(c.open&&s<c.open.end){const n=e.offsetAt(t),o=function(e,t,n,o){if(o.attributes&&0!==o.attributes.length&&"comment"!==o.type)for(const i of o.attributes){if(n<i.start)return(0,r.offsetRangeToSelection)(e,i.start,i.end);if(!i.value||i.value.start===i.value.end)continue;if(t===i.start&&n===i.end||n<i.value.start)return(0,r.offsetRangeToSelection)(e,i.value.start,i.value.end);if(!i.value.toString().includes(" "))continue;let o;if(t===i.value.start&&n===i.value.end&&(o=-1),void 0===o&&n<i.end&&(o=e.positionAt(n).character-e.positionAt(i.value.start).character-1),void 0!==o){const[t,n]=(0,r.findNextWord)(i.value.toString(),o);if(void 0===t||void 0===n)return;if(t>=0&&n>=0){const o=i.value.start+t,s=i.value.start+n;return(0,r.offsetRangeToSelection)(e,o,s)}}}}(e,n,s,c);if(o)return o}for(a=c.firstChild;a&&(s>=a.end||"comment"===a.type);)a=a.nextSibling}for(;!a&&c;)c.nextSibling?"comment"!==c.nextSibling.type?a=c.nextSibling:c=c.nextSibling:c=c.parent;return a&&o(e,a)}},t.prevItemHTML=function(e,t,n,i){const s=e.offsetAt(t);let a,c=(0,r.getHtmlFlatNode)(e.getText(),i,s,!1);if(!c)return;const u=e.offsetAt(n);if(c.open&&"comment"!==c.type&&s-1>c.open.start)if(s<c.open.end||!c.firstChild||u<=c.firstChild.start)a=c;else{let e;for(a=c.firstChild;a.nextSibling&&s>=a.nextSibling.end;)a&&"comment"!==a.type&&(e=a),a=a.nextSibling;a=(0,r.getDeepestFlatNode)(a&&"comment"!==a.type?a:e)}for(;!a&&c;)c.previousSibling?"comment"!==c.previousSibling.type?a=(0,r.getDeepestFlatNode)(c.previousSibling):c=c.previousSibling:a=c.parent;if(!a)return;const l=function(e,t,n,o){if(o.attributes&&0!==o.attributes.length&&"comment"!==o.type)for(let i=o.attributes.length-1;i>=0;i--){const s=o.attributes[i];if(t<=s.start)continue;if(!s.value||s.value.start===s.value.end||t<s.value.start)return(0,r.offsetRangeToSelection)(e,s.start,s.end);if(t===s.value.start)return n>=s.value.end?(0,r.offsetRangeToSelection)(e,s.start,s.end):(0,r.offsetRangeToSelection)(e,s.value.start,s.value.end);const a=e.positionAt(t).character,c=e.positionAt(s.value.start).character,u=t>s.value.end?s.value.toString().length:a-c,[l,f]=(0,r.findPrevWord)(s.value.toString(),u);if(void 0===l||void 0===f)return;if(l>=0&&f>=0){const t=s.value.start+l,n=s.value.start+f;return(0,r.offsetRangeToSelection)(e,t,n)}}}(e,s,u,a);return l||o(e,a)};const r=n(7937);function o(e,t){if(t&&t.open){const n=t.open.start+1,o=n+t.name.length;return(0,r.offsetRangeToSelection)(e,n,o)}}},4950:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.nextItemStylesheet=function(e,t,n,r){const o=e.offsetAt(t),i=e.offsetAt(n);let s=(0,c.getFlatNode)(r,i,!0);if(s||(s=r),!s)return;if("property"===s.type&&o===s.start&&i===s.end)return l(e,s,o,i,!0,"next");if("property"===s.type&&o>=s.valueToken.start&&i<=s.valueToken.end){const t=l(e,s,o,i,!1,"next");if(t)return t}if("rule"===s.type&&i<s.selectorToken.end||"property"===s.type&&i<s.valueToken.end)return u(e,s);let a=s.firstChild;for(;a&&i>=a.end;)a=a.nextSibling;for(;!a&&s;)a=s.nextSibling,s=s.parent;return a?u(e,a):void 0},t.prevItemStylesheet=function(e,t,n,r){const o=e.offsetAt(t),i=e.offsetAt(n);let s=(0,c.getFlatNode)(r,o,!1);if(s||(s=r),!s)return;if("property"===s.type&&o===s.valueToken.start&&i===s.valueToken.end)return u(e,s);if("property"===s.type&&o>=s.valueToken.start&&i<=s.valueToken.end){const t=l(e,s,o,i,!1,"prev");if(t)return t}if("property"===s.type||!s.firstChild||"rule"===s.type&&o<=s.firstChild.start)return u(e,s);let a=s.firstChild;for(;a.nextSibling&&o>=a.nextSibling.end;)a=a.nextSibling;return a=(0,c.getDeepestFlatNode)(a),l(e,a,o,i,!1,"prev")};const a=s(n(1398)),c=n(7937);function u(e,t){if(!t)return;const n="rule"===t.type?t.selectorToken:t;return(0,c.offsetRangeToSelection)(e,n.start,n.end)}function l(e,t,n,r,o,i){if(!t||"property"!==t.type)return;const s=t,u=s.valueToken.stream.substring(s.valueToken.start,s.valueToken.end);if(o=o||"prev"===i&&n===s.valueToken.start&&r<s.valueToken.end)return(0,c.offsetRangeToSelection)(e,s.valueToken.start,s.valueToken.end);let l=-1;if("prev"===i){if(n===s.valueToken.start)return;const t=e.positionAt(n).character,r=e.positionAt(s.valueToken.start).character;l=n>s.valueToken.end?u.length:t-r}else if("next"===i){if(r===s.valueToken.end&&(n>s.valueToken.start||!u.includes(" ")))return;const t=e.positionAt(r).character,o=e.positionAt(s.valueToken.start).character;l=r===s.valueToken.end?-1:t-o-1}const[f,d]="prev"===i?(0,c.findPrevWord)(u,l):(0,c.findNextWord)(u,l);if(!f&&!d)return;const p=e.positionAt(s.valueToken.start),h=p.translate(0,f),m=p.translate(0,d);return new a.Selection(h,m)}},5197:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.splitJoinTag=function(){if(!(0,c.validate)(!1)||!a.window.activeTextEditor)return;const e=a.window.activeTextEditor,t=e.document,n=(0,u.getRootNode)(e.document,!0);return n?e.edit((r=>{Array.from(e.selections).reverse().forEach((e=>{const o=t.getText(),i=t.offsetAt(e.start),s=(0,c.getHtmlFlatNode)(o,n,i,!0);if(s){const e=function(e,t){let n,r;if(t.open&&t.close){const o=t.open.end-1,i=t.end;n=(0,c.offsetRangeToVsRange)(e,o,i),r="/>";const s=(0,c.getEmmetMode)(e.languageId,{},[])??"",a=(0,c.getEmmetConfiguration)(s);s&&a.syntaxProfiles[s]&&("xhtml"===a.syntaxProfiles[s].selfClosingStyle||"xhtml"===a.syntaxProfiles[s].self_closing_tag)&&(r=" "+r)}else{const o=e.getText().substring(t.start,t.end).match(/(\s*\/)?>$/),i=t.end,s=o?i-o[0].length:i;n=(0,c.offsetRangeToVsRange)(e,s,i),r=`></${t.name}>`}return new a.TextEdit(n,r)}(t,s);r.replace(e.range,e.newText)}}))})):void 0};const a=s(n(1398)),c=n(7937),u=n(6647)},6676:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t}),a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.toggleComment=function(){if(!(0,u.validate)()||!c.window.activeTextEditor)return;c.workspace.getConfiguration("editor.comments").get("insertSpace")?(d="/* ",p=" */",h="\x3c!-- ",m=" --\x3e"):(d="/*",p="*/",h="\x3c!--",m="--\x3e");const e=c.window.activeTextEditor,t=(0,f.getRootNode)(e.document,!0);return t?e.edit((n=>{const r=[];Array.from(e.selections).reverse().forEach((n=>{const o=(0,u.isStyleSheet)(e.document.languageId)?b(e.document,n,t):function(e,t,n){const r=t.isReversed?t.active:t.anchor,o=t.isReversed?t.anchor:t.active,i=e.offsetAt(r),s=e.offsetAt(o),a=e.getText(),f=(0,u.getHtmlFlatNode)(a,n,i,!0),d=(0,u.getHtmlFlatNode)(a,n,s,!0);if(!f||!d)return[];if((0,u.sameNodes)(f,d)&&"style"===f.name&&f.open&&f.close&&f.open.end<i&&f.close.start>s){const n=" ".repeat(f.open.end)+a.substring(f.open.end,f.close.start),r=(0,l.default)(n);return b(e,t,r)}const p=(0,u.getNodesInBetween)(f,d);let v=[];return p.forEach((t=>{v=v.concat(g(t,e))})),"comment"===f.type||(v.push(new c.TextEdit((0,u.offsetRangeToVsRange)(e,p[0].start,p[0].start),h)),v.push(new c.TextEdit((0,u.offsetRangeToVsRange)(e,p[p.length-1].end,p[p.length-1].end),m))),v}(e.document,n,t);o.length>0&&r.push(o)})),r.sort(((e,t)=>{const n=e[0].range.start.line-t[0].range.start.line;return 0===n?e[0].range.start.character-t[0].range.start.character:n}));let o=new c.Position(0,0);for(const e of r)e[0].range.end.isAfterOrEqual(o)&&e.forEach((e=>{n.replace(e.range,e.newText),o=e.range.end}))})):void 0};const c=s(n(1398)),u=n(7937),l=a(n(7545)),f=n(6647);let d,p,h,m;function g(e,t){let n=[];return"comment"===e.type?(n.push(new c.TextEdit((0,u.offsetRangeToVsRange)(t,e.start,e.start+h.length),"")),n.push(new c.TextEdit((0,u.offsetRangeToVsRange)(t,e.end-m.length,e.end),"")),n):(e.children.forEach((e=>{n=n.concat(g(e,t))})),n)}function b(e,t,n){const r=t.isReversed?t.active:t.anchor,o=t.isReversed?t.anchor:t.active;let i=e.offsetAt(r),s=e.offsetAt(o);const a=(0,u.getFlatNode)(n,i,!0),l=(0,u.getFlatNode)(n,s,!0);t.isEmpty?a&&(i=a.start,s=a.end,t=(0,u.offsetRangeToSelection)(e,i,s)):(i=function(e,t,n){for(const e of n.comments)if(e.start<=t&&t<=e.end)return t;if(!e)return t;if("property"===e.type)return e.start;const r=e;if(t<r.contentStartToken.end||!r.firstChild)return r.start;if(t<r.firstChild.start)return t;let o=r.firstChild;for(;o.nextSibling&&t>o.end;)o=o.nextSibling;return o.start}(a,i,n),s=function(e,t,n){for(const e of n.comments)if(e.start<=t&&t<=e.end)return t;if(!e)return t;if("property"===e.type)return e.end;const r=e;if(t===r.contentEndToken.end||!r.firstChild)return r.end;if(t>r.children[r.children.length-1].end)return t;let o=r.children[r.children.length-1];for(;o.previousSibling&&t<o.start;)o=o.previousSibling;return o.end}(l,s,n),t=(0,u.offsetRangeToSelection)(e,i,s));const f=[],h=[];return n.comments.forEach((n=>{const r=(0,u.offsetRangeToVsRange)(e,n.start,n.end);t.intersection(r)&&(f.push(r),h.push(new c.TextEdit((0,u.offsetRangeToVsRange)(e,n.start,n.start+d.length),"")),h.push(new c.TextEdit((0,u.offsetRangeToVsRange)(e,n.end-p.length,n.end),"")))})),h.length>0?h:[new c.TextEdit(new c.Range(t.start,t.start),d),new c.TextEdit(new c.Range(t.end,t.end),p)]}},530:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t}),a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.updateImageSize=function(){if(!(0,f.validate)()||!c.window.activeTextEditor)return;const e=c.window.activeTextEditor,t=Array.from(e.selections).reverse().map((t=>{const n=t.isReversed?t.active:t.anchor;return(0,f.isStyleSheet)(e.document.languageId)?function(e,t){return m(e,t,b)}(e,n):function(e,t){const n=g(e,t),r=n&&v(n);return r?(0,d.locateFile)(u.dirname(e.document.fileName),r).then(l.getImageSize).then((n=>{const o=g(e,t);return o&&v(o)===r?function(e,t,n,r){const o=e.document,i=x(t,"src");if(!i)return[];const s=x(t,"width"),a=x(t,"height"),u=function(e,t){const n=t.value?t.value.end:t.end,r=t.end;return n===r?"":e.document.getText().substring(n,r)}(e,i),l=t.attributes[t.attributes.length-1].end,d=[];let p="";return s?d.push(new c.TextEdit((0,f.offsetRangeToVsRange)(o,s.value.start,s.value.end),String(n))):p+=` width=${u}${n}${u}`,a?d.push(new c.TextEdit((0,f.offsetRangeToVsRange)(o,a.value.start,a.value.end),String(r))):p+=` height=${u}${r}${u}`,p&&d.push(new c.TextEdit((0,f.offsetRangeToVsRange)(o,l,l),p)),d}(e,o,n.width,n.height):[]})).catch((e=>(console.warn("Error while updating image size:",e),[]))):function(e,t){return m(e,t,(e=>{const n=e.document,r=(0,h.getRootNode)(n,!0),o=n.offsetAt(t),i=(0,f.getFlatNode)(r,o,!0);if(i&&"style"===i.name&&i.open&&i.close&&i.open.end<o&&i.close.start>o){const e=" ".repeat(i.open.end)+n.getText().substring(i.open.end,i.close.start),t=(0,p.default)(e),r=(0,f.getFlatNode)(t,o,!0);return r&&"property"===r.type?r:null}return null}))}(e,t)}(e,n)}));return Promise.all(t).then((t=>e.edit((e=>{t.forEach((t=>{t.forEach((t=>{e.replace(t.range,t.newText)}))}))}))))};const c=n(1398),u=s(n(6928)),l=n(2745),f=n(7937),d=n(2361),p=a(n(7545)),h=n(6647);function m(e,t,n){const r=n(e,t),o=r&&y(e,r,t);return o?(0,d.locateFile)(u.dirname(e.document.fileName),o).then(l.getImageSize).then((r=>{const i=n(e,t);return r&&i&&y(e,i,t)===o?function(e,t,n,r){const o=e.document,i=t.parent,s=(0,f.getCssPropertyFromRule)(i,"width"),a=(0,f.getCssPropertyFromRule)(i,"height"),u=t.separator||": ",l=function(e,t){let n;return(n=t.previousSibling||t.parent.contentStartToken)?e.document.getText().substring(n.end,t.start):(n=t.nextSibling||t.parent.contentEndToken)?e.document.getText().substring(t.end,n.start):""}(e,t),d=[];t.terminatorToken||d.push(new c.TextEdit((0,f.offsetRangeToVsRange)(o,t.end,t.end),";"));let p="";return s?d.push(new c.TextEdit((0,f.offsetRangeToVsRange)(o,s.valueToken.start,s.valueToken.end),`${n}px`)):p+=`${l}width${u}${n}px;`,a?d.push(new c.TextEdit((0,f.offsetRangeToVsRange)(o,a.valueToken.start,a.valueToken.end),`${r}px`)):p+=`${l}height${u}${r}px;`,p&&d.push(new c.TextEdit((0,f.offsetRangeToVsRange)(o,t.end,t.end),p)),d}(e,i,r.width,r.height):[]})).catch((e=>(console.warn("Error while updating image size:",e),[]))):Promise.reject(new Error("No valid image source"))}function g(e,t){const n=e.document,r=(0,h.getRootNode)(n,!0),o=n.offsetAt(t),i=(0,f.getFlatNode)(r,o,!0);return i&&"img"===i.name.toLowerCase()?i:null}function b(e,t){const n=e.document,r=(0,h.getRootNode)(n,!0),o=n.offsetAt(t),i=(0,f.getFlatNode)(r,o,!0);return i&&"property"===i.type?i:null}function v(e){const t=x(e,"src");if(t)return t.value.value}function y(e,t,n){if(!t)return;const r=function(e,t,n){const r=e.document.offsetAt(n);for(let e,n=0,o=t.parsedValue.length;n<o;n++)if((0,f.iterateCSSToken)(t.parsedValue[n],(t=>!("url"===t.type&&t.start<=r&&t.end>=r&&(e=t,1)))),e)return e}(e,t,n);if(!r)return;let o=r.item(0);return o&&"string"===o.type&&(o=o.item(0)),o&&o.valueOf()}function x(e,t){return t=t.toLowerCase(),e&&e.attributes.find((e=>e.name.toString().toLowerCase()===t))}},8306:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.updateTag=async function(e){if(!(0,c.validate)(!1)||!a.window.activeTextEditor)return;const t=a.window.activeTextEditor,n=t.document,r=(0,u.getRootNode)(n,!0);if(!r)return;const o=t.selections.reduceRight(((e,t)=>e.concat(function(e,t,n){const r=e.getText(),o=e.offsetAt(t.start),i=(0,c.getHtmlFlatNode)(r,n,o,!0);return i?function(e,t){const n=[];if(e.open){const r=t.positionAt(e.open.start);n.push({name:e.name,range:new a.Range(r.translate(0,1),r.translate(0,1).translate(0,e.name.length))})}if(e.close){const r=t.positionAt(e.close.start),o=t.positionAt(e.close.end);n.push({name:e.name,range:new a.Range(r.translate(0,2),o.translate(0,-1))})}return n}(i,e):[]}(n,t,r))),[]);if(!o.length)return;const i=o[0].name,s=o.every((e=>e.name===i));return!(void 0===e&&!(e=await a.window.showInputBox({prompt:"Enter Tag",value:s?i:void 0})))&&t.edit((t=>{o.forEach((n=>{t.replace(n.range,e)}))}))};const a=s(n(1398)),c=n(7937),u=n(6647)},7937:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t}),a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.allowedMimeTypesInScriptTag=t.LANGUAGE_MODES=void 0,t.setHomeDir=function(e){g=e},t.getEmmetHelper=b,t.updateEmmetExtensionsPath=function(e=!1){const t=b();let n=c.workspace.getConfiguration("emmet").get("extensionsPath");if(n||(n=[]),e||m!==n){m=n;const e=c.workspace.workspaceFolders?.length?c.workspace.workspaceFolders.map((e=>e.uri)):void 0,r=c.workspace.fs;t.updateExtensionsPath(n,r,e,g).catch((e=>{Array.isArray(n)&&n.length&&c.window.showErrorMessage(e.message)}))}},t.migrateEmmetExtensionsPath=function(){const e=c.workspace.getConfiguration().inspect("emmet.extensionsPath");"string"==typeof e?.globalValue?c.workspace.getConfiguration().update("emmet.extensionsPath",[e.globalValue],!0):null===e?.globalValue&&c.workspace.getConfiguration().update("emmet.extensionsPath",[],!0),"string"==typeof e?.workspaceValue?c.workspace.getConfiguration().update("emmet.extensionsPath",[e.workspaceValue],!1):null===e?.workspaceValue&&c.workspace.getConfiguration().update("emmet.extensionsPath",[],!1),"string"==typeof e?.workspaceFolderValue?c.workspace.getConfiguration().update("emmet.extensionsPath",[e.workspaceFolderValue]):null===e?.workspaceFolderValue&&c.workspace.getConfiguration().update("emmet.extensionsPath",[])},t.isStyleSheet=v,t.validate=function(e=!0){const t=c.window.activeTextEditor;return t?!(!e&&v(t.document.languageId)):(c.window.showInformationMessage("No editor is active"),!1)},t.getMappingForIncludedLanguages=function(){const e={},n=c.workspace.getConfiguration("emmet").get("includeLanguages"),r=Object.assign({},{handlebars:"html",php:"html"},n??{});return Object.keys(r).forEach((n=>{"string"==typeof r[n]&&t.LANGUAGE_MODES[r[n]]&&(e[n]=r[n])})),e},t.getEmmetMode=function(e,t,n){if(!e||n.includes(e))return;"jsx-tags"===e&&(e="javascriptreact"),t[e]&&(e=t[e]),/\b(typescriptreact|javascriptreact|jsx-tags)\b/.test(e)?e="jsx":"sass-indented"===e?e="sass":"jade"!==e&&"pug"!==e||(e="pug");const r={markup:["html","xml","xsl","jsx","js","pug","slim","haml"],stylesheet:["css","sass","scss","less","sss","stylus"]};return r.markup.includes(e)||r.stylesheet.includes(e)?e:void 0},t.parsePartialStylesheet=function(e,t){const n="css"===e.languageId,r=e.offsetAt(t);let o=0,i=e.getText().length;const s=r-5e3,a=s>0?s:o,u=new f.DocumentStreamReader(e,r);function d(){const t=e.positionAt(u.pos).line;if(!n&&g!==t){g=t;const n=e.lineAt(g).text.indexOf("//");n>-1&&(u.pos=e.offsetAt(new c.Position(g,n)))}}function p(){u.sof()||u.peek()!==w||(u.backUp(1)===k?u.pos=function(t){const n=e.getText().substring(0,t).lastIndexOf("/*");if(-1!==n)return n}(u.pos)??o:u.next())}function h(){if(u.eat(w))if(u.eat(w)&&!n){const t=e.positionAt(u.pos).line;u.pos=e.offsetAt(new c.Position(t+1,0))}else u.eat(k)&&(u.pos=function(t){let n=e.getText().substring(t).indexOf("*/");if(-1!==n)return n+=2+t,n}(u.pos)??i)}for(;!u.eof()&&!u.eat(y);)u.peek()===w?h():u.next();u.eof()||(i=u.pos),u.pos=r;let m=1,g=t.line,b=!1;for(;!b&&m>0&&!u.sof();){switch(d(),u.backUp(1)){case x:m--;break;case y:n?(u.next(),o=u.pos,b=!0):m++;break;case w:p()}(t.line-e.positionAt(u.pos).line>100||u.pos<=a)&&(b=!0)}g=e.positionAt(u.pos).line,m=0;let v=!1;for(;!b&&!u.sof()&&!v&&m>=0;){d();const e=u.backUp(1);if(!/\s/.test(String.fromCharCode(e))){switch(e){case w:p();break;case y:m++;break;case x:m--;break;default:m||(v=!0)}!u.sof()&&v&&(o=u.pos)}}try{const t=" ".repeat(o)+e.getText().substring(o,i);return(0,l.default)(t)}catch(e){return}},t.getFlatNode=C,t.getHtmlFlatNode=function e(t,n,r,o){let i=C(n,r,o);if(i){if("script"===i.name&&0===i.children.length){const n=_(t,i);n&&(i=e(n,i,r,o)??i)}else if("cdata"===i.type){i=e(T(t,i),i,r,o)??i}return i}},t.setupScriptNodeSubtree=_,t.setupCdataNodeSubtree=T,t.isOffsetInsideOpenOrCloseTag=function(e,t){const n=e;return!!(n.open&&t>n.open.start&&t<n.open.end||n.close&&t>n.close.start&&t<n.close.end)},t.offsetRangeToSelection=function(e,t,n){const r=e.positionAt(t),o=e.positionAt(n);return new c.Selection(r,o)},t.offsetRangeToVsRange=function(e,t,n){const r=e.positionAt(t),o=e.positionAt(n);return new c.Range(r,o)},t.getDeepestFlatNode=function e(t){if(!t||!t.children||0===t.children.length||!t.children.find((e=>"comment"!==e.type)))return t;for(let n=t.children.length-1;n>=0;n--)if("comment"!==t.children[n].type)return e(t.children[n])},t.findNextWord=function(e,t){let n,r,o=-1===t,i=!1,s=!1;for(;t<e.length-1;)if(t++,o){if(!o||i||" "!==e[t])if(i){if(" "===e[t]){r=t,s=!0;break}}else n=t,i=!0}else" "===e[t]&&(o=!0);return i&&!s&&(r=e.length),[n,r]},t.findPrevWord=function(e,t){let n,r,o=t===e.length,i=!1,s=!1;for(;t>-1;)if(t--,o){if(!o||s||" "!==e[t])if(s){if(" "===e[t]){n=t+1,i=!0;break}}else r=t+1,s=!0}else" "===e[t]&&(o=!0);return s&&!i&&(n=0),[n,r]},t.getNodesInBetween=function(e,t){if(S(e,t))return[e];if(!S(e.parent,t.parent)){if(t.start<e.start)return[t];if(t.start<e.end)return[e];for(;e.parent&&e.parent.end<t.start;)e=e.parent;for(;t.parent&&t.parent.start>e.start;)t=t.parent}const n=[];let r=e;const o=t.end;for(;r&&o>r.start;)n.push(r),r=r.nextSibling;return n},t.sameNodes=S,t.getEmmetConfiguration=function(e){const t=c.workspace.getConfiguration("emmet"),n=Object.assign({},t.syntaxProfiles||{}),r=Object.assign({},t.preferences||{});return"jsx"!==e&&"xml"!==e&&"xsl"!==e||(n[e]=n[e]||{},"object"!=typeof n[e]||n[e].hasOwnProperty("self_closing_tag")||n[e].hasOwnProperty("selfClosingStyle")||(n[e]={...n[e],selfClosingStyle:"jsx"===e?"xhtml":"xml"})),{preferences:r,showExpandedAbbreviation:t.showExpandedAbbreviation,showAbbreviationSuggestions:t.showAbbreviationSuggestions,syntaxProfiles:n,variables:t.variables,excludeLanguages:t.excludeLanguages,showSuggestionsAsSnippets:t.showSuggestionsAsSnippets}},t.iterateCSSToken=function e(t,n){for(let r=0,o=t.size;r<o;r++)if(!1===n(t.item(r))||!1===e(t.item(r),n))return!1;return!0},t.getCssPropertyFromRule=function(e,t){return e.children.find((e=>"property"===e.type&&e.name===t))},t.getCssPropertyFromDocument=function(e,t){const n=e.document,r=(0,p.getRootNode)(n,!0),o=n.offsetAt(t),i=C(r,o,!0);if(v(e.document.languageId))return i&&"property"===i.type?i:null;const s=i;if(s&&"style"===s.name&&s.open&&s.close&&s.open.end<o&&s.close.start>o){const e=" ".repeat(s.start)+n.getText().substring(s.start,s.end),t=C((0,l.default)(e),o,!0);return t&&"property"===t.type?t:null}return null},t.getEmbeddedCssNodeIfAny=function(e,t,n){if(!t)return;const r=t;if(r&&r.open&&r.close){const t=e.offsetAt(n);if(r.open.end<t&&t<=r.close.start&&"style"===r.name){const t=" ".repeat(r.open.end)+e.getText().substring(r.open.end,r.close.start);return(0,l.default)(t)}}},t.isStyleAttribute=function(e,t){if(!e)return!1;const n=e,r=(n.attributes||[]).findIndex((e=>"style"===e.name.toString()));if(-1===r)return!1;const o=n.attributes[r];return t>=o.value.start&&t<=o.value.end},t.isNumber=function(e){return"number"==typeof e},t.toLSTextDocument=function(e){return d.TextDocument.create(e.uri.toString(),e.languageId,e.version,e.getText())},t.getPathBaseName=function(e){const t=e.split("/").pop();return(t?t.split("\\").pop():"")??""},t.getSyntaxes=function(){return{markup:["html","xml","xsl","jsx","js","pug","slim","haml"],stylesheet:["css","sass","scss","less","sss","stylus"]}};const c=s(n(1398)),u=a(n(1253)),l=a(n(7545)),f=n(4367),d=n(2367),p=n(6647);let h,m,g;function b(){return h||(h=n(8708)),h}function v(e){return["css","scss","sass","less","stylus"].includes(e)}t.LANGUAGE_MODES={html:["!",".","}",":","*","$","]","/",">","0","1","2","3","4","5","6","7","8","9"],jade:["!",".","}",":","*","$","]","/",">","0","1","2","3","4","5","6","7","8","9"],slim:["!",".","}",":","*","$","]","/",">","0","1","2","3","4","5","6","7","8","9"],haml:["!",".","}",":","*","$","]","/",">","0","1","2","3","4","5","6","7","8","9"],xml:[".","}","*","$","]","/",">","0","1","2","3","4","5","6","7","8","9"],xsl:["!",".","}","*","$","/","]",">","0","1","2","3","4","5","6","7","8","9"],css:[":","!","-","0","1","2","3","4","5","6","7","8","9"],scss:[":","!","-","0","1","2","3","4","5","6","7","8","9"],sass:[":","!","0","1","2","3","4","5","6","7","8","9"],less:[":","!","-","0","1","2","3","4","5","6","7","8","9"],stylus:[":","!","0","1","2","3","4","5","6","7","8","9"],javascriptreact:["!",".","}","*","$","]","/",">","0","1","2","3","4","5","6","7","8","9"],typescriptreact:["!",".","}","*","$","]","/",">","0","1","2","3","4","5","6","7","8","9"]};const y=125,x=123,w=47,k=42;function C(e,t,n){if(e)return o(e.children);function r(e){if(!e)return;const r=e.start,i=e.end;if(r<t&&i>t||n&&r<=t&&i>=t)return o(e.children)??e;if("close"in e){const t=e;if(t.open&&!t.close)return o(t.children)}}function o(e){for(let t=0;t<e.length;t++){const n=r(e[t]);if(n)return n}}}function _(e,n){if("script"===n.name&&n.attributes&&n.attributes.some((e=>"type"===e.name.toString()&&t.allowedMimeTypesInScriptTag.includes(e.value.toString())))&&n.open){const t=" ".repeat(n.open.end),r=n.close?n.close.start:n.end,o=t+e.substring(n.open.end,r);return(0,u.default)(o).children.forEach((e=>{n.children.push(e),e.parent=n})),o}return""}function T(e,t){const n=t.start+9,r=t.end-3,o=" ".repeat(n)+e.substring(n,r);return(0,u.default)(o).children.forEach((e=>{t.children.push(e),e.parent=t})),o}function S(e,t){return!e&&!t||!(!e||!t)&&e.start===t.start&&e.end===t.end}t.allowedMimeTypesInScriptTag=["text/html","text/plain","text/x-template","text/template","text/ng-template"]},1398:e=>{"use strict";e.exports=require("vscode")},4434:e=>{"use strict";e.exports=require("events")},9896:e=>{"use strict";e.exports=require("fs")},8611:e=>{"use strict";e.exports=require("http")},5692:e=>{"use strict";e.exports=require("https")},857:e=>{"use strict";e.exports=require("os")},6928:e=>{"use strict";e.exports=require("path")},7016:e=>{"use strict";e.exports=require("url")},9023:e=>{"use strict";e.exports=require("util")},7483:(e,t,n)=>{"use strict";function r(e){return e>47&&e<58}function o(e,t,n){return n=n||90,(e&=-33)>=(t=t||65)&&e<=n}function i(e){return r(e)||s(e)}function s(e){return 95===e||o(e)}function a(e){return function(e){return 32===e||9===e||160===e}(e)||10===e||13===e}function c(e){return 39===e||34===e}n.r(t),n.d(t,{CSSAbbreviationScope:()=>zn,default:()=>Dr,extract:()=>$r,markup:()=>Fr,markupAbbreviation:()=>ve,parseMarkup:()=>xn,parseStylesheet:()=>Wn,parseStylesheetSnippets:()=>Hn,resolveConfig:()=>cr,stringifyMarkup:()=>wn,stringifyStylesheet:()=>Dn,stylesheet:()=>Br,stylesheetAbbreviation:()=>Xe});class u{constructor(e,t,n){null==n&&"string"==typeof e&&(n=e.length),this.string=e,this.pos=this.start=t||0,this.end=n||0}eof(){return this.pos>=this.end}limit(e,t){return new u(this.string,e,t)}peek(){return this.string.charCodeAt(this.pos)}next(){if(this.pos<this.string.length)return this.string.charCodeAt(this.pos++)}eat(e){const t=this.peek(),n="function"==typeof e?e(t):t===e;return n&&this.next(),n}eatWhile(e){const t=this.pos;for(;!this.eof()&&this.eat(e););return this.pos!==t}backUp(e){this.pos-=e||1}current(){return this.substring(this.start,this.pos)}substring(e,t){return this.string.slice(e,t)}error(e,t=this.pos){return new l(`${e} at ${t+1}`,t,this.string)}}class l extends Error{constructor(e,t,n){super(e),this.pos=t,this.string=n}}function f(e){return e.tokens[e.pos]}function d(e){return e.tokens[e.pos++]}function p(e,t=e.start,n=e.pos){return e.tokens.slice(t,n)}function h(e){return e.pos<e.size}function m(e,t){const n=f(e);return!(!n||!t(n)||(e.pos++,0))}function g(e,t,n=f(e)){n&&null!=n.start&&(t+=` at ${n.start}`);const r=new Error(t);return r.pos=n&&n.start,r}function b(e,t){const n={type:"TokenGroup",elements:[]};let r,o=n;const i=[];for(;h(e)&&(r=y(e,t)||v(e,t));)if(o.elements.push(r),m(e,B))i.push(o),o=r;else{if(m(e,U))continue;if(m(e,q))do{i.length&&(o=i.pop())}while(m(e,q))}return n}function v(e,t){if(m(e,D)){const n=b(e,t);return O(d(e),"group",!1)&&(n.repeat=function(e){return I(f(e))?e.tokens[e.pos++]:void 0}(e)),n}}function y(e,t){let n;const r={type:"TokenElement",name:void 0,attributes:void 0,value:void 0,repeat:void 0,selfClose:!1,elements:[]};for(function(e,t){const n=e.pos;if(t.jsx&&m(e,$))for(;h(e);){const{pos:t}=e;if(!m(e,M)||!m(e,$)){e.pos=t;break}}for(;h(e)&&m(e,R););return e.pos!==n&&(e.start=n,!0)}(e,t)&&(r.name=p(e));h(e);)if(e.start=e.pos,r.repeat||F(r)||!m(e,I))if(!r.value&&T(e))r.value=S(e);else{if(!(n=w(e,"id",t)||w(e,"class",t)||x(e))){!F(r)&&m(e,V)&&(r.selfClose=!0,!r.repeat&&m(e,I)&&(r.repeat=e.tokens[e.pos-1]));break}r.attributes?r.attributes=r.attributes.concat(n):r.attributes=Array.isArray(n)?n.slice():[n]}else r.repeat=e.tokens[e.pos-1];return F(r)?void 0:r}function x(e){if(m(e,N)){const t=[];let n;for(;h(e);)if(n=k(e))t.push(n);else{if(m(e,L))break;if(!m(e,j))throw g(e,`Unexpected "${f(e).type}" token`)}return t}}function w(e,t,n){if(E(f(e),t)){e.pos++;let o=1;for(;E(f(e),t);)e.pos++,o++;const i={name:[(r=t,{type:"Literal",value:r})]};return o>1&&(i.multiple=!0),n.jsx&&T(e)?(i.value=S(e),i.expression=!0):i.value=_(e)?p(e):void 0,i}var r}function k(e){if(C(e))return{value:p(e)};if(_(e,!0)){const t=p(e);let n;return m(e,P)&&(C(e)||_(e,!0))&&(n=p(e)),{name:t,value:n}}}function C(e){const t=e.pos,n=f(e);if(A(n)){for(e.pos++;h(e);)if(A(d(e),n.single))return e.start=t,!0;throw g(e,"Unclosed quote",n)}return!1}function _(e,t){const n=e.pos,r={attribute:0,expression:0,group:0};for(;h(e);){const n=f(e);if(r.expression)O(n,"expression")&&(r[n.context]+=n.open?1:-1);else{if(A(n)||E(n)||j(n)||I(n))break;if(O(n)){if(!t)break;if(n.open)r[n.context]++;else{if(!r[n.context])break;r[n.context]--}}}e.pos++}return n!==e.pos&&(e.start=n,!0)}function T(e){const t=e.pos;if(m(e,z)){let n=0;for(;h(e);){const t=d(e);if(O(t,"expression"))if(t.open)n++;else{if(!n)break;n--}}return e.start=t,!0}return!1}function S(e){let t=e.start,n=e.pos;return O(e.tokens[t],"expression",!0)&&t++,O(e.tokens[n-1],"expression",!1)&&n--,p(e,t,n)}function O(e,t,n){return Boolean(e&&"Bracket"===e.type&&(!t||e.context===t)&&(null==n||e.open===n))}function E(e,t){return Boolean(e&&"Operator"===e.type&&(!t||e.operator===t))}function A(e,t){return Boolean(e&&"Quote"===e.type&&(null==t||e.single===t))}function j(e){return Boolean(e&&"WhiteSpace"===e.type)}function P(e){return E(e,"equal")}function I(e){return Boolean(e&&"Repeater"===e.type)}function $(e){if(function(e){return"Literal"===e.type}(e)){const t=e.value.charCodeAt(0);return t>=65&&t<=90}return!1}function R(e){return"Literal"===e.type||"RepeaterNumber"===e.type||"RepeaterPlaceholder"===e.type}function M(e){return E(e,"class")}function N(e){return O(e,"attribute",!0)}function L(e){return O(e,"attribute",!1)}function z(e){return O(e,"expression",!0)}function D(e){return O(e,"group",!0)}function F(e){return!e.name&&!e.value&&!e.attributes}function B(e){return E(e,"child")}function U(e){return E(e,"sibling")}function q(e){return E(e,"climb")}function V(e){return E(e,"close")}var W;function H(e){return!!e.eat(W.Escape)&&(e.start=e.pos,e.eof()||e.pos++,!0)}function G(e,t){return function(e,t){const n=e.pos;if((t.expression||t.attribute)&&e.eat(W.Dollar)&&e.eat(W.CurlyBracketOpen)){let t;e.start=e.pos;let i="";if(e.eatWhile(r)?(t=Number(e.current()),i=e.eat(W.Colon)?J(e):""):o(e.peek())&&(i=J(e)),e.eat(W.CurlyBracketClose))return{type:"Field",index:t,name:i,start:n,end:e.pos};throw e.error("Expecting }")}e.pos=n}(e,t)||function(e){const t=e.pos;if(e.eat(W.Dollar)&&e.eat(W.Hash))return{type:"RepeaterPlaceholder",value:void 0,start:t,end:e.pos};e.pos=t}(e)||function(e){const t=e.pos;if(e.eatWhile(W.Dollar)){const n=e.pos-t;let o=!1,i=1,s=0;if(e.eat(W.At)){for(;e.eat(W.Climb);)s++;o=e.eat(W.Dash),e.start=e.pos,e.eatWhile(r)&&(i=Number(e.current()))}return e.start=t,{type:"RepeaterNumber",size:n,reverse:o,base:i,parent:s,start:t,end:e.pos}}}(e)||function(e){const t=e.pos;if(e.eat(W.Asterisk)){e.start=e.pos;let n=1,o=!1;return e.eatWhile(r)?n=Number(e.current()):o=!0,{type:"Repeater",count:n,value:0,implicit:o,start:t,end:e.pos}}}(e)||function(e){const t=e.pos;if(e.eatWhile(a))return{type:"WhiteSpace",start:t,end:e.pos,value:e.substring(t,e.pos)}}(e)||function(e,t){const n=e.pos,o=t.expression;let i="";for(;!e.eof();){if(H(e)){i+=e.current();continue}const n=e.peek();if(n===W.Slash&&!t.quote&&!t.expression&&!t.attribute){const t=e.string.charCodeAt(e.pos-1),n=e.string.charCodeAt(e.pos+1);if(r(t)&&r(n)){i+=e.string[e.pos++];continue}}if(n===t.quote||n===W.Dollar||Q(n,t))break;if(o){if(n===W.CurlyBracketOpen)t.expression++;else if(n===W.CurlyBracketClose){if(!(t.expression>o))break;t.expression--}}else if(!t.quote){if(!t.attribute&&!te(n))break;if(K(n,t)||X(n,t)||c(n)||Z(n))break}i+=e.string[e.pos++]}if(n!==e.pos)return e.start=n,{type:"Literal",value:i,start:n,end:e.pos}}(e,t)||function(e){const t=Y(e.peek());if(t)return{type:"Operator",operator:t,start:e.pos++,end:e.pos}}(e)||function(e){const t=e.peek();if(c(t))return{type:"Quote",single:t===W.SingleQuote,start:e.pos++,end:e.pos}}(e)||function(e){const t=e.peek(),n=Z(t);if(n)return{type:"Bracket",open:ee(t),context:n,start:e.pos++,end:e.pos}}(e)}function J(e){const t=[];for(e.start=e.pos;!e.eof();)if(e.eat(W.CurlyBracketOpen))t.push(e.pos);else if(e.eat(W.CurlyBracketClose)){if(!t.length){e.pos--;break}t.pop()}else e.pos++;if(t.length)throw e.pos=t.pop(),e.error("Expecting }");return e.current()}function Q(e,t){const n=Y(e);return!(!n||t.quote||t.expression||t.attribute&&"equal"!==n)}function K(e,t){return a(e)&&!t.expression}function X(e,t){return e===W.Asterisk&&!t.attribute&&!t.expression}function Z(e){return e===W.RoundBracketOpen||e===W.RoundBracketClose?"group":e===W.SquareBracketOpen||e===W.SquareBracketClose?"attribute":e===W.CurlyBracketOpen||e===W.CurlyBracketClose?"expression":void 0}function Y(e){return(e===W.Child?"child":e===W.Sibling&&"sibling")||e===W.Climb&&"climb"||e===W.Dot&&"class"||e===W.Hash&&"id"||e===W.Slash&&"close"||e===W.Equals&&"equal"||void 0}function ee(e){return e===W.CurlyBracketOpen||e===W.SquareBracketOpen||e===W.RoundBracketOpen}function te(e){return i(e)||function(e){return 196===e||214==e||220===e||228===e||246===e||252===e}(e)||e===W.Dash||e===W.Colon||e===W.Excl}!function(e){e[e.CurlyBracketOpen=123]="CurlyBracketOpen",e[e.CurlyBracketClose=125]="CurlyBracketClose",e[e.Escape=92]="Escape",e[e.Equals=61]="Equals",e[e.SquareBracketOpen=91]="SquareBracketOpen",e[e.SquareBracketClose=93]="SquareBracketClose",e[e.Asterisk=42]="Asterisk",e[e.Hash=35]="Hash",e[e.Dollar=36]="Dollar",e[e.Dash=45]="Dash",e[e.Dot=46]="Dot",e[e.Slash=47]="Slash",e[e.Colon=58]="Colon",e[e.Excl=33]="Excl",e[e.At=64]="At",e[e.Underscore=95]="Underscore",e[e.RoundBracketOpen=40]="RoundBracketOpen",e[e.RoundBracketClose=41]="RoundBracketClose",e[e.Sibling=43]="Sibling",e[e.Child=62]="Child",e[e.Climb=94]="Climb",e[e.SingleQuote=39]="SingleQuote",e[e.DoubleQuote=34]="DoubleQuote"}(W||(W={}));const ne={child:">",class:".",climb:"^",id:"#",equal:"=",close:"/",sibling:"+"},re={Literal:e=>e.value,Quote:e=>e.single?"'":'"',Bracket:e=>"attribute"===e.context?e.open?"[":"]":"expression"===e.context?e.open?"{":"}":e.open?"(":"}",Operator:e=>ne[e.operator],Field:(e,t)=>null!=e.index?e.name?`\${${e.index}:${e.name}}`:`\${${e.index}`:e.name?t.getVariable(e.name):"",RepeaterPlaceholder(e,t){let n;for(let e=t.repeaters.length-1;e>=0;e--)if(t.repeaters[e].implicit){n=t.repeaters[e];break}return t.inserted=!0,t.getText(n&&n.value)},RepeaterNumber(e,t){let n=1;const r=t.repeaters.length-1,o=t.repeaters[r];if(o&&(n=e.reverse?e.base+o.count-o.value-1:e.base+o.value,e.parent)){const i=Math.max(0,r-e.parent);if(i!==r){const e=t.repeaters[i];n+=o.count*e.value}}let i=String(n);for(;i.length<e.size;)i="0"+i;return i},WhiteSpace:e=>e.value};function oe(e,t){if(!re[e.type])throw new Error(`Unknown token ${e.type}`);return re[e.type](e,t)}const ie=/^((https?:|ftp:|file:)?\/\/|(www|ftp)\.)[^ ]*$/,se=/^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,5}$/;function ae(e,t){let n=[];if(e.repeat){const r=e.repeat,o=Object.assign({},r);let i;o.count=o.implicit&&Array.isArray(t.text)?t.cleanText.length:o.count||1,t.repeaters.push(o);for(let r=0;r<o.count;r++){if(o.value=r,e.repeat=o,i=pe(e)?ue(e,t):ce(e,t),o.implicit&&!t.inserted){const e=me(i),n=e&&ge(e);n&&be(n,t.getText(o.value))}if(n=n.concat(i),--t.repeatGuard<=0)break}t.repeaters.pop(),e.repeat=r,o.implicit&&(t.inserted=!0)}else n=n.concat(pe(e)?ue(e,t):ce(e,t));return n}function ce(e,t){let n=[];const r={type:"AbbreviationNode",name:e.name&&fe(e.name,t),value:e.value&&de(e.value,t),attributes:void 0,children:n,repeat:e.repeat&&Object.assign({},e.repeat),selfClosing:e.selfClose};let o=[r];for(const r of e.elements)n=n.concat(ae(r,t));if(e.attributes){r.attributes=[];for(const n of e.attributes)r.attributes.push(le(n,t))}return r.name||r.attributes||!r.value||r.value.some(he)?r.children=n:o=o.concat(n),o}function ue(e,t){let n=[];for(const r of e.elements)n=n.concat(ae(r,t));return e.repeat&&(n=function(e,t){for(const n of e)n.repeat||(n.repeat=Object.assign({},t));return e}(n,e.repeat)),n}function le(e,t){let n,r=!1,o=!1,i=e.expression?"expression":"raw";const s=e.name&&fe(e.name,t);if(s&&"!"===s[0]&&(r=!0),s&&"."===s[s.length-1]&&(o=!0),e.value){const r=e.value.slice();if(A(r[0])){const e=r.shift();r.length&&me(r).type===e.type&&r.pop(),i=e.single?"singleQuote":"doubleQuote"}else O(r[0],"expression",!0)&&(i="expression",r.shift(),O(me(r),"expression",!1)&&r.pop());n=de(r,t)}return{name:o||r?s.slice(r?1:0,o?-1:void 0):s,value:n,boolean:o,implied:r,valueType:i,multiple:e.multiple}}function fe(e,t){let n="";for(let r=0;r<e.length;r++)n+=oe(e[r],t);return n}function de(e,t){const n=[];let r="";for(let o,i=0;i<e.length;i++)o=e[i],he(o)?(r&&(n.push(r),r=""),n.push(o)):r+=oe(o,t);return r&&n.push(r),n}function pe(e){return"TokenGroup"===e.type}function he(e){return"object"==typeof e&&"Field"===e.type&&null!=e.index}function me(e){return e[e.length-1]}function ge(e){return e.children.length?ge(me(e.children)):e}function be(e,t){e.value?"string"==typeof me(e.value)?e.value[e.value.length-1]+=t:e.value.push(t):e.value=[t]}function ve(e,t){try{return function(e,t={}){let n,r=!1;t.text&&(n=Array.isArray(t.text)?t.text.filter((e=>e.trim())):t.text);const o={type:"Abbreviation",children:ue(e,{inserted:!1,repeaters:[],text:t.text,cleanText:n,repeatGuard:t.maxRepeat||Number.POSITIVE_INFINITY,getText(e){var o;let i;if(r=!0,Array.isArray(t.text)){if(void 0!==e&&e>=0&&e<n.length)return n[e];i=void 0!==e?t.text[e]:t.text.join("\n")}else i=null!==(o=t.text)&&void 0!==o?o:"";return i},getVariable(e){const n=t.variables&&t.variables[e];return null!=n?n:e}})};if(null!=t.text&&!r){const e=ge(me(o.children));if(e){const n=Array.isArray(t.text)?t.text.join("\n"):t.text;be(e,n),"a"===e.name&&t.href&&function(e,t){var n;let r="";ie.test(t)?(r=t,/\w+:/.test(r)||r.startsWith("//")||(r=`http://${r}`)):se.test(t)&&(r=`mailto:${t}`);const o=null===(n=e.attributes)||void 0===n?void 0:n.find((e=>"href"===e.name));o?o.value||(o.value=[r]):(e.attributes||(e.attributes=[]),e.attributes.push({name:"href",value:[r],valueType:"doubleQuote"}))}(e,n)}}return o}(function(e,t={}){const n={tokens:r=e,start:0,pos:0,size:r.length};var r;const o=b(n,t);if(h(n))throw g(n,"Unexpected character");return o}("string"==typeof e?function(e){const t=new u(e),n=[],r={group:0,attribute:0,expression:0,quote:0};let o,i=0;for(;!t.eof();){if(i=t.peek(),o=G(t,r),!o)throw t.error("Unexpected character");n.push(o),"Quote"===o.type?r.quote=i===r.quote?0:i:"Bracket"===o.type&&(r[o.context]+=o.open?1:-1)}return n}(e):e,t),t)}catch(t){throw t instanceof l&&"string"==typeof e&&(t.message+=`\n${e}\n${"-".repeat(t.pos)}^`),t}}var ye,xe;function we(e,t){return function(e){const t=e.pos;if(e.eat(xe.Dollar)&&e.eat(xe.CurlyBracketOpen)){let n;e.start=e.pos;let i="";if(e.eatWhile(r)?(n=Number(e.current()),i=e.eat(xe.Colon)?ke(e):""):o(e.peek())&&(i=ke(e)),e.eat(xe.CurlyBracketClose))return{type:"Field",index:n,name:i,start:t,end:e.pos};throw e.error("Expecting }")}e.pos=t}(e)||function(e){const t=e.pos;if(e.eat(xe.Dash)&&e.eat(xe.Dash))return e.start=t,e.eatWhile(Ee),{type:"CustomProperty",value:e.current(),start:t,end:e.pos};e.pos=t}(e)||function(e){const t=e.pos;if(function(e){const t=e.pos;e.eat(xe.Dash);const n=e.pos,o=e.eatWhile(r),i=e.pos;if(e.eat(xe.Dot)){const t=e.eatWhile(r);o||t||(e.pos=i)}return e.pos===n&&(e.pos=t),e.pos!==t}(e)){e.start=t;const n=e.current();return e.start=e.pos,e.eat(xe.Percent)||e.eatWhile(s),{type:"NumberValue",value:Number(n),rawValue:n,unit:e.current(),start:t,end:e.pos}}}(e)||function(e){const t=e.pos;if(e.eat(xe.Hash)){const n=e.pos;let r="",o="";if(e.eatWhile(Oe)?(r=e.substring(n,e.pos),o=_e(e)):e.eat(xe.Transparent)?(r="0",o=_e(e)||"0"):o=_e(e),r||o||e.eof()){const{r:n,g:i,b:s,a}=function(e,t){let n="0",r="0",o="0",i=Number(null!=t&&""!==t?t:1);if("t"===e)i=0;else switch(e.length){case 0:break;case 1:n=r=o=e+e;break;case 2:n=r=o=e;break;case 3:n=e[0]+e[0],r=e[1]+e[1],o=e[2]+e[2];break;default:n=(e+=e).slice(0,2),r=e.slice(2,4),o=e.slice(4,6)}return{r:parseInt(n,16),g:parseInt(r,16),b:parseInt(o,16),a:i}}(r,o);return{type:"ColorValue",r:n,g:i,b:s,a,raw:e.substring(t+1,e.pos),start:t,end:e.pos}}return Ce(e,t)}e.pos=t}(e)||function(e){const t=e.peek(),n=e.pos;let r=!1;if(c(t)){for(e.pos++;!e.eof();){if(e.eat(t)){r=!0;break}e.pos++}return e.start=n,{type:"StringValue",value:e.substring(n+1,e.pos-(r?1:0)),quote:t===xe.SingleQuote?"single":"double",start:n,end:e.pos}}}(e)||function(e){const t=e.peek();if(function(e){return e===xe.RoundBracketOpen||e===xe.RoundBracketClose}(t))return{type:"Bracket",open:t===xe.RoundBracketOpen,start:e.pos++,end:e.pos}}(e)||Te(e)||function(e){const t=e.pos;if(e.eatWhile(a))return{type:"WhiteSpace",start:t,end:e.pos}}(e)||function(e,t){const n=e.pos;if(e.eat(Se)?e.eatWhile(n?Ee:Ae):e.eat(s)?e.eatWhile(t?Ae:Ee):(e.eat(xe.Dot),e.eatWhile(Ae)),n!==e.pos)return e.start=n,Ce(e,e.start=n)}(e,t)}function ke(e){const t=[];for(e.start=e.pos;!e.eof();)if(e.eat(xe.CurlyBracketOpen))t.push(e.pos);else if(e.eat(xe.CurlyBracketClose)){if(!t.length){e.pos--;break}t.pop()}else e.pos++;if(t.length)throw e.pos=t.pop(),e.error("Expecting }");return e.current()}function Ce(e,t=e.start,n=e.pos){return{type:"Literal",value:e.substring(t,n),start:t,end:n}}function _e(e){const t=e.pos;return e.eat(xe.Dot)?(e.start=t,e.eatWhile(r)?e.current():"1"):""}function Te(e){const t=(n=e.peek())===xe.Sibling&&ye.Sibling||n===xe.Excl&&ye.Important||n===xe.Comma&&ye.ArgumentDelimiter||n===xe.Colon&&ye.PropertyDelimiter||n===xe.Dash&&ye.ValueDelimiter||void 0;var n;if(t)return{type:"Operator",operator:t,start:e.pos++,end:e.pos}}function Se(e){return e===xe.At||e===xe.Dollar}function Oe(e){return r(e)||o(e,65,70)}function Ee(e){return i(e)||e===xe.Dash}function Ae(e){return s(e)||e===xe.Percent||e===xe.Slash}function je(e){return"ColorValue"===e.type||"NumberValue"===e.type&&!e.unit}function Pe(e,t){let n=0,r=0;for(;t.length;){const e=(o=t)[o.length-1];if("Literal"!==e.type&&"NumberValue"!==e.type)break;n=e.start,r||(r=e.end),t.pop()}var o;n!==r&&t.push(Ce(e,n,r))}function Ie(e){return e.tokens[e.pos]}function $e(e){return e.pos<e.size}function Re(e,t){return!!t(Ie(e))&&(e.pos++,!0)}function Me(e,t,n=Ie(e)){n&&null!=n.start&&(t+=` at ${n.start}`);const r=new Error(t);return r.pos=n&&n.start,r}function Ne(e,t){let n,r,o=!1;const i=[],s=Ie(e),a=!!t.value;for(a||!De(s)||function(e){const t=e.tokens[e.pos],n=e.tokens[e.pos+1];return t&&n&&De(t)&&"Bracket"===n.type}(e)||(e.pos++,n=s.value,Re(e,Ke)),a&&Re(e,qe);$e(e);)if(Re(e,Je))o=!0;else if(r=Le(e,a))i.push(r);else if(!Re(e,Ge))break;if(n||i.length||o)return{name:n,value:i,important:o}}function Le(e,t){const n=[];let r,o;for(;$e(e);)if(r=Ie(e),Qe(r))e.pos++,De(r)&&(o=ze(e))?n.push({type:"FunctionCall",name:r.value,arguments:o}):n.push(r);else{if(!(Ke(r)||t&&qe(r)))break;e.pos++}return n.length?{type:"CSSValue",value:n}:void 0}function ze(e){const t=e.pos;if(Re(e,Be)){const n=[];let r;for(;$e(e)&&!Re(e,Ue);)if(r=Le(e,!0))n.push(r);else if(!Re(e,qe)&&!Re(e,He))throw Me(e,"Unexpected token");return e.start=t,n}}function De(e){return e&&"Literal"===e.type}function Fe(e,t){return e&&"Bracket"===e.type&&(null==t||e.open===t)}function Be(e){return Fe(e,!0)}function Ue(e){return Fe(e,!1)}function qe(e){return e&&"WhiteSpace"===e.type}function Ve(e,t){return e&&"Operator"===e.type&&(!t||e.operator===t)}function We(e){return Ve(e,ye.Sibling)}function He(e){return Ve(e,ye.ArgumentDelimiter)}function Ge(e){return He(e)}function Je(e){return Ve(e,ye.Important)}function Qe(e){return"StringValue"===e.type||"ColorValue"===e.type||"NumberValue"===e.type||"Literal"===e.type||"Field"===e.type||"CustomProperty"===e.type}function Ke(e){return Ve(e,ye.PropertyDelimiter)||Ve(e,ye.ValueDelimiter)}function Xe(e,t){try{const n="string"==typeof e?function(e,t){let n,r=0;const o=new u(e),i=[];for(;!o.eof();){if(n=we(o,0===r&&!t),!n)throw o.error("Unexpected character");if("Bracket"===n.type&&(!r&&n.open&&Pe(o,i),r+=n.open?1:-1,r<0))throw o.error("Unexpected bracket",n.start);i.push(n),je(n)&&(n=Te(o))&&i.push(n)}return i}(e,t&&t.value):e;return function(e,t={}){const n=function(e){return{tokens:e,start:0,pos:0,size:e.length}}(e),r=[];let o;for(;$e(n);)if(o=Ne(n,t))r.push(o);else if(!Re(n,We))throw Me(n,"Unexpected token");return r}(n,t)}catch(t){throw t instanceof l&&"string"==typeof e&&(t.message+=`\n${e}\n${"-".repeat(t.pos)}^`),t}}function Ze(e,t,n){if(e&&t){e.length&&n&&et(e,n);for(const n of t)et(e,n);return e}const r=e||t;return r&&r.slice()}function Ye(e,t,n){return e.name=t.name,n.options["output.reverseAttributes"]||(e.value=t.value),e.implied||(e.implied=t.implied),e.boolean||(e.boolean=t.boolean),"expression"!==e.valueType&&(e.valueType=t.valueType),e}function et(e,t){const n=e.length-1;"string"==typeof e[n]&&"string"==typeof t?e[n]+=t:e.push(t)}function tt(e,t){for(let n=0;n<e.children.length;n++){const r=e.children[n];if(t(r))return r;const o=tt(r,t);if(o)return o}}function nt(e){let t;for(;e.children.length;)t=e,e=e.children[e.children.length-1];return{parent:t,node:e}}function rt(e){return"AbbreviationNode"===e.type}function ot(e,t,n){let r=[];for(const n of e.children){const e=t(n);if(e){r=r.concat(e.children);const o=nt(e);rt(o.node)&&(o.node.children=o.node.children.concat(ot(n,t)))}else r.push(n),n.children=ot(n,t)}return e.children=r}!function(e){e.Sibling="+",e.Important="!",e.ArgumentDelimiter=",",e.ValueDelimiter="-",e.PropertyDelimiter=":"}(ye||(ye={})),function(e){e[e.Hash=35]="Hash",e[e.Dollar=36]="Dollar",e[e.Dash=45]="Dash",e[e.Dot=46]="Dot",e[e.Colon=58]="Colon",e[e.Comma=44]="Comma",e[e.Excl=33]="Excl",e[e.At=64]="At",e[e.Percent=37]="Percent",e[e.Underscore=95]="Underscore",e[e.RoundBracketOpen=40]="RoundBracketOpen",e[e.RoundBracketClose=41]="RoundBracketClose",e[e.CurlyBracketOpen=123]="CurlyBracketOpen",e[e.CurlyBracketClose=125]="CurlyBracketClose",e[e.Sibling=43]="Sibling",e[e.SingleQuote=39]="SingleQuote",e[e.DoubleQuote=34]="DoubleQuote",e[e.Transparent=116]="Transparent",e[e.Slash=47]="Slash"}(xe||(xe={}));const it="{",st="}";function at(e,t=0){return{options:e,value:"",level:t,offset:0,line:0,column:0}}function ct(e,t){gt(e,(0,e.options["output.text"])(t,e.offset,e.line,e.column))}function ut(e,t){const n=function(e){return e.split(/\r\n|\r|\n/g)}(t);for(let t=0,r=n.length-1;t<=r;t++)ct(e,n[t]),t!==r&&lt(e,!0)}function lt(e,t){const n=e.options["output.baseIndent"];ct(e,e.options["output.newline"]+n),e.line++,e.column=n.length,t&&function(e,t=e.level){ct(e,e.options["output.indent"].repeat(Math.max(t,0)))}(e,!0===t?e.level:t)}function ft(e,t,n){gt(e,(0,e.options["output.field"])(t,n,e.offset,e.line,e.column))}function dt(e,t){return bt(e,t.options["output.attributeCase"])}function pt(e,t,n){return"expression"===e.valueType?n?it:st:"single"===t.options["output.attributeQuotes"]?"'":'"'}function ht(e,t){return e.boolean||t.options["output.booleanAttributes"].includes((e.name||"").toLowerCase())}function mt(e,t){return"string"==typeof e?t.options.inlineElements.includes(e.toLowerCase()):e.name?mt(e.name,t):Boolean(e.value&&!e.attributes)}function gt(e,t){e.value+=t,e.offset+=t.length,e.column+=t.length}function bt(e,t){return t?"upper"===t?e.toUpperCase():e.toLowerCase():e}const vt={p:"span",ul:"li",ol:"li",table:"tr",tr:"td",tbody:"tr",thead:"tr",tfoot:"tr",colgroup:"col",select:"option",optgroup:"option",audio:"source",video:"source",object:"param",map:"area"};function yt(e,t,n){const r=function(e){for(let t=e.length-1;t>=0;t--){const n=e[t];if(rt(n))return n}}(t),o=n.context?n.context.name:"",i=((r?r.name:o)||"").toLowerCase();e.name=vt[i]||(mt(i,n)?"span":"div")}const xt={ru:{common:["далеко-далеко","за","словесными","горами","в стране","гласных","и согласных","живут","рыбные","тексты"],words:["вдали","от всех","они","буквенных","домах","на берегу","семантика","большого","языкового","океана","маленький","ручеек","даль","журчит","по всей","обеспечивает","ее","всеми","необходимыми","правилами","эта","парадигматическая","страна","которой","жаренные","предложения","залетают","прямо","рот","даже","всемогущая","пунктуация","не","имеет","власти","над","рыбными","текстами","ведущими","безорфографичный","образ","жизни","однажды","одна","маленькая","строчка","рыбного","текста","имени","lorem","ipsum","решила","выйти","большой","мир","грамматики","великий","оксмокс","предупреждал","о","злых","запятых","диких","знаках","вопроса","коварных","точках","запятой","но","текст","дал","сбить","себя","толку","он","собрал","семь","своих","заглавных","букв","подпоясал","инициал","за","пояс","пустился","дорогу","взобравшись","первую","вершину","курсивных","гор","бросил","последний","взгляд","назад","силуэт","своего","родного","города","буквоград","заголовок","деревни","алфавит","подзаголовок","своего","переулка","грустный","реторический","вопрос","скатился","его","щеке","продолжил","свой","путь","дороге","встретил","рукопись","она","предупредила","моей","все","переписывается","несколько","раз","единственное","что","меня","осталось","это","приставка","возвращайся","ты","лучше","свою","безопасную","страну","послушавшись","рукописи","наш","продолжил","свой","путь","вскоре","ему","повстречался","коварный","составитель","рекламных","текстов","напоивший","языком","речью","заманивший","свое","агентство","которое","использовало","снова","снова","своих","проектах","если","переписали","то","живет","там","до","сих","пор"]},sp:{common:["mujer","uno","dolor","más","de","poder","mismo","si"],words:["ejercicio","preferencia","perspicacia","laboral","paño","suntuoso","molde","namibia","planeador","mirar","demás","oficinista","excepción","odio","consecuencia","casi","auto","chicharra","velo","elixir","ataque","no","odio","temporal","cuórum","dignísimo","facilismo","letra","nihilista","expedición","alma","alveolar","aparte","león","animal","como","paria","belleza","modo","natividad","justo","ataque","séquito","pillo","sed","ex","y","voluminoso","temporalidad","verdades","racional","asunción","incidente","marejada","placenta","amanecer","fuga","previsor","presentación","lejos","necesariamente","sospechoso","adiposidad","quindío","pócima","voluble","débito","sintió","accesorio","falda","sapiencia","volutas","queso","permacultura","laudo","soluciones","entero","pan","litro","tonelada","culpa","libertario","mosca","dictado","reincidente","nascimiento","dolor","escolar","impedimento","mínima","mayores","repugnante","dulce","obcecado","montaña","enigma","total","deletéreo","décima","cábala","fotografía","dolores","molesto","olvido","paciencia","resiliencia","voluntad","molestias","magnífico","distinción","ovni","marejada","cerro","torre","y","abogada","manantial","corporal","agua","crepúsculo","ataque","desierto","laboriosamente","angustia","afortunado","alma","encefalograma","materialidad","cosas","o","renuncia","error","menos","conejo","abadía","analfabeto","remo","fugacidad","oficio","en","almácigo","vos","pan","represión","números","triste","refugiado","trote","inventor","corchea","repelente","magma","recusado","patrón","explícito","paloma","síndrome","inmune","autoinmune","comodidad","ley","vietnamita","demonio","tasmania","repeler","apéndice","arquitecto","columna","yugo","computador","mula","a","propósito","fantasía","alias","rayo","tenedor","deleznable","ventana","cara","anemia","corrupto"]},latin:{common:["lorem","ipsum","dolor","sit","amet","consectetur","adipisicing","elit"],words:["exercitationem","perferendis","perspiciatis","laborum","eveniet","sunt","iure","nam","nobis","eum","cum","officiis","excepturi","odio","consectetur","quasi","aut","quisquam","vel","eligendi","itaque","non","odit","tempore","quaerat","dignissimos","facilis","neque","nihil","expedita","vitae","vero","ipsum","nisi","animi","cumque","pariatur","velit","modi","natus","iusto","eaque","sequi","illo","sed","ex","et","voluptatibus","tempora","veritatis","ratione","assumenda","incidunt","nostrum","placeat","aliquid","fuga","provident","praesentium","rem","necessitatibus","suscipit","adipisci","quidem","possimus","voluptas","debitis","sint","accusantium","unde","sapiente","voluptate","qui","aspernatur","laudantium","soluta","amet","quo","aliquam","saepe","culpa","libero","ipsa","dicta","reiciendis","nesciunt","doloribus","autem","impedit","minima","maiores","repudiandae","ipsam","obcaecati","ullam","enim","totam","delectus","ducimus","quis","voluptates","dolores","molestiae","harum","dolorem","quia","voluptatem","molestias","magni","distinctio","omnis","illum","dolorum","voluptatum","ea","quas","quam","corporis","quae","blanditiis","atque","deserunt","laboriosam","earum","consequuntur","hic","cupiditate","quibusdam","accusamus","ut","rerum","error","minus","eius","ab","ad","nemo","fugit","officia","at","in","id","quos","reprehenderit","numquam","iste","fugiat","sit","inventore","beatae","repellendus","magnam","recusandae","quod","explicabo","doloremque","aperiam","consequatur","asperiores","commodi","optio","dolor","labore","temporibus","repellat","veniam","architecto","est","esse","mollitia","nulla","a","similique","eos","alias","dolore","tenetur","deleniti","porro","facere","maxime","corrupti"]}},wt=/^lorem([a-z]*)(\d*)(-\d*)?$/i;function kt(e,t){return Math.floor(Math.random()*(t-e)+e)}function Ct(e,t){const n=e.length,r=Math.min(n,t),o=[];for(;o.length<r;){const t=e[kt(0,n)];o.includes(t)||o.push(t)}return o}function _t(e,t){var n;return e.length&&(e=[(n=e[0],n[0].toUpperCase()+n.slice(1))].concat(e.slice(1))),e.join(" ")+(t||"?!..."[kt(0,4)])}function Tt(e){if(e.length<2)return e;const t=(e=e.slice()).length,n=/,$/;let r=0;r=t>3&&t<=6?kt(0,1):t>6&&t<=12?kt(0,2):kt(1,4);for(let o,i=0;i<r;i++)o=kt(0,t-2),n.test(e[o])||(e[o]+=",");return e}function St(e,t,n){const r=[];let o,i=0;for(n&&e.common&&(o=e.common.slice(0,t),i+=o.length,r.push(_t(Tt(o),".")));i<t;)o=Ct(e.words,Math.min(kt(2,30),t-i)),i+=o.length,r.push(_t(Tt(o)));return r.join(" ")}function Ot(e){return"select"!==e.name}const Et=/^(-+)([a-z0-9]+[a-z0-9-]*)/i,At=/^(_+)([a-z0-9]+[a-z0-9-_]*)/i,jt=e=>/^[a-z]\-/i.test(e),Pt=e=>/^[a-z]/i.test(e);function It(e){if(!e._bem){let t="";if(e.attributes)for(const n of e.attributes)if("class"===n.name&&n.value){t=zt(n.value);break}e._bem=$t(t)}return e._bem}function $t(e){const t=e?e.split(/\s+/):[];return{classNames:t,block:Mt(t)}}function Rt(e,t=0,n){let r=Math.max(e.length-t,0);do{const t=e[r];if(t){const e=It(t);if(e.block)return e.block}}while(0<r--);if(n){const e=function(e){return e._bem||(e._bem=$t(e.attributes&&e.attributes.class||"")),e._bem}(n);if(e.block)return e.block}return""}function Mt(e){return Nt(e,jt)||Nt(e,Pt)||void 0}function Nt(e,t){for(const n of e){if(Et.test(n)||At.test(n))break;if(t(n))return n}}function Lt(e,t){for(const n of e.attributes)if("class"===n.name){n.value=[t];break}}function zt(e){let t="";for(const n of e)t+="string"==typeof n?n:n.name;return t}function Dt(e,t,n){return!!e&&n.indexOf(e)===t}function Ft(e){if("label"===e.name){const t=tt(e,(e=>"input"===e.name||"textarea"===e.name));t&&(e.attributes&&(e.attributes=e.attributes.filter((e=>!("for"===e.name&&Bt(e))))),t.attributes&&(t.attributes=t.attributes.filter((e=>!("id"===e.name&&Bt(e))))))}}function Bt(e){if(!e.value)return!0;if(1===e.value.length){const t=e.value[0];if(t&&"string"!=typeof t&&!t.name)return!0}return!1}function Ut(e,t,n){const r=(e,r,i)=>{const{parent:s,current:a}=n;n.parent=a,n.current=e,t(e,r,i,n,o),n.current=a,n.parent=s},o=(e,t,o)=>{n.ancestors.push(n.current),r(e,t,o),n.ancestors.pop()};e.children.forEach(r)}function qt(e){return{current:null,parent:void 0,ancestors:[],config:e,field:1,out:at(e.options)}}const Vt=[{type:"Field",index:0,name:""}];function Wt(e){return!!e&&!e.name&&!e.attributes}function Ht(e,t){return!!e&&mt(e,t)}function Gt(e){return"object"==typeof e&&"Field"===e.type}function Jt(e,t){const{out:n}=t;let r=-1;for(const o of e)"string"==typeof o?ut(n,o):(ft(n,t.field+o.index,o.name),o.index>r&&(r=o.index));-1!==r&&(t.field+=r+1)}function Qt(e){return!e.implied||"raw"!==e.valueType||!!e.value&&e.value.length>0}var Kt;function Xt(e){const t=[],n={pos:0,text:e};let r,o=n.pos,i=n.pos;for(;n.pos<n.text.length;)i=n.pos,(r=Zt(n))?(o!==n.pos&&t.push(e.slice(o,i)),t.push(r),o=n.pos):n.pos++;return o!==n.pos&&t.push(e.slice(o)),t}function Zt(e){if(Yt(e)===Kt.Start){const t=++e.pos;let n=t,r=t,o=1;for(;e.pos<e.text.length;){const i=Yt(e);if(en(i)){for(n=e.pos;tn(Yt(e));)e.pos++;r=e.pos}else{if(i===Kt.Start)o++;else if(i===Kt.End&&0==--o)return{before:e.text.slice(t,n),after:e.text.slice(r,e.pos++),name:e.text.slice(n,r)};e.pos++}}}}function Yt(e,t=e.pos){return e.text.charCodeAt(t)}function en(e){return e>=65&&e<=90}function tn(e){return en(e)||e>47&&e<58||e===Kt.Underscore||e===Kt.Dash}function nn(e,t){const{comment:n}=t;if(!(n.enabled&&n.trigger&&e.name&&e.attributes))return!1;for(const t of e.attributes)if(t.name&&n.trigger.includes(t.name))return!0;return!1}function rn(e,t,n){const r={},{out:o}=n;for(const t of e.attributes)t.name&&t.value&&(r[t.name.toUpperCase()]=t.value);for(const e of t)"string"==typeof e?ut(o,e):r[e.name]&&(ut(o,e.before),Jt(r[e.name],n),ut(o,e.after))}!function(e){e[e.Start=91]="Start",e[e.End=93]="End",e[e.Underscore=95]="Underscore",e[e.Dash=45]="Dash"}(Kt||(Kt={}));const on=/^<([\w\-:]+)[\s>]/,sn=new Set(["for","while","of","async","await","const","let","var","continue","break","debugger","do","export","import","in","instanceof","new","return","switch","this","throw","try","catch","typeof","void","with","yield"]);function an(e,t){const n=qt(t);return n.comment=function(e){const{options:t}=e;return{enabled:t["comment.enabled"],trigger:t["comment.trigger"],before:t["comment.before"]?Xt(t["comment.before"]):void 0,after:t["comment.after"]?Xt(t["comment.after"]):void 0}}(t),Ut(e,cn,n),n.out.value}function cn(e,t,n,r,o){const{out:i,config:s}=r,a=fn(e,t,n,r),c=function(e){const{config:t,parent:n}=e;return!n||Wt(n)||n.name&&t.options["output.formatSkip"].includes(n.name)?0:1}(r);if(i.level+=c,a&&lt(i,!0),e.name){const t=function(e,t){return bt(e,t.options["output.tagCase"])}(e.name,s);if(function(e,t){nn(e,t)&&t.comment.before&&rn(e,t.comment.before,t)}(e,r),ut(i,`<${t}`),e.attributes)for(const t of e.attributes)Qt(t)&&un(t,r);if(!e.selfClosing||e.children.length||e.value){if(ut(i,">"),!ln(e,r,o)){if(e.value){const t=e.value.some(dn)||function(e,t){if(e.length&&"string"==typeof e[0]){const n=on.exec(e[0]);if((null==n?void 0:n.length)&&!t.options.inlineElements.includes(n[1].toLowerCase()))return!0}return!1}(e.value,s);t&&lt(r.out,++i.level),Jt(e.value,r),t&&lt(r.out,--i.level)}if(e.children.forEach(o),!e.value&&!e.children.length){const t=s.options["output.formatLeafNode"]||s.options["output.formatForce"].includes(e.name);t&&lt(r.out,++i.level),Jt(Vt,r),t&&lt(r.out,--i.level)}}ut(i,`</${t}>`),function(e,t){nn(e,t)&&t.comment.after&&rn(e,t.comment.after,t)}(e,r)}else ut(i,`${function(e){switch(e.options["output.selfClosingStyle"]){case"xhtml":return" /";case"xml":return"/";default:return""}}(s)}>`)}else!ln(e,r,o)&&e.value&&(Jt(e.value,r),e.children.forEach(o));if(a&&t===n.length-1&&r.parent){const e=Wt(r.parent)?0:1;lt(i,i.level-e)}i.level-=c}function un(e,t){const{out:n,config:r}=t;if(e.name){const o=r.options["markup.attributes"],i=r.options["markup.valuePrefix"];let{name:s,value:a}=e,c=pt(e,r,!0),u=pt(e,r);o&&(s=pn(s,o,e.multiple)||s),s=dt(s,r),r.options["jsx.enabled"]&&e.multiple&&(c=it,u=st);const l=i?pn(e.name,i,e.multiple):null;if(l&&1===(null==a?void 0:a.length)&&"string"==typeof a[0]){const e=a[0];a=[hn(e)?`${l}.${e}`:`${l}['${e}']`],r.options["jsx.enabled"]&&(c=it,u=st)}ht(e,r)&&!a?r.options["output.compactBoolean"]||(a=[s]):a||(a=Vt),ut(n," "+s),a?(ut(n,"="+c),Jt(a,t),ut(n,u)):"html"!==r.options["output.selfClosingStyle"]&&ut(n,"="+c+u)}}function ln(e,t,n){if(e.value&&e.children.length){const r=e.value.findIndex(Gt);if(-1!==r){Jt(e.value.slice(0,r),t);const o=t.out.line;let i=r+1;return e.children.forEach(n),t.out.line!==o&&"string"==typeof e.value[i]&&ut(t.out,e.value[i++].trimLeft()),Jt(e.value.slice(i),t),!0}}return!1}function fn(e,t,n,r){const{config:o,parent:i}=r;if(!o.options["output.format"])return!1;if(0===t&&!i)return!1;if(i&&Wt(i)&&1===n.length)return!1;if(Wt(e)&&(Wt(n[t-1])||Wt(n[t+1])||e.value.some(dn)||e.value.some(Gt)&&e.children.length))return!0;if(mt(e,o)){if(0===t){for(let e=0;e<n.length;e++)if(!mt(n[e],o))return!0}else if(!mt(n[t-1],o))return!0;if(o.options["output.inlineBreak"]){let e=1,r=t,i=t;for(;Ht(n[--r],o);)e++;for(;Ht(n[++i],o);)e++;if(e>=o.options["output.inlineBreak"])return!0}for(let t=0,n=e.children.length;t<n;t++)if(fn(e.children[t],t,e.children,r))return!0;return!1}return!0}function dn(e){return"string"==typeof e&&/\r|\n/.test(e)}function pn(e,t,n){return n&&t[`${e}*`]||t[e]}function hn(e){return!sn.has(e)&&/^[a-zA-Z_$][\w_$]*$/.test(e)}function mn(e,t,n){const r=qt(t);return r.options=n||{},Ut(e,gn,r),r.out.value}function gn(e,t,n,r,o){const{out:i,options:s}=r,{primary:a,secondary:c}=function(e){const t=[],n=[];if(e.attributes)for(const r of e.attributes)bn(r)?t.push(r):n.push(r);return{primary:t,secondary:n}}(e),u=r.parent?1:0;i.level+=u,function(e,t,n,r){return!(!r.parent&&0===t)&&!Wt(e)}(e,t,0,r)&&lt(i,!0),!e.name||"div"===e.name&&a.length||ut(i,(s.beforeName||"")+e.name+(s.afterName||"")),function(e,t){for(const n of e)n.value&&("class"===n.name?(ut(t.out,"."),Jt(n.value.map((e=>"string"==typeof e?e.replace(/\s+/g,"."):e)),t)):(ut(t.out,"#"),Jt(n.value,t)))}(a,r),function(e,t){if(e.length){const{out:n,config:r,options:o}=t;o.beforeAttribute&&ut(n,o.beforeAttribute);for(let i=0;i<e.length;i++){const s=e[i];ut(n,dt(s.name||"",r)),ht(s,r)&&!s.value?!r.options["output.compactBoolean"]&&o.booleanValue&&ut(n,"="+o.booleanValue):(ut(n,"="+pt(s,r,!0)),Jt(s.value||Vt,t),ut(n,pt(s,r))),i!==e.length-1&&o.glueAttribute&&ut(n,o.glueAttribute)}o.afterAttribute&&ut(n,o.afterAttribute)}}(c.filter(Qt),r),!e.selfClosing||e.value||e.children.length?(function(e,t){if(!e.value&&e.children.length)return;const n=e.value||Vt,r=function(e){const t=[];let n=[];for(const r of e)if("string"==typeof r){const e=r.split(/\r\n?|\n/g);for(n.push(e.shift()||"");e.length;)t.push(n),n=[e.shift()||""]}else n.push(r);return n.length&&t.push(n),t}(n),{out:o,options:i}=t;if(1===r.length)(e.name||e.attributes)&&ct(o," "),Jt(n,t);else{const e=[];let n=0;for(const t of r){const r=vn(t);e.push(r),r>n&&(n=r)}o.level++;for(let s=0;s<r.length;s++)lt(o,!0),i.beforeTextLine&&ct(o,i.beforeTextLine),Jt(r[s],t),i.afterTextLine&&(ct(o," ".repeat(n-e[s])),ct(o,i.afterTextLine));o.level--}}(e,r),e.children.forEach(o)):r.options.selfClose&&ut(i,r.options.selfClose),i.level-=u}function bn(e){return"class"===e.name||"id"===e.name}function vn(e){let t=0;for(const n of e)t+="string"==typeof n?n.length:n.name.length;return t}const yn={html:an,haml:function(e,t){return mn(e,t,{beforeName:"%",beforeAttribute:"(",afterAttribute:")",glueAttribute:" ",afterTextLine:" |",booleanValue:"true",selfClose:"/"})},slim:function(e,t){return mn(e,t,{beforeAttribute:" ",glueAttribute:" ",beforeTextLine:"| ",selfClose:"/"})},pug:function(e,t){return mn(e,t,{beforeAttribute:"(",afterAttribute:")",glueAttribute:", ",beforeTextLine:"| ",selfClose:"xml"===t.options["output.selfClosingStyle"]?"/":""})}};function xn(e,t){let n;if("string"==typeof e){const r=Object.assign({},t);t.options["jsx.enabled"]&&(r.jsx=!0),t.options["markup.href"]&&(r.href=!0),e=ve(e,r),n=t.text,t.text=void 0}return e=function(e,t){const n=[],r=t.options["output.reverseAttributes"],o=e=>{const i=e.name&&t.snippets[e.name];if(!i||n.includes(i))return null;const s=ve(i,t);n.push(i),ot(s,o),n.pop();for(const t of s.children){if(e.attributes){const n=t.attributes||[],o=e.attributes||[];t.attributes=r?o.concat(n):n.concat(o)}c=t,(a=e).selfClosing&&(c.selfClosing=!0),null!=a.value&&(c.value=a.value),a.repeat&&(c.repeat=a.repeat)}var a,c;return s};return ot(e,o),e}(e,t),function(e,t,n){const r=[e],o=e=>{t(e,r,n),r.push(e),e.children.forEach(o),r.pop()};e.children.forEach(o)}(e,kn,t),t.text=null!=n?n:t.text,e}function wn(e,t){return(yn[t.syntax]||an)(e,t)}function kn(e,t,n){!function(e,t,n){!e.name&&e.attributes&&yt(e,t,n)}(e,t,n),function(e,t){if(!e.attributes)return;const n=[],r={};for(const o of e.attributes)if(o.name){const e=o.name;if(e in r){const n=r[e];"class"===e?n.value=Ze(n.value,o.value," "):Ye(n,o,t)}else n.push(r[e]=Object.assign({},o))}else n.push(o);e.attributes=n}(e,n),function(e,t,n){let r;if(e.name&&(r=e.name.match(wt))){const o=xt[r[1]]||xt.latin,i=r[2]?Math.max(1,Number(r[2])):30,s=kt(i,r[3]?Math.max(i,Number(r[3].slice(1))):i),a=e.repeat||function(e){for(let t=e.length-1;t>=0;t--){const n=e[t];if("AbbreviationNode"===n.type&&n.repeat)return n.repeat}}(t);e.name=e.attributes=void 0,e.value=[St(o,s,!a||0===a.value)],e.repeat&&t.length>1&&yt(e,t,n)}}(e,t,n),"xsl"===n.syntax&&function(e){var t;"xsl:variable"!==(t=e.name)&&"xsl:with-param"!==t||!e.attributes||!e.children.length&&!e.value||(e.attributes=e.attributes.filter(Ot))}(e),"markup"===n.type&&Ft(e),n.options["bem.enabled"]&&function(e,t,n){!function(e){const t=It(e),n=[];for(const e of t.classNames){const t=e.indexOf("_");t>0&&!e.startsWith("-")?(n.push(e.slice(0,t)),n.push(e.slice(t))):n.push(e)}n.length&&(t.classNames=n.filter(Dt),t.block=Mt(t.classNames),Lt(e,t.classNames.join(" ")))}(e),function(e,t,n){const r=It(e),o=[],{options:i}=n,s=t.slice(1).concat(e);for(let e of r.classNames){let t,r="";const a=e;(t=e.match(Et))&&(r=Rt(s,t[1].length,n.context)+i["bem.element"]+t[2],o.push(r),e=e.slice(t[0].length)),(t=e.match(At))&&(r||(r=Rt(s,t[1].length),o.push(r)),o.push(`${r}${i["bem.modifier"]}${t[2]}`),e=e.slice(t[0].length)),e===a&&o.push(a)}const a=o.filter(Dt);a.length&&Lt(e,a.join(" "))}(e,t,n)}(e,t,n)}var Cn;!function(e){e.Raw="Raw",e.Property="Property"}(Cn||(Cn={}));const _n=/^([a-z-]+)(?:\s*:\s*([^\n\r;]+?);*)?$/,Tn={value:!0};function Sn(e,t){const n=t.match(_n);if(n){const t={},r=n[2]?n[2].split("|").map(En):[];for(const e of r)for(const n of e)jn(n,t);return{type:Cn.Property,key:e,property:n[1],value:r,keywords:t,dependencies:[]}}return{type:Cn.Raw,key:e,value:t}}function On(e,t){return e.key===t.key?0:e.key<t.key?-1:1}function En(e){return Xe(e.trim(),Tn)[0].value}function An(e){return e.type===Cn.Property}function jn(e,t){for(const n of e.value)if("Literal"===n.type)t[n.value]=n;else if("FunctionCall"===n.type)t[n.name]=n;else if("Field"===n.type){const e=n.name.trim();e&&(t[e]={type:"Literal",value:e})}}function Pn(e,t,n=!1){if((e=e.toLowerCase())===(t=t.toLowerCase()))return 1;if(!e||!t||e.charCodeAt(0)!==t.charCodeAt(0))return 0;const r=e.length,o=t.length;if(!n&&r>o)return 0;const i=Math.min(r,o),s=Math.max(r,o);let a=1,c=1,u=s,l=0,f=0,d=!1,p=!1;for(;a<r;){for(l=e.charCodeAt(a),d=!1,p=!1;c<o;){if(f=t.charCodeAt(c),l===f){d=!0,u+=s-(p?a:c);break}p=45===f,c++}if(!d){if(!n)return 0;break}a++}const h=s-i;return u*(a/s)/(In(s)-In(h))}function In(e){return e*(e+1)/2}function $n(e,t){return e.r||e.g||e.b||e.a?1===e.a?function(e,t){const n=t&&Mn(e.r)&&Mn(e.g)&&Mn(e.b)?Nn:Ln;return"#"+n(e.r)+n(e.g)+n(e.b)}(e,t):function(e){const t=[e.r,e.g,e.b];return 1!==e.a&&t.push(Rn(e.a,8)),`${3===t.length?"rgb":"rgba"}(${t.join(", ")})`}(e):"transparent"}function Rn(e,t=4){return e.toFixed(t).replace(/\.?0+$/,"")}function Mn(e){return!(e%17)}function Nn(e){return(e>>4).toString(16)}function Ln(e){return function(e,t){for(;e.length<2;)e="0"+e;return e}(e.toString(16))}const zn={Global:"@@global",Section:"@@section",Property:"@@property",Value:"@@value"};function Dn(e,t){var n;const r=at(t.options),o=t.options["output.format"];(null===(n=t.context)||void 0===n?void 0:n.name)===zn.Section&&(e=e.filter((e=>e.snippet)));for(let n=0;n<e.length;n++)o&&0!==n&&lt(r,!0),Fn(e[n],r,t);return r.value}function Fn(e,t,n){const r=n.options["stylesheet.json"];if(e.name)ut(t,(r?e.name.replace(/\-(\w)/g,((e,t)=>t.toUpperCase())):e.name)+n.options["stylesheet.between"]),e.value.length?function(e,t,n){const r=n.options["stylesheet.json"],o=r?function(e){if(1===e.value.length){const t=e.value[0];if(1===t.value.length&&"NumberValue"===t.value[0].type)return t.value[0]}}(e):null;if(!o||o.unit&&"px"!==o.unit){const o=function(e){return e.options["stylesheet.jsonDoubleQuotes"]?'"':"'"}(n);r&&ct(t,o);for(let r=0;r<e.value.length;r++)0!==r&&ct(t,", "),Un(e.value[r],t,n);r&&ct(t,o)}else ct(t,String(o.value))}(e,t,n):ft(t,0,""),r?ct(t,","):(Bn(e,t,!0),ct(t,n.options["stylesheet.after"]));else{for(const r of e.value)for(const e of r.value)qn(e,t,n);Bn(e,t,e.value.length>0)}}function Bn(e,t,n){e.important&&(n&&ct(t," "),ct(t,"!important"))}function Un(e,t,n){for(let r=0,o=-1;r<e.value.length;r++){const i=e.value[r];0===r||"Field"===i.type&&i.start===o||ct(t," "),qn(i,t,n),o=i.end}}function qn(e,t,n){if("ColorValue"===e.type)ct(t,$n(e,n.options["stylesheet.shortHex"]));else if("Literal"===e.type||"CustomProperty"===e.type)ut(t,e.value);else if("NumberValue"===e.type)ut(t,Rn(e.value,4)+e.unit);else if("StringValue"===e.type){const n="double"===e.quote?'"':"'";ut(t,n+e.value+n)}else if("Field"===e.type)ft(t,e.index,e.name);else if("FunctionCall"===e.type){ct(t,e.name+"(");for(let r=0;r<e.arguments.length;r++)r&&ct(t,", "),Un(e.arguments[r],t,n);ct(t,")")}}const Vn="lg";function Wn(e,t){var n;const r=(null===(n=t.cache)||void 0===n?void 0:n.stylesheetSnippets)||Hn(t.snippets);t.cache&&(t.cache.stylesheetSnippets=r),"string"==typeof e&&(e=Xe(e,{value:rr(t)}));const o=function(e,t){if(t.context){if(t.context.name===zn.Section)return e.filter((e=>e.type===Cn.Raw));if(t.context.name===zn.Property)return e.filter((e=>e.type===Cn.Property))}return e}(r,t);for(const n of e)Gn(n,o,t);return e}function Hn(e){const t=[];for(const n of Object.keys(e))t.push(Sn(n,e[n]));return function(e){e=e.slice().sort(On);const t=[];let n;for(const r of e.filter(An)){for(;t.length;){if(n=t[t.length-1],r.property.startsWith(n.property)&&45===r.property.charCodeAt(n.property.length)){n.dependencies.push(r),t.push(r);break}t.pop()}t.length||t.push(r)}return e}(t)}function Gn(e,t,n){if(!function(e,t){let n=null;const r=1===e.value.length?e.value[0]:null;if(r&&1===r.value.length){const e=r.value[0];"FunctionCall"===e.type&&e.name===Vn&&(n=e)}return!(!n&&e.name!==Vn)&&(n=n?Object.assign(Object.assign({},n),{name:"linear-gradient"}):{type:"FunctionCall",name:"linear-gradient",arguments:[Zn(er(0,""))]},t.context||(e.name="background-image"),e.value=[Zn(n)],!0)}(e,n)){const r=n.options["stylesheet.fuzzySearchMinScore"];if(rr(n)){const o=n.context.name,i=t.find((e=>e.type===Cn.Property&&e.property===o));Jn(e,n,i,r),e.snippet=i}else if(e.name){const o=Qn(e.name,t,r,!0);e.snippet=o,o&&(o.type===Cn.Property?function(e,t,n){const r=function(e,t){for(let n=0,r=0;n<e.length;n++){if(r=t.indexOf(e[n],r),-1===r)return e.slice(n);r++}return""}(e.name,t.key);if(r){if(e.value.length)return e;const o=Xn(r,n,t);if(!o)return e;e.value.push(Zn(o))}if(e.name=t.property,e.value.length)Jn(e,n,t);else if(t.value.length){const r=t.value[0];e.value=1===t.value.length||r.some(tr)?r:r.map((e=>nr(e,n)))}}(e,o,n):function(e,t){let n,r=0;const o=/\$\{(\d+)(:[^}]+)?\}/g,i=e.value[0],s=[];for(;n=o.exec(t.value);)r!==n.index&&s.push(Yn(t.value.slice(r,n.index))),r=n.index+n[0].length,i&&i.value.length?s.push(i.value.shift()):s.push(er(Number(n[1]),n[2]?n[2].slice(1):""));const a=t.value.slice(r);a&&s.push(Yn(a)),e.name=void 0,e.value=[Zn(...s)]}(e,o))}}return(e.name||n.context)&&function(e,t){const n=t.options["stylesheet.unitAliases"],r=t.options["stylesheet.unitless"];for(const o of e.value)for(const i of o.value)"NumberValue"===i.type&&(i.unit?i.unit=n[i.unit]||i.unit:0===i.value||r.includes(e.name)||(i.unit=i.rawValue.includes(".")?t.options["stylesheet.floatUnit"]:t.options["stylesheet.intUnit"]))}(e,n),e}function Jn(e,t,n,r){for(const o of e.value){const e=[];for(const i of o.value)if("Literal"===i.type)e.push(Xn(i.value,t,n,r)||i);else if("FunctionCall"===i.type){const o=Xn(i.name,t,n,r);o&&"FunctionCall"===o.type?e.push(Object.assign(Object.assign({},o),{arguments:i.arguments.concat(o.arguments.slice(i.arguments.length))})):e.push(i)}else e.push(i);o.value=e}}function Qn(e,t,n=0,r=!1){let o=null,i=0;for(const n of t){const t=Pn(e,Kn(n),r);if(1===t)return n;t&&t>=i&&(i=t,o=n)}return i>=n?o:null}function Kn(e){return"string"==typeof e?e:e.key}function Xn(e,t,n,r){let o;if(n){if(o=Qn(e,Object.keys(n.keywords),r))return n.keywords[o];for(const t of n.dependencies)if(o=Qn(e,Object.keys(t.keywords),r))return t.keywords[o]}return(o=Qn(e,t.options["stylesheet.keywords"],r))?Yn(o):null}function Zn(...e){return{type:"CSSValue",value:e}}function Yn(e){return{type:"Literal",value:e}}function er(e,t){return{type:"Field",index:e,name:t}}function tr(e){for(const t of e.value)if("Field"===t.type||"FunctionCall"===t.type&&t.arguments.some(tr))return!0;return!1}function nr(e,t,n={index:1}){let r=[];for(const o of e.value)switch(o.type){case"ColorValue":r.push(er(n.index++,$n(o,t.options["stylesheet.shortHex"])));break;case"Literal":r.push(er(n.index++,o.value));break;case"NumberValue":r.push(er(n.index++,`${o.value}${o.unit}`));break;case"StringValue":const e="single"===o.quote?"'":'"';r.push(er(n.index++,e+o.value+e));break;case"FunctionCall":r.push(er(n.index++,o.name),Yn("("));for(let e=0,i=o.arguments.length;e<i;e++)r=r.concat(nr(o.arguments[e],t,n).value),e!==i-1&&r.push(Yn(", "));r.push(Yn(")"));break;default:r.push(o)}return Object.assign(Object.assign({},e),{value:r})}function rr(e){return!(!e.context||e.context.name!==zn.Value&&e.context.name.startsWith("@@"))}const or={markup:"html",stylesheet:"css"},ir={type:"markup",syntax:"html",variables:{lang:"en",locale:"en-US",charset:"UTF-8",indentation:"\t",newline:"\n"},snippets:{},options:{inlineElements:["a","abbr","acronym","applet","b","basefont","bdo","big","br","button","cite","code","del","dfn","em","font","i","iframe","img","input","ins","kbd","label","map","object","q","s","samp","select","small","span","strike","strong","sub","sup","textarea","tt","u","var"],"output.indent":"\t","output.baseIndent":"","output.newline":"\n","output.tagCase":"","output.attributeCase":"","output.attributeQuotes":"double","output.format":!0,"output.formatLeafNode":!1,"output.formatSkip":["html"],"output.formatForce":["body"],"output.inlineBreak":3,"output.compactBoolean":!1,"output.booleanAttributes":["contenteditable","seamless","async","autofocus","autoplay","checked","controls","defer","disabled","formnovalidate","hidden","ismap","loop","multiple","muted","novalidate","readonly","required","reversed","selected","typemustmatch"],"output.reverseAttributes":!1,"output.selfClosingStyle":"html","output.field":(e,t)=>t,"output.text":e=>e,"markup.href":!0,"comment.enabled":!1,"comment.trigger":["id","class"],"comment.before":"","comment.after":"\n\x3c!-- /[#ID][.CLASS] --\x3e","bem.enabled":!1,"bem.element":"__","bem.modifier":"_","jsx.enabled":!1,"stylesheet.keywords":["auto","inherit","unset","none"],"stylesheet.unitless":["z-index","line-height","opacity","font-weight","zoom","flex","flex-grow","flex-shrink"],"stylesheet.shortHex":!0,"stylesheet.between":": ","stylesheet.after":";","stylesheet.intUnit":"px","stylesheet.floatUnit":"em","stylesheet.unitAliases":{e:"em",p:"%",x:"ex",r:"rem"},"stylesheet.json":!1,"stylesheet.jsonDoubleQuotes":!1,"stylesheet.fuzzySearchMinScore":0}},sr={markup:{snippets:ar({a:"a[href]","a:blank":"a[href='http://${0}' target='_blank' rel='noopener noreferrer']","a:link":"a[href='http://${0}']","a:mail":"a[href='mailto:${0}']","a:tel":"a[href='tel:+${0}']",abbr:"abbr[title]","acr|acronym":"acronym[title]",base:"base[href]/",basefont:"basefont/",br:"br/",frame:"frame/",hr:"hr/",bdo:"bdo[dir]","bdo:r":"bdo[dir=rtl]","bdo:l":"bdo[dir=ltr]",col:"col/",link:"link[rel=stylesheet href]/","link:css":"link[href='${1:style}.css']","link:print":"link[href='${1:print}.css' media=print]","link:favicon":"link[rel='shortcut icon' type=image/x-icon href='${1:favicon.ico}']","link:mf|link:manifest":"link[rel='manifest' href='${1:manifest.json}']","link:touch":"link[rel=apple-touch-icon href='${1:favicon.png}']","link:rss":"link[rel=alternate type=application/rss+xml title=RSS href='${1:rss.xml}']","link:atom":"link[rel=alternate type=application/atom+xml title=Atom href='${1:atom.xml}']","link:im|link:import":"link[rel=import href='${1:component}.html']",meta:"meta/","meta:utf":"meta[http-equiv=Content-Type content='text/html;charset=UTF-8']","meta:vp":"meta[name=viewport content='width=${1:device-width}, initial-scale=${2:1.0}']","meta:compat":"meta[http-equiv=X-UA-Compatible content='${1:IE=7}']","meta:edge":"meta:compat[content='${1:ie=edge}']","meta:redirect":"meta[http-equiv=refresh content='0; url=${1:http://example.com}']","meta:refresh":"meta[http-equiv=refresh content='${1:5}']","meta:kw":"meta[name=keywords content]","meta:desc":"meta[name=description content]",style:"style",script:"script","script:src":"script[src]","script:module":"script[type=module src]",img:"img[src alt]/","img:s|img:srcset":"img[srcset src alt]","img:z|img:sizes":"img[sizes srcset src alt]",picture:"picture","src|source":"source/","src:sc|source:src":"source[src type]","src:s|source:srcset":"source[srcset]","src:t|source:type":"source[srcset type='${1:image/}']","src:z|source:sizes":"source[sizes srcset]","src:m|source:media":"source[media='(${1:min-width: })' srcset]","src:mt|source:media:type":"source:media[type='${2:image/}']","src:mz|source:media:sizes":"source:media[sizes srcset]","src:zt|source:sizes:type":"source[sizes srcset type='${1:image/}']",iframe:"iframe[src frameborder=0]",embed:"embed[src type]/",object:"object[data type]",param:"param[name value]/",map:"map[name]",area:"area[shape coords href alt]/","area:d":"area[shape=default]","area:c":"area[shape=circle]","area:r":"area[shape=rect]","area:p":"area[shape=poly]",form:"form[action]","form:get":"form[method=get]","form:post":"form[method=post]",label:"label[for]",input:"input[type=${1:text}]/",inp:"input[name=${1} id=${1}]","input:h|input:hidden":"input[type=hidden name]","input:t|input:text":"inp[type=text]","input:search":"inp[type=search]","input:email":"inp[type=email]","input:url":"inp[type=url]","input:p|input:password":"inp[type=password]","input:datetime":"inp[type=datetime]","input:date":"inp[type=date]","input:datetime-local":"inp[type=datetime-local]","input:month":"inp[type=month]","input:week":"inp[type=week]","input:time":"inp[type=time]","input:tel":"inp[type=tel]","input:number":"inp[type=number]","input:color":"inp[type=color]","input:c|input:checkbox":"inp[type=checkbox]","input:r|input:radio":"inp[type=radio]","input:range":"inp[type=range]","input:f|input:file":"inp[type=file]","input:s|input:submit":"input[type=submit value]","input:i|input:image":"input[type=image src alt]","input:b|input:btn|input:button":"input[type=button value]","input:reset":"input:button[type=reset]",isindex:"isindex/",select:"select[name=${1} id=${1}]","select:d|select:disabled":"select[disabled.]","opt|option":"option[value]",textarea:"textarea[name=${1} id=${1}]","tarea:c|textarea:cols":"textarea[name=${1} id=${1} cols=${2:30}]","tarea:r|textarea:rows":"textarea[name=${1} id=${1} rows=${3:10}]","tarea:cr|textarea:cols:rows":"textarea[name=${1} id=${1} cols=${2:30} rows=${3:10}]",marquee:"marquee[behavior direction]","menu:c|menu:context":"menu[type=context]","menu:t|menu:toolbar":"menu[type=toolbar]",video:"video[src]",audio:"audio[src]","html:xml":"html[xmlns=http://www.w3.org/1999/xhtml]",keygen:"keygen/",command:"command/","btn:s|button:s|button:submit":"button[type=submit]","btn:r|button:r|button:reset":"button[type=reset]","btn:b|button:b|button:button":"button[type=button]","btn:d|button:d|button:disabled":"button[disabled.]","fst:d|fset:d|fieldset:d|fieldset:disabled":"fieldset[disabled.]",bq:"blockquote",fig:"figure",figc:"figcaption",pic:"picture",ifr:"iframe",emb:"embed",obj:"object",cap:"caption",colg:"colgroup",fst:"fieldset",btn:"button",optg:"optgroup",tarea:"textarea",leg:"legend",sect:"section",art:"article",hdr:"header",ftr:"footer",adr:"address",dlg:"dialog",str:"strong",prog:"progress",mn:"main",tem:"template",fset:"fieldset",datal:"datalist",kg:"keygen",out:"output",det:"details",sum:"summary",cmd:"command",data:"data[value]",meter:"meter[value]",time:"time[datetime]","ri:d|ri:dpr":"img:s","ri:v|ri:viewport":"img:z","ri:a|ri:art":"pic>src:m+img","ri:t|ri:type":"pic>src:t+img","!!!":"{<!DOCTYPE html>}",doc:"html[lang=${lang}]>(head>meta[charset=${charset}]+meta:vp+title{${1:Document}})+body","!|html:5":"!!!+doc",c:"{\x3c!-- ${0} --\x3e}","cc:ie":"{\x3c!--[if IE]>${0}<![endif]--\x3e}","cc:noie":"{\x3c!--[if !IE]>\x3c!--\x3e${0}\x3c!--<![endif]--\x3e}"})},xhtml:{options:{"output.selfClosingStyle":"xhtml"}},xml:{options:{"output.selfClosingStyle":"xml"}},xsl:{snippets:ar({"tm|tmatch":"xsl:template[match mode]","tn|tname":"xsl:template[name]",call:"xsl:call-template[name]",ap:"xsl:apply-templates[select mode]",api:"xsl:apply-imports",imp:"xsl:import[href]",inc:"xsl:include[href]",ch:"xsl:choose","wh|xsl:when":"xsl:when[test]",ot:"xsl:otherwise",if:"xsl:if[test]",par:"xsl:param[name]",pare:"xsl:param[name select]",var:"xsl:variable[name]",vare:"xsl:variable[name select]",wp:"xsl:with-param[name select]",key:"xsl:key[name match use]",elem:"xsl:element[name]",attr:"xsl:attribute[name]",attrs:"xsl:attribute-set[name]",cp:"xsl:copy[select]",co:"xsl:copy-of[select]",val:"xsl:value-of[select]","for|each":"xsl:for-each[select]",tex:"xsl:text",com:"xsl:comment",msg:"xsl:message[terminate=no]",fall:"xsl:fallback",num:"xsl:number[value]",nam:"namespace-alias[stylesheet-prefix result-prefix]",pres:"xsl:preserve-space[elements]",strip:"xsl:strip-space[elements]",proc:"xsl:processing-instruction[name]",sort:"xsl:sort[select order]",choose:"xsl:choose>xsl:when+xsl:otherwise",xsl:"!!!+xsl:stylesheet[version=1.0 xmlns:xsl=http://www.w3.org/1999/XSL/Transform]>{\n|}","!!!":'{<?xml version="1.0" encoding="UTF-8"?>}'}),options:{"output.selfClosingStyle":"xml"}},jsx:{options:{"jsx.enabled":!0,"markup.attributes":{class:"className","class*":"styleName",for:"htmlFor"},"markup.valuePrefix":{"class*":"styles"}}},vue:{options:{"markup.attributes":{"class*":":class"}}},svelte:{options:{"jsx.enabled":!0}},pug:{snippets:ar({"!!!":"{doctype html}"})},stylesheet:{snippets:ar({"@f":"@font-face {\n\tfont-family: ${1};\n\tsrc: url(${2});\n}","@ff":"@font-face {\n\tfont-family: '${1:FontName}';\n\tsrc: url('${2:FileName}.eot');\n\tsrc: url('${2:FileName}.eot?#iefix') format('embedded-opentype'),\n\t\t url('${2:FileName}.woff') format('woff'),\n\t\t url('${2:FileName}.ttf') format('truetype'),\n\t\t url('${2:FileName}.svg#${1:FontName}') format('svg');\n\tfont-style: ${3:normal};\n\tfont-weight: ${4:normal};\n}","@i|@import":"@import url(${0});","@kf":"@keyframes ${1:identifier} {\n\t${2}\n}","@m|@media":"@media ${1:screen} {\n\t${0}\n}",ac:"align-content:start|end|flex-start|flex-end|center|space-between|space-around|stretch|space-evenly",ai:"align-items:start|end|flex-start|flex-end|center|baseline|stretch",anim:"animation:${1:name} ${2:duration} ${3:timing-function} ${4:delay} ${5:iteration-count} ${6:direction} ${7:fill-mode}",animdel:"animation-delay:time",animdir:"animation-direction:normal|reverse|alternate|alternate-reverse",animdur:"animation-duration:${1:0}s",animfm:"animation-fill-mode:both|forwards|backwards",animic:"animation-iteration-count:1|infinite",animn:"animation-name",animps:"animation-play-state:running|paused",animtf:"animation-timing-function:linear|ease|ease-in|ease-out|ease-in-out|cubic-bezier(${1:0.1}, ${2:0.7}, ${3:1.0}, ${3:0.1})",ap:"appearance:none",as:"align-self:start|end|auto|flex-start|flex-end|center|baseline|stretch",b:"bottom",bd:"border:${1:1px} ${2:solid} ${3:#000}",bdb:"border-bottom:${1:1px} ${2:solid} ${3:#000}",bdbc:"border-bottom-color:${1:#000}",bdbi:"border-bottom-image:url(${0})",bdbk:"border-break:close",bdbli:"border-bottom-left-image:url(${0})|continue",bdblrs:"border-bottom-left-radius",bdbri:"border-bottom-right-image:url(${0})|continue",bdbrrs:"border-bottom-right-radius",bdbs:"border-bottom-style",bdbw:"border-bottom-width",bdc:"border-color:${1:#000}",bdci:"border-corner-image:url(${0})|continue",bdcl:"border-collapse:collapse|separate",bdf:"border-fit:repeat|clip|scale|stretch|overwrite|overflow|space",bdi:"border-image:url(${0})",bdl:"border-left:${1:1px} ${2:solid} ${3:#000}",bdlc:"border-left-color:${1:#000}",bdlen:"border-length",bdli:"border-left-image:url(${0})",bdls:"border-left-style",bdlw:"border-left-width",bdr:"border-right:${1:1px} ${2:solid} ${3:#000}",bdrc:"border-right-color:${1:#000}",bdri:"border-right-image:url(${0})",bdrs:"border-radius",bdrst:"border-right-style",bdrw:"border-right-width",bds:"border-style:none|hidden|dotted|dashed|solid|double|dot-dash|dot-dot-dash|wave|groove|ridge|inset|outset",bdsp:"border-spacing",bdt:"border-top:${1:1px} ${2:solid} ${3:#000}",bdtc:"border-top-color:${1:#000}",bdti:"border-top-image:url(${0})",bdtli:"border-top-left-image:url(${0})|continue",bdtlrs:"border-top-left-radius",bdtri:"border-top-right-image:url(${0})|continue",bdtrrs:"border-top-right-radius",bdts:"border-top-style",bdtw:"border-top-width",bdw:"border-width",bbs:"border-block-start",bbe:"border-block-end",bis:"border-inline-start",bie:"border-inline-end",bfv:"backface-visibility:hidden|visible",bg:"background:${1:#000}","bg:n":"background: none",bga:"background-attachment:fixed|scroll",bgbk:"background-break:bounding-box|each-box|continuous",bgc:"background-color:${1:#fff}",bgcp:"background-clip:padding-box|border-box|content-box|no-clip",bgi:"background-image:url(${0})",bgo:"background-origin:padding-box|border-box|content-box",bgp:"background-position:${1:0} ${2:0}",bgpx:"background-position-x",bgpy:"background-position-y",bgr:"background-repeat:no-repeat|repeat-x|repeat-y|space|round",bgsz:"background-size:contain|cover",bs:"block-size",bxsh:"box-shadow:${1:inset }${2:hoff} ${3:voff} ${4:blur} ${5:#000}|none",bxsz:"box-sizing:border-box|content-box|border-box",c:"color:${1:#000}",cr:"color:rgb(${1:0}, ${2:0}, ${3:0})",cra:"color:rgba(${1:0}, ${2:0}, ${3:0}, ${4:.5})",cl:"clear:both|left|right|none",cm:"/* ${0} */",cnt:"content:'${0}'|normal|open-quote|no-open-quote|close-quote|no-close-quote|attr(${0})|counter(${0})|counters(${0})",coi:"counter-increment",colm:"columns",colmc:"column-count",colmf:"column-fill",colmg:"column-gap",colmr:"column-rule",colmrc:"column-rule-color",colmrs:"column-rule-style",colmrw:"column-rule-width",colms:"column-span",colmw:"column-width",cor:"counter-reset",cp:"clip:auto|rect(${1:top} ${2:right} ${3:bottom} ${4:left})",cps:"caption-side:top|bottom",cur:"cursor:pointer|auto|default|crosshair|hand|help|move|pointer|text",d:"display:block|none|flex|inline-flex|inline|inline-block|grid|inline-grid|subgrid|list-item|run-in|contents|table|inline-table|table-caption|table-column|table-column-group|table-header-group|table-footer-group|table-row|table-row-group|table-cell|ruby|ruby-base|ruby-base-group|ruby-text|ruby-text-group",ec:"empty-cells:show|hide",f:"font:${1:1em} ${2:sans-serif}",fd:"font-display:auto|block|swap|fallback|optional",fef:"font-effect:none|engrave|emboss|outline",fem:"font-emphasize",femp:"font-emphasize-position:before|after",fems:"font-emphasize-style:none|accent|dot|circle|disc",ff:"font-family:serif|sans-serif|cursive|fantasy|monospace",fft:'font-family:"Times New Roman", Times, Baskerville, Georgia, serif',ffa:'font-family:Arial, "Helvetica Neue", Helvetica, sans-serif',ffv:"font-family:Verdana, Geneva, sans-serif",fl:"float:left|right|none",fs:"font-style:italic|normal|oblique",fsm:"font-smoothing:antialiased|subpixel-antialiased|none",fst:"font-stretch:normal|ultra-condensed|extra-condensed|condensed|semi-condensed|semi-expanded|expanded|extra-expanded|ultra-expanded",fv:"font-variant:normal|small-caps",fvs:"font-variation-settings:normal|inherit|initial|unset",fw:"font-weight:normal|bold|bolder|lighter",fx:"flex",fxb:"flex-basis:fill|max-content|min-content|fit-content|content",fxd:"flex-direction:row|row-reverse|column|column-reverse",fxf:"flex-flow",fxg:"flex-grow",fxsh:"flex-shrink",fxw:"flex-wrap:nowrap|wrap|wrap-reverse",fsz:"font-size",fsza:"font-size-adjust",g:"gap",gtc:"grid-template-columns:repeat(${0})|minmax()",gtr:"grid-template-rows:repeat(${0})|minmax()",gta:"grid-template-areas",gt:"grid-template",gg:"grid-gap",gcg:"grid-column-gap",grg:"grid-row-gap",gac:"grid-auto-columns:auto|minmax()",gar:"grid-auto-rows:auto|minmax()",gaf:"grid-auto-flow:row|column|dense|inherit|initial|unset",gd:"grid",gc:"grid-column",gcs:"grid-column-start",gce:"grid-column-end",gr:"grid-row",grs:"grid-row-start",gre:"grid-row-end",ga:"grid-area",h:"height",is:"inline-size",jc:"justify-content:start|end|stretch|flex-start|flex-end|center|space-between|space-around|space-evenly",ji:"justify-items:start|end|center|stretch",js:"justify-self:start|end|center|stretch",l:"left",lg:"background-image:linear-gradient(${1})",lh:"line-height",lis:"list-style",lisi:"list-style-image",lisp:"list-style-position:inside|outside",list:"list-style-type:disc|circle|square|decimal|decimal-leading-zero|lower-roman|upper-roman",lts:"letter-spacing:normal",m:"margin",mah:"max-height",mar:"max-resolution",maw:"max-width",mb:"margin-bottom",mih:"min-height",mir:"min-resolution",miw:"min-width",ml:"margin-left",mr:"margin-right",mt:"margin-top",mbs:"margin-block-start",mbe:"margin-block-end",mis:"margin-inline-start",mie:"margin-inline-end",ol:"outline",olc:"outline-color:${1:#000}|invert",olo:"outline-offset",ols:"outline-style:none|dotted|dashed|solid|double|groove|ridge|inset|outset",olw:"outline-width:thin|medium|thick","op|opa":"opacity",ord:"order",ori:"orientation:landscape|portrait",orp:"orphans",ov:"overflow:hidden|visible|hidden|scroll|auto",ovs:"overflow-style:scrollbar|auto|scrollbar|panner|move|marquee",ovx:"overflow-x:hidden|visible|hidden|scroll|auto",ovy:"overflow-y:hidden|visible|hidden|scroll|auto",p:"padding",pb:"padding-bottom",pgba:"page-break-after:auto|always|left|right",pgbb:"page-break-before:auto|always|left|right",pgbi:"page-break-inside:auto|avoid",pl:"padding-left",pos:"position:relative|absolute|relative|fixed|static",pr:"padding-right",pt:"padding-top",pbs:"padding-block-start",pbe:"padding-block-end",pis:"padding-inline-start",pie:"padding-inline-end",spbs:"scroll-padding-block-start",spbe:"scroll-padding-block-end",spis:"scroll-padding-inline-start",spie:"scroll-padding-inline-end",q:"quotes",qen:"quotes:'\\201C' '\\201D' '\\2018' '\\2019'",qru:"quotes:'\\00AB' '\\00BB' '\\201E' '\\201C'",r:"right",rsz:"resize:none|both|horizontal|vertical",t:"top",ta:"text-align:left|center|right|justify",tal:"text-align-last:left|center|right",tbl:"table-layout:fixed",td:"text-decoration:none|underline|overline|line-through",te:"text-emphasis:none|accent|dot|circle|disc|before|after",th:"text-height:auto|font-size|text-size|max-size",ti:"text-indent",tj:"text-justify:auto|inter-word|inter-ideograph|inter-cluster|distribute|kashida|tibetan",to:"text-outline:${1:0} ${2:0} ${3:#000}",tov:"text-overflow:ellipsis|clip",tr:"text-replace",trf:"transform:${1}|skewX(${1:angle})|skewY(${1:angle})|scale(${1:x}, ${2:y})|scaleX(${1:x})|scaleY(${1:y})|scaleZ(${1:z})|scale3d(${1:x}, ${2:y}, ${3:z})|rotate(${1:angle})|rotateX(${1:angle})|rotateY(${1:angle})|rotateZ(${1:angle})|translate(${1:x}, ${2:y})|translateX(${1:x})|translateY(${1:y})|translateZ(${1:z})|translate3d(${1:tx}, ${2:ty}, ${3:tz})",trfo:"transform-origin",trfs:"transform-style:preserve-3d",trs:"transition:${1:prop} ${2:time}",trsde:"transition-delay:${1:time}",trsdu:"transition-duration:${1:time}",trsp:"transition-property:${1:prop}",trstf:"transition-timing-function:${1:fn}",tsh:"text-shadow:${1:hoff} ${2:voff} ${3:blur} ${4:#000}",tt:"text-transform:uppercase|lowercase|capitalize|none",tw:"text-wrap:none|normal|unrestricted|suppress",us:"user-select:none",v:"visibility:hidden|visible|collapse",va:"vertical-align:top|super|text-top|middle|baseline|bottom|text-bottom|sub","w|wid":"width",whs:"white-space:nowrap|pre|pre-wrap|pre-line|normal",whsc:"white-space-collapse:normal|keep-all|loose|break-strict|break-all",wido:"widows",wm:"writing-mode:lr-tb|lr-tb|lr-bt|rl-tb|rl-bt|tb-rl|tb-lr|bt-lr|bt-rl",wob:"word-break:normal|keep-all|break-all",wos:"word-spacing",wow:"word-wrap:none|unrestricted|suppress|break-word|normal",z:"z-index",zom:"zoom:1"})},sass:{options:{"stylesheet.after":""}},stylus:{options:{"stylesheet.between":" ","stylesheet.after":""}}};function ar(e){const t={};return Object.keys(e).forEach((n=>{for(const r of n.split("|"))t[r]=e[n]})),t}function cr(e={},t={}){const n=e.type||"markup",r=e.syntax||or[n];return Object.assign(Object.assign(Object.assign({},ir),e),{type:n,syntax:r,variables:ur(n,r,"variables",e,t),snippets:ur(n,r,"snippets",e,t),options:ur(n,r,"options",e,t)})}function ur(e,t,n,r,o={}){const i=sr[e],s=o[e],a=sr[t],c=o[t];return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},ir[n]),i&&i[n]),a&&a[n]),s&&s[n]),c&&c[n]),r[n])}function lr(e,t=0){return{text:e,start:t,pos:e.length}}function fr(e){return e.pos===e.start}function dr(e,t=0){return e.text.charCodeAt(e.pos-1+t)}function pr(e){if(!fr(e))return e.text.charCodeAt(--e.pos)}function hr(e,t){if(fr(e))return!1;const n="function"==typeof t?t(dr(e)):t===dr(e);return n&&e.pos--,!!n}function mr(e,t){const n=e.pos;for(;hr(e,t););return e.pos<n}var gr,br;function vr(e){return e===gr.SingleQuote||e===gr.DoubleQuote}!function(e){e[e.SingleQuote=39]="SingleQuote",e[e.DoubleQuote=34]="DoubleQuote",e[e.Escape=92]="Escape"}(gr||(gr={})),function(e){e[e.SquareL=91]="SquareL",e[e.SquareR=93]="SquareR",e[e.RoundL=40]="RoundL",e[e.RoundR=41]="RoundR",e[e.CurlyL=123]="CurlyL",e[e.CurlyR=125]="CurlyR"}(br||(br={}));const yr={[br.SquareL]:br.SquareR,[br.RoundL]:br.RoundR,[br.CurlyL]:br.CurlyR};var xr;function wr(e){const t=e.pos;if(!hr(e,xr.AngleRight))return!1;let n=!1;for(hr(e,xr.Slash);!fr(e);){if(mr(e,Sr),_r(e)){if(hr(e,xr.Slash)){n=hr(e,xr.AngleLeft);break}if(hr(e,xr.AngleLeft)){n=!0;break}if(hr(e,Sr))continue;if(hr(e,xr.Equals)){if(_r(e))continue;break}if(Cr(e)){n=!0;break}break}if(!kr(e))break}return e.pos=t,n}function kr(e){return function(e){const t=e.pos;return!!(function(e){const t=e.pos,n=pr(e);if(vr(n))for(;!fr(e);)if(pr(e)===n&&dr(e)!==gr.Escape)return!0;return e.pos=t,!1}(e)&&hr(e,xr.Equals)&&_r(e))||(e.pos=t,!1)}(e)||Cr(e)}function Cr(e){const t=e.pos,n=[];for(;!fr(e);){const t=dr(e);if(Ar(t))n.push(t);else if(Er(t)){if(n.pop()!==yr[t])break}else if(!Or(t))break;e.pos--}return!(t===e.pos||!hr(e,xr.Equals)||!_r(e))||(e.pos=t,!1)}function _r(e){return mr(e,Tr)}function Tr(e){return e===xr.Colon||e===xr.Dash||function(e){return(e&=-33)>=65&&e<=90}(e)||function(e){return e>47&&e<58}(e)}function Sr(e){return e===xr.Space||e===xr.Tab}function Or(e){return!isNaN(e)&&e!==xr.Equals&&!Sr(e)&&!vr(e)}function Er(e){return e===br.CurlyL||e===br.RoundL||e===br.SquareL}function Ar(e){return e===br.CurlyR||e===br.RoundR||e===br.SquareR}!function(e){e[e.Tab=9]="Tab",e[e.Space=32]="Space",e[e.Dash=45]="Dash",e[e.Slash=47]="Slash",e[e.Colon=58]="Colon",e[e.Equals=61]="Equals",e[e.AngleLeft=60]="AngleLeft",e[e.AngleRight=62]="AngleRight"}(xr||(xr={}));const jr=e=>e.charCodeAt(0),Pr="#.*:$-_!@%^+>/".split("").map(jr),Ir={type:"markup",lookAhead:!0,prefix:""};function $r(e,t=e.length,n={}){const r=Object.assign(Object.assign({},Ir),n);let o;t=Math.min(e.length,Math.max(0,null==t?e.length:t)),r.lookAhead&&(t=function(e,t,n){for(vr(e.charCodeAt(t))&&t++;zr(e.charCodeAt(t),n.type);)t++;return t}(e,t,r));const i=function(e,t,n){if(!n)return 0;const r=lr(e),o=n.split("").map(jr);let i;for(r.pos=t;!fr(r);)if(!Rr(r,br.SquareR,br.SquareL)&&!Rr(r,br.CurlyR,br.CurlyL)){if(i=r.pos,Mr(r,o))return i;r.pos--}return-1}(e,t,r.prefix||"");if(-1===i)return;const s=lr(e,i);s.pos=t;const a=[];for(;!fr(s);){if(o=dr(s),a.includes(br.CurlyR)){if(o===br.CurlyR){a.push(o),s.pos--;continue}if(o!==br.CurlyL){s.pos--;continue}}if(zr(o,r.type))a.push(o);else if(Lr(o,r.type)){if(a.pop()!==yr[o])break}else{if(a.includes(br.SquareR)||a.includes(br.CurlyR)){s.pos--;continue}if(wr(s)||!Nr(o))break}s.pos--}if(!a.length&&s.pos!==t){const r=e.slice(s.pos,t).replace(/^[*+>^]+/,"");return{abbreviation:r,location:t-r.length,start:n.prefix?i-n.prefix.length:t-r.length,end:t}}}function Rr(e,t,n){const r=e.pos;if(hr(e,t))for(;!fr(e);){if(hr(e,n))return!0;e.pos--}return e.pos=r,!1}function Mr(e,t){const n=e.pos;let r=!1;for(let n=t.length-1;n>=0&&!fr(e)&&hr(e,t[n]);n--)r=0===n;return r||(e.pos=n),r}function Nr(e){return e>64&&e<91||e>96&&e<123||e>47&&e<58||Pr.includes(e)}function Lr(e,t){return e===br.RoundL||"markup"===t&&(e===br.SquareL||e===br.CurlyL)}function zr(e,t){return e===br.RoundR||"markup"===t&&(e===br.SquareR||e===br.CurlyR)}function Dr(e,t){const n=cr(t);return"stylesheet"===n.type?Br(e,n):Fr(e,n)}function Fr(e,t){return wn(xn(e,t),t)}function Br(e,t){return Dn(Wn(e,t),t)}}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r].call(i.exports,i,i.exports,n),i.exports}n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r=n(4359),o=exports;for(var i in r)o[i]=r[i];r.__esModule&&Object.defineProperty(o,"__esModule",{value:!0})})();
//# sourceMappingURL=http://go/sourcemap/sourcemaps/3af362bc7c6ffdde67ee75328bc9be679d6f3a40/extensions/emmet/dist\node/emmetNodeMain.js.map