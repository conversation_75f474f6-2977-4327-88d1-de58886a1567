"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.enumToMap = void 0;
function enumToMap(obj) {
    const res = {};
    Object.keys(obj).forEach((key) => {
        const value = obj[key];
        if (typeof value === 'number') {
            res[key] = value;
        }
    });
    return res;
}
exports.enumToMap = enumToMap;//# sourceMappingURL=http://go/sourcemap/sourcemaps/3af362bc7c6ffdde67ee75328bc9be679d6f3a40/node_modules/@connectrpc/connect-node/node_modules/undici/lib/llhttp/utils.js.map