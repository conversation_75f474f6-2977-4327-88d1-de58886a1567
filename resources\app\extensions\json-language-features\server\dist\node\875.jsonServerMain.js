exports.id=875,exports.ids=[875],exports.modules={9323:(e,t,n)=>{(()=>{var e={747:(e,t,n)=>{"use strict";var r,o=Object.defineProperty,i=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,c={};((e,t)=>{for(var n in t)o(e,n,{get:t[n],enumerable:!0})})(c,{config:()=>f,t:()=>h}),e.exports=(r=c,((e,t,n,r)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of s(t))a.call(e,n)||void 0===n||o(e,n,{get:()=>t[n],enumerable:!(r=i(t,n))||r.enumerable});return e})(o({},"__esModule",{value:!0}),r));var u,l=n(896),d=n(943);function f(e){if("contents"in e)u="string"==typeof e.contents?JSON.parse(e.contents):e.contents;else if("fsPath"in e){const n=(t=e.fsPath,(0,l.readFileSync)(t,"utf8")),r=JSON.parse(n);u=g(r)?r.contents.bundle:r}else{var t;if(e.uri){let t=e.uri;return"string"==typeof e.uri&&(t=new URL(e.uri)),new Promise(((e,n)=>{(async function(e){if("file:"===e.protocol)return await(0,d.readFile)(e,"utf8");if("http:"===e.protocol||"https:"===e.protocol){const t=await fetch(e.toString(),{headers:{"Accept-Encoding":"gzip, deflate",Accept:"application/json"},redirect:"follow"});if(!t.ok){let n=`Unexpected ${t.status} response while trying to read ${e}`;try{n+=`: ${await t.text()}`}catch{}throw new Error(n)}return await t.text()}throw new Error("Unsupported protocol")})(t).then((t=>{try{const n=JSON.parse(t);u=g(n)?n.contents.bundle:n,e()}catch(e){n(e)}})).catch((e=>{n(e)}))}))}}}function h(...e){const t=e[0];let n,r,o;if("string"==typeof t)n=t,r=t,e.splice(0,1),o=e&&"object"==typeof e[0]?e[0]:e;else{if(t instanceof Array){const n=e.slice(1);if(t.length!==n.length+1)throw new Error("expected a string as the first argument to l10n.t");let r=t[0];for(let e=1;e<t.length;e++)r+=`{${e-1}}`+t[e];return h(r,...n)}r=t.message,n=r,t.comment&&t.comment.length>0&&(n+=`/${Array.isArray(t.comment)?t.comment.join(""):t.comment}`),o=t.args??{}}const i=u?.[n];return i?"string"==typeof i?m(i,o):i.comment?m(i.message,o):m(r,o):m(r,o)}var p=/{([^}]+)}/g;function m(e,t){return 0===Object.keys(t).length?e:e.replace(p,((e,n)=>t[n]??e))}function g(e){return!("object"!=typeof e?.contents?.bundle||"string"!=typeof e?.version)}},544:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.req=t.json=t.toBuffer=void 0;const s=i(n(611)),a=i(n(692));async function c(e){let t=0;const n=[];for await(const r of e)t+=r.length,n.push(r);return Buffer.concat(n,t)}t.toBuffer=c,t.json=async function(e){const t=(await c(e)).toString("utf8");try{return JSON.parse(t)}catch(e){const n=e;throw n.message+=` (input: ${t})`,n}},t.req=function(e,t={}){const n=(("string"==typeof e?e:e.href).startsWith("https:")?a:s).request(e,t),r=new Promise(((e,t)=>{n.once("response",e).once("error",t).end()}));return n.then=r.then.bind(r),n}},917:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t},s=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.Agent=void 0;const a=i(n(278)),c=i(n(611)),u=n(692);s(n(544),t);const l=Symbol("AgentBaseInternalState");class d extends c.Agent{constructor(e){super(e),this[l]={}}isSecureEndpoint(e){if(e){if("boolean"==typeof e.secureEndpoint)return e.secureEndpoint;if("string"==typeof e.protocol)return"https:"===e.protocol}const{stack:t}=new Error;return"string"==typeof t&&t.split("\n").some((e=>-1!==e.indexOf("(https.js:")||-1!==e.indexOf("node:https:")))}incrementSockets(e){if(this.maxSockets===1/0&&this.maxTotalSockets===1/0)return null;this.sockets[e]||(this.sockets[e]=[]);const t=new a.Socket({writable:!1});return this.sockets[e].push(t),this.totalSocketCount++,t}decrementSockets(e,t){if(!this.sockets[e]||null===t)return;const n=this.sockets[e],r=n.indexOf(t);-1!==r&&(n.splice(r,1),this.totalSocketCount--,0===n.length&&delete this.sockets[e])}getName(e){return("boolean"==typeof e.secureEndpoint?e.secureEndpoint:this.isSecureEndpoint(e))?u.Agent.prototype.getName.call(this,e):super.getName(e)}createSocket(e,t,n){const r={...t,secureEndpoint:this.isSecureEndpoint(t)},o=this.getName(r),i=this.incrementSockets(o);Promise.resolve().then((()=>this.connect(e,r))).then((s=>{if(this.decrementSockets(o,i),s instanceof c.Agent)return s.addRequest(e,r);this[l].currentSocket=s,super.createSocket(e,t,n)}),(e=>{this.decrementSockets(o,i),n(e)}))}createConnection(){const e=this[l].currentSocket;if(this[l].currentSocket=void 0,!e)throw new Error("No socket was returned in the `connect()` function");return e}get defaultPort(){return this[l].defaultPort??("https:"===this.protocol?443:80)}set defaultPort(e){this[l]&&(this[l].defaultPort=e)}get protocol(){return this[l].protocol??(this.isSecureEndpoint()?"https:":"http:")}set protocol(e){this[l]&&(this[l].protocol=e)}}t.Agent=d},645:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.HttpProxyAgent=void 0;const a=i(n(278)),c=i(n(756)),u=s(n(150)),l=n(434),d=n(917),f=n(16),h=(0,u.default)("http-proxy-agent");class p extends d.Agent{constructor(e,t){super(t),this.proxy="string"==typeof e?new f.URL(e):e,this.proxyHeaders=t?.headers??{},h("Creating new HttpProxyAgent instance: %o",this.proxy.href);const n=(this.proxy.hostname||this.proxy.host).replace(/^\[|\]$/g,""),r=this.proxy.port?parseInt(this.proxy.port,10):"https:"===this.proxy.protocol?443:80;this.connectOpts={...t?m(t,"headers"):null,host:n,port:r}}addRequest(e,t){e._header=null,this.setRequestProps(e,t),super.addRequest(e,t)}setRequestProps(e,t){const{proxy:n}=this,r=`${t.secureEndpoint?"https:":"http:"}//${e.getHeader("host")||"localhost"}`,o=new f.URL(e.path,r);80!==t.port&&(o.port=String(t.port)),e.path=String(o);const i="function"==typeof this.proxyHeaders?this.proxyHeaders():{...this.proxyHeaders};if(n.username||n.password){const e=`${decodeURIComponent(n.username)}:${decodeURIComponent(n.password)}`;i["Proxy-Authorization"]=`Basic ${Buffer.from(e).toString("base64")}`}i["Proxy-Connection"]||(i["Proxy-Connection"]=this.keepAlive?"Keep-Alive":"close");for(const t of Object.keys(i)){const n=i[t];n&&e.setHeader(t,n)}}async connect(e,t){let n,r,o;return e._header=null,e.path.includes("://")||this.setRequestProps(e,t),h("Regenerating stored HTTP header string for request"),e._implicitHeader(),e.outputData&&e.outputData.length>0&&(h("Patching connection write() output buffer with updated header"),n=e.outputData[0].data,r=n.indexOf("\r\n\r\n")+4,e.outputData[0].data=e._header+n.substring(r),h("Output buffer: %o",e.outputData[0].data)),"https:"===this.proxy.protocol?(h("Creating `tls.Socket`: %o",this.connectOpts),o=c.connect(this.connectOpts)):(h("Creating `net.Socket`: %o",this.connectOpts),o=a.connect(this.connectOpts)),await(0,l.once)(o,"connect"),o}}function m(e,...t){const n={};let r;for(r in e)t.includes(r)||(n[r]=e[r]);return n}p.protocols=["http","https"],t.HttpProxyAgent=p},288:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.HttpsProxyAgent=void 0;const a=i(n(278)),c=i(n(756)),u=s(n(613)),l=s(n(150)),d=n(917),f=n(16),h=n(868),p=(0,l.default)("https-proxy-agent");class m extends d.Agent{constructor(e,t){super(t),this.options={path:void 0},this.proxy="string"==typeof e?new f.URL(e):e,this.proxyHeaders=t?.headers??{},p("Creating new HttpsProxyAgent instance: %o",this.proxy.href);const n=(this.proxy.hostname||this.proxy.host).replace(/^\[|\]$/g,""),r=this.proxy.port?parseInt(this.proxy.port,10):"https:"===this.proxy.protocol?443:80;this.connectOpts={ALPNProtocols:["http/1.1"],...t?y(t,"headers"):null,host:n,port:r}}async connect(e,t){const{proxy:n}=this;if(!t.host)throw new TypeError('No "host" provided');let r;if("https:"===n.protocol){p("Creating `tls.Socket`: %o",this.connectOpts);const e=this.connectOpts.servername||this.connectOpts.host;r=c.connect({...this.connectOpts,servername:e})}else p("Creating `net.Socket`: %o",this.connectOpts),r=a.connect(this.connectOpts);const o="function"==typeof this.proxyHeaders?this.proxyHeaders():{...this.proxyHeaders},i=a.isIPv6(t.host)?`[${t.host}]`:t.host;let s=`CONNECT ${i}:${t.port} HTTP/1.1\r\n`;if(n.username||n.password){const e=`${decodeURIComponent(n.username)}:${decodeURIComponent(n.password)}`;o["Proxy-Authorization"]=`Basic ${Buffer.from(e).toString("base64")}`}o.Host=`${i}:${t.port}`,o["Proxy-Connection"]||(o["Proxy-Connection"]=this.keepAlive?"Keep-Alive":"close");for(const e of Object.keys(o))s+=`${e}: ${o[e]}\r\n`;const l=(0,h.parseProxyResponse)(r);r.write(`${s}\r\n`);const{connect:d,buffered:f}=await l;if(e.emit("proxyConnect",d),this.emit("proxyConnect",d,e),200===d.statusCode){if(e.once("socket",g),t.secureEndpoint){p("Upgrading socket connection to TLS");const e=t.servername||t.host;return c.connect({...y(t,"host","path","port"),socket:r,servername:e})}return r}r.destroy();const m=new a.Socket({writable:!1});return m.readable=!0,e.once("socket",(e=>{p("Replaying proxy buffer for failed request"),(0,u.default)(e.listenerCount("data")>0),e.push(f),e.push(null)})),m}}function g(e){e.resume()}function y(e,...t){const n={};let r;for(r in e)t.includes(r)||(n[r]=e[r]);return n}m.protocols=["http","https"],t.HttpsProxyAgent=m},868:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.parseProxyResponse=void 0;const o=(0,r(n(150)).default)("https-proxy-agent:parse-proxy-response");t.parseProxyResponse=function(e){return new Promise(((t,n)=>{let r=0;const i=[];function s(){const c=e.read();c?function(c){i.push(c),r+=c.length;const u=Buffer.concat(i,r),l=u.indexOf("\r\n\r\n");if(-1===l)return o("have not received end of HTTP headers yet..."),void s();const d=u.slice(0,l).toString("ascii").split("\r\n"),f=d.shift();if(!f)return e.destroy(),n(new Error("No header received from proxy CONNECT response"));const h=f.split(" "),p=+h[1],m=h.slice(2).join(" "),g={};for(const t of d){if(!t)continue;const r=t.indexOf(":");if(-1===r)return e.destroy(),n(new Error(`Invalid header from proxy CONNECT response: "${t}"`));const o=t.slice(0,r).toLowerCase(),i=t.slice(r+1).trimStart(),s=g[o];"string"==typeof s?g[o]=[s,i]:Array.isArray(s)?s.push(i):g[o]=i}o("got proxy server response: %o %o",f,g),a(),t({connect:{statusCode:p,statusText:m,headers:g},buffered:u})}(c):e.once("readable",s)}function a(){e.removeListener("end",c),e.removeListener("error",u),e.removeListener("readable",s)}function c(){a(),o("onend"),n(new Error("Proxy connection ended before receiving CONNECT response"))}function u(e){a(),o("onerror %o",e),n(e)}e.on("error",u),e.on("end",c),s()}))}},580:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.xhr=t.configure=void 0,t.getErrorStatusDescription=function(e){if(!(e<400))switch(e){case 400:return u.t("Bad request. The request cannot be fulfilled due to bad syntax.");case 401:return u.t("Unauthorized. The server is refusing to respond.");case 403:return u.t("Forbidden. The server is refusing to respond.");case 404:return u.t("Not Found. The requested location could not be found.");case 405:return u.t("Method not allowed. A request was made using a request method not supported by that location.");case 406:return u.t("Not Acceptable. The server can only generate a response that is not accepted by the client.");case 407:return u.t("Proxy Authentication Required. The client must first authenticate itself with the proxy.");case 408:return u.t("Request Timeout. The server timed out waiting for the request.");case 409:return u.t("Conflict. The request could not be completed because of a conflict in the request.");case 410:return u.t("Gone. The requested page is no longer available.");case 411:return u.t('Length Required. The "Content-Length" is not defined.');case 412:return u.t("Precondition Failed. The precondition given in the request evaluated to false by the server.");case 413:return u.t("Request Entity Too Large. The server will not accept the request, because the request entity is too large.");case 414:return u.t("Request-URI Too Long. The server will not accept the request, because the URL is too long.");case 415:return u.t("Unsupported Media Type. The server will not accept the request, because the media type is not supported.");case 500:return u.t("Internal Server Error.");case 501:return u.t("Not Implemented. The server either does not recognize the request method, or it lacks the ability to fulfill the request.");case 502:return u.t("Bad Gateway. The upstream server did not respond.");case 503:return u.t("Service Unavailable. The server is currently unavailable (overloaded or down).");default:return u.t("HTTP status code {0}",e)}};var s=n(611),a=n(692),c=n(16),u=n(747),l=n(106),d=n(645),f=n(288),h=void 0,p=!0;function m(e){var t;return new Promise((function(n,r){var o=(0,c.parse)(e.url),i={hostname:o.hostname,agent:!!e.agent&&e.agent,port:o.port?parseInt(o.port):"https:"===o.protocol?443:80,path:o.path,method:e.type||"GET",headers:e.headers,rejectUnauthorized:"boolean"!=typeof e.strictSSL||e.strictSSL};e.user&&e.password&&(i.auth=e.user+":"+e.password);var u=function(r){if(r.statusCode>=300&&r.statusCode<400&&e.followRedirects&&e.followRedirects>0&&r.headers.location){var i=r.headers.location;i.startsWith("/")&&(i=(0,c.format)({protocol:o.protocol,hostname:o.hostname,port:o.port,pathname:i})),n(m(function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return t.forEach((function(t){return Object.keys(t).forEach((function(n){return e[n]=t[n]}))})),e}({},e,{url:i,followRedirects:e.followRedirects-1})))}else n({req:t,res:r})};(t="https:"===o.protocol?a.request(i,u):s.request(i,u)).on("error",r),e.timeout&&t.setTimeout(e.timeout),e.data&&t.write(e.data),t.end(),e.token&&(e.token.isCancellationRequested&&t.destroy(new g),e.token.onCancellationRequested((function(){t.destroy(new g)})))}))}t.configure=function(e,t){h=e,p=t},t.xhr=function(e){return"boolean"!=typeof(e=i({},e)).strictSSL&&(e.strictSSL=p),e.agent||(e.agent=function(e,t){var n;void 0===t&&(t={});var r=(0,c.parse)(e),o=t.proxyUrl||function(e){return"http:"===e.protocol?process.env.HTTP_PROXY||process.env.http_proxy||null:"https:"===e.protocol&&(process.env.HTTPS_PROXY||process.env.https_proxy||process.env.HTTP_PROXY||process.env.http_proxy)||null}(r);return o&&/^https?:/.test(o)?"http:"===r.protocol?new d.HttpProxyAgent(o):new f.HttpsProxyAgent(o,{rejectUnauthorized:null===(n=t.strictSSL)||void 0===n||n}):null}(e.url,{proxyUrl:h,strictSSL:p})),"number"!=typeof e.followRedirects&&(e.followRedirects=5),m(e).then((function(n){return new Promise((function(r,o){var i,s,a=n.res,d=a,f=!1,h=a.headers&&a.headers["content-encoding"];if(h&&(i=e.type,s=n.res.statusCode,!("HEAD"===i||s>=100&&s<200||204===s||304===s))){var p={flush:l.constants.Z_SYNC_FLUSH,finishFlush:l.constants.Z_SYNC_FLUSH};if("gzip"===h){var m=l.createGunzip(p);a.pipe(m),d=m}else if("deflate"===h){var y=l.createInflate(p);a.pipe(y),d=y}}var b=[];d.on("data",(function(e){return b.push(e)})),d.on("end",(function(){if(!f){if(f=!0,e.followRedirects>0&&(a.statusCode>=300&&a.statusCode<=303||307===a.statusCode)){var n=a.headers.location;if(n.startsWith("/")){var i=(0,c.parse)(e.url);n=(0,c.format)({protocol:i.protocol,hostname:i.hostname,port:i.port,pathname:n})}if(n){var s={type:e.type,url:n,user:e.user,password:e.password,headers:e.headers,timeout:e.timeout,followRedirects:e.followRedirects-1,data:e.data,token:e.token};return void(0,t.xhr)(s).then(r,o)}}var u=Buffer.concat(b),l={responseText:u.toString(),body:u,status:a.statusCode,headers:a.headers||{}};a.statusCode>=200&&a.statusCode<300||1223===a.statusCode?r(l):o(l)}})),d.on("error",(function(t){var n;n=g.is(t)?t:{responseText:u.t("Unable to access {0}. Error: {1}",e.url,t.message),body:Buffer.concat(b),status:500,headers:{}},f=!0,o(n)})),e.token&&(e.token.isCancellationRequested&&d.destroy(new g),e.token.onCancellationRequested((function(){d.destroy(new g)})))}))}),(function(t){var n;return n=g.is(t)?t:{responseText:e.agent?u.t("Unable to connect to {0} through a proxy. Error: {1}",e.url,t.message):u.t("Unable to connect to {0}. Error: {1}",e.url,t.message),body:Buffer.concat([]),status:404,headers:{}},Promise.reject(n)}))};var g=function(e){function t(){var n=e.call(this,"The user aborted a request")||this;return n.name="AbortError",Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t.is=function(e){return e instanceof t},t}(Error)},150:(e,t)=>{function n(){}Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return n}},613:e=>{"use strict";e.exports=n(2613)},434:e=>{"use strict";e.exports=n(4434)},896:e=>{"use strict";e.exports=n(9896)},943:e=>{"use strict";e.exports=n(1943)},611:e=>{"use strict";e.exports=n(8611)},692:e=>{"use strict";e.exports=n(5692)},278:e=>{"use strict";e.exports=n(9278)},756:e=>{"use strict";e.exports=n(4756)},16:e=>{"use strict";e.exports=n(7016)},106:e=>{"use strict";e.exports=n(3106)}},r={},o=function t(n){var o=r[n];if(void 0!==o)return o.exports;var i=r[n]={exports:{}};return e[n].call(i.exports,i,i.exports,t),i.exports}(580),i=t;for(var s in o)i[s]=o[s];o.__esModule&&Object.defineProperty(i,"__esModule",{value:!0})})()},7547:(e,t,n)=>{"use strict";function r(e,t=!1){const n=e.length;let r=0,a="",c=0,u=16,l=0,d=0,f=0,h=0,p=0;function m(t,n){let o=0,i=0;for(;o<t||!n;){let t=e.charCodeAt(r);if(t>=48&&t<=57)i=16*i+t-48;else if(t>=65&&t<=70)i=16*i+t-65+10;else{if(!(t>=97&&t<=102))break;i=16*i+t-97+10}r++,o++}return o<t&&(i=-1),i}function g(){if(a="",p=0,c=r,d=l,h=f,r>=n)return c=n,u=17;let t=e.charCodeAt(r);if(o(t)){do{r++,a+=String.fromCharCode(t),t=e.charCodeAt(r)}while(o(t));return u=15}if(i(t))return r++,a+=String.fromCharCode(t),13===t&&10===e.charCodeAt(r)&&(r++,a+="\n"),l++,f=r,u=14;switch(t){case 123:return r++,u=1;case 125:return r++,u=2;case 91:return r++,u=3;case 93:return r++,u=4;case 58:return r++,u=6;case 44:return r++,u=5;case 34:return r++,a=function(){let t="",o=r;for(;;){if(r>=n){t+=e.substring(o,r),p=2;break}const s=e.charCodeAt(r);if(34===s){t+=e.substring(o,r),r++;break}if(92!==s){if(s>=0&&s<=31){if(i(s)){t+=e.substring(o,r),p=2;break}p=6}r++}else{if(t+=e.substring(o,r),r++,r>=n){p=2;break}switch(e.charCodeAt(r++)){case 34:t+='"';break;case 92:t+="\\";break;case 47:t+="/";break;case 98:t+="\b";break;case 102:t+="\f";break;case 110:t+="\n";break;case 114:t+="\r";break;case 116:t+="\t";break;case 117:const e=m(4,!0);e>=0?t+=String.fromCharCode(e):p=4;break;default:p=5}o=r}}return t}(),u=10;case 47:const o=r-1;if(47===e.charCodeAt(r+1)){for(r+=2;r<n&&!i(e.charCodeAt(r));)r++;return a=e.substring(o,r),u=12}if(42===e.charCodeAt(r+1)){r+=2;const t=n-1;let s=!1;for(;r<t;){const t=e.charCodeAt(r);if(42===t&&47===e.charCodeAt(r+1)){r+=2,s=!0;break}r++,i(t)&&(13===t&&10===e.charCodeAt(r)&&r++,l++,f=r)}return s||(r++,p=1),a=e.substring(o,r),u=13}return a+=String.fromCharCode(t),r++,u=16;case 45:if(a+=String.fromCharCode(t),r++,r===n||!s(e.charCodeAt(r)))return u=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return a+=function(){let t=r;if(48===e.charCodeAt(r))r++;else for(r++;r<e.length&&s(e.charCodeAt(r));)r++;if(r<e.length&&46===e.charCodeAt(r)){if(r++,!(r<e.length&&s(e.charCodeAt(r))))return p=3,e.substring(t,r);for(r++;r<e.length&&s(e.charCodeAt(r));)r++}let n=r;if(r<e.length&&(69===e.charCodeAt(r)||101===e.charCodeAt(r)))if(r++,(r<e.length&&43===e.charCodeAt(r)||45===e.charCodeAt(r))&&r++,r<e.length&&s(e.charCodeAt(r))){for(r++;r<e.length&&s(e.charCodeAt(r));)r++;n=r}else p=3;return e.substring(t,n)}(),u=11;default:for(;r<n&&y(t);)r++,t=e.charCodeAt(r);if(c!==r){switch(a=e.substring(c,r),a){case"true":return u=8;case"false":return u=9;case"null":return u=7}return u=16}return a+=String.fromCharCode(t),r++,u=16}}function y(e){if(o(e)||i(e))return!1;switch(e){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return!1}return!0}return{setPosition:function(e){r=e,a="",c=0,u=16,p=0},getPosition:()=>r,scan:t?function(){let e;do{e=g()}while(e>=12&&e<=15);return e}:g,getToken:()=>u,getTokenValue:()=>a,getTokenOffset:()=>c,getTokenLength:()=>r-c,getTokenStartLine:()=>d,getTokenStartCharacter:()=>c-h,getTokenError:()=>p}}function o(e){return 32===e||9===e}function i(e){return 10===e||13===e}function s(e){return e>=48&&e<=57}var a;n.r(t),n.d(t,{ClientCapabilities:()=>Ye,CodeAction:()=>Re,CodeActionContext:()=>Ce,CodeActionKind:()=>Te,Color:()=>M,ColorInformation:()=>N,ColorPresentation:()=>E,Command:()=>W,CompletionItem:()=>ue,CompletionItemKind:()=>re,CompletionItemTag:()=>ie,CompletionList:()=>le,Diagnostic:()=>$,DiagnosticSeverity:()=>I,DocumentHighlight:()=>ge,DocumentHighlightKind:()=>me,DocumentLink:()=>xe,DocumentSymbol:()=>_e,DocumentUri:()=>S,ErrorCode:()=>Xe,FoldingRange:()=>A,FoldingRangeKind:()=>q,Hover:()=>fe,InsertTextFormat:()=>oe,Location:()=>P,MarkedString:()=>de,MarkupContent:()=>ne,MarkupKind:()=>te,Position:()=>w,Range:()=>x,SchemaDraft:()=>Ze,SelectionRange:()=>Pe,SymbolInformation:()=>ve,SymbolKind:()=>ye,TextDocument:()=>Ge,TextDocumentEdit:()=>z,TextEdit:()=>V,VersionedTextDocumentIdentifier:()=>Y,WorkspaceEdit:()=>X,getLanguageService:()=>En}),function(e){e[e.lineFeed=10]="lineFeed",e[e.carriageReturn=13]="carriageReturn",e[e.space=32]="space",e[e._0=48]="_0",e[e._1=49]="_1",e[e._2=50]="_2",e[e._3=51]="_3",e[e._4=52]="_4",e[e._5=53]="_5",e[e._6=54]="_6",e[e._7=55]="_7",e[e._8=56]="_8",e[e._9=57]="_9",e[e.a=97]="a",e[e.b=98]="b",e[e.c=99]="c",e[e.d=100]="d",e[e.e=101]="e",e[e.f=102]="f",e[e.g=103]="g",e[e.h=104]="h",e[e.i=105]="i",e[e.j=106]="j",e[e.k=107]="k",e[e.l=108]="l",e[e.m=109]="m",e[e.n=110]="n",e[e.o=111]="o",e[e.p=112]="p",e[e.q=113]="q",e[e.r=114]="r",e[e.s=115]="s",e[e.t=116]="t",e[e.u=117]="u",e[e.v=118]="v",e[e.w=119]="w",e[e.x=120]="x",e[e.y=121]="y",e[e.z=122]="z",e[e.A=65]="A",e[e.B=66]="B",e[e.C=67]="C",e[e.D=68]="D",e[e.E=69]="E",e[e.F=70]="F",e[e.G=71]="G",e[e.H=72]="H",e[e.I=73]="I",e[e.J=74]="J",e[e.K=75]="K",e[e.L=76]="L",e[e.M=77]="M",e[e.N=78]="N",e[e.O=79]="O",e[e.P=80]="P",e[e.Q=81]="Q",e[e.R=82]="R",e[e.S=83]="S",e[e.T=84]="T",e[e.U=85]="U",e[e.V=86]="V",e[e.W=87]="W",e[e.X=88]="X",e[e.Y=89]="Y",e[e.Z=90]="Z",e[e.asterisk=42]="asterisk",e[e.backslash=92]="backslash",e[e.closeBrace=125]="closeBrace",e[e.closeBracket=93]="closeBracket",e[e.colon=58]="colon",e[e.comma=44]="comma",e[e.dot=46]="dot",e[e.doubleQuote=34]="doubleQuote",e[e.minus=45]="minus",e[e.openBrace=123]="openBrace",e[e.openBracket=91]="openBracket",e[e.plus=43]="plus",e[e.slash=47]="slash",e[e.formFeed=12]="formFeed",e[e.tab=9]="tab"}(a||(a={}));const c=new Array(20).fill(0).map(((e,t)=>" ".repeat(t))),u=200,l={" ":{"\n":new Array(u).fill(0).map(((e,t)=>"\n"+" ".repeat(t))),"\r":new Array(u).fill(0).map(((e,t)=>"\r"+" ".repeat(t))),"\r\n":new Array(u).fill(0).map(((e,t)=>"\r\n"+" ".repeat(t)))},"\t":{"\n":new Array(u).fill(0).map(((e,t)=>"\n"+"\t".repeat(t))),"\r":new Array(u).fill(0).map(((e,t)=>"\r"+"\t".repeat(t))),"\r\n":new Array(u).fill(0).map(((e,t)=>"\r\n"+"\t".repeat(t)))}},d=["\n","\r","\r\n"];function f(e,t){let n="";for(let r=0;r<t;r++)n+=e;return n}function h(e,t){return-1!=="\r\n".indexOf(e.charAt(t))}var p;!function(e){e.DEFAULT={allowTrailingComma:!1}}(p||(p={}));const m=r;var g,y;!function(e){e[e.None=0]="None",e[e.UnexpectedEndOfComment=1]="UnexpectedEndOfComment",e[e.UnexpectedEndOfString=2]="UnexpectedEndOfString",e[e.UnexpectedEndOfNumber=3]="UnexpectedEndOfNumber",e[e.InvalidUnicode=4]="InvalidUnicode",e[e.InvalidEscapeCharacter=5]="InvalidEscapeCharacter",e[e.InvalidCharacter=6]="InvalidCharacter"}(g||(g={})),function(e){e[e.OpenBraceToken=1]="OpenBraceToken",e[e.CloseBraceToken=2]="CloseBraceToken",e[e.OpenBracketToken=3]="OpenBracketToken",e[e.CloseBracketToken=4]="CloseBracketToken",e[e.CommaToken=5]="CommaToken",e[e.ColonToken=6]="ColonToken",e[e.NullKeyword=7]="NullKeyword",e[e.TrueKeyword=8]="TrueKeyword",e[e.FalseKeyword=9]="FalseKeyword",e[e.StringLiteral=10]="StringLiteral",e[e.NumericLiteral=11]="NumericLiteral",e[e.LineCommentTrivia=12]="LineCommentTrivia",e[e.BlockCommentTrivia=13]="BlockCommentTrivia",e[e.LineBreakTrivia=14]="LineBreakTrivia",e[e.Trivia=15]="Trivia",e[e.Unknown=16]="Unknown",e[e.EOF=17]="EOF"}(y||(y={}));const b=function(e,t=[],n=p.DEFAULT){let o=null,i=[];const s=[];function a(e){Array.isArray(i)?i.push(e):null!==o&&(i[o]=e)}return function(e,t,n=p.DEFAULT){const o=r(e,!1),i=[];let s=0;function a(e){return e?t=>0===s&&e(t,o.getTokenOffset(),o.getTokenLength(),o.getTokenStartLine(),o.getTokenStartCharacter()):()=>!0}function c(e){return e?t=>0===s&&e(t,o.getTokenOffset(),o.getTokenLength(),o.getTokenStartLine(),o.getTokenStartCharacter(),(()=>i.slice())):()=>!0}function u(e){return e?()=>{s>0?s++:!1===e(o.getTokenOffset(),o.getTokenLength(),o.getTokenStartLine(),o.getTokenStartCharacter(),(()=>i.slice()))&&(s=1)}:()=>!0}function l(e){return e?()=>{s>0&&s--,0===s&&e(o.getTokenOffset(),o.getTokenLength(),o.getTokenStartLine(),o.getTokenStartCharacter())}:()=>!0}const d=u(t.onObjectBegin),f=c(t.onObjectProperty),h=l(t.onObjectEnd),m=u(t.onArrayBegin),g=l(t.onArrayEnd),y=c(t.onLiteralValue),b=a(t.onSeparator),v=(_=t.onComment)?()=>0===s&&_(o.getTokenOffset(),o.getTokenLength(),o.getTokenStartLine(),o.getTokenStartCharacter()):()=>!0,k=a(t.onError);var _;const T=n&&n.disallowComments,S=n&&n.allowTrailingComma;function C(){for(;;){const e=o.scan();switch(o.getTokenError()){case 4:R(14);break;case 5:R(15);break;case 3:R(13);break;case 1:T||R(11);break;case 2:R(12);break;case 6:R(16)}switch(e){case 12:case 13:T?R(10):v();break;case 16:R(1);break;case 15:case 14:break;default:return e}}}function R(e,t=[],n=[]){if(k(e),t.length+n.length>0){let e=o.getToken();for(;17!==e;){if(-1!==t.indexOf(e)){C();break}if(-1!==n.indexOf(e))break;e=C()}}}function D(e){const t=o.getTokenValue();return e?y(t):(f(t),i.push(t)),C(),!0}C(),17===o.getToken()?!!n.allowEmptyContent||R(4,[],[]):function e(){switch(o.getToken()){case 3:return function(){m(),C();let t=!0,n=!1;for(;4!==o.getToken()&&17!==o.getToken();){if(5===o.getToken()){if(n||R(4,[],[]),b(","),C(),4===o.getToken()&&S)break}else n&&R(6,[],[]);t?(i.push(0),t=!1):i[i.length-1]++,e()||R(4,[],[4,5]),n=!0}return g(),t||i.pop(),4!==o.getToken()?R(8,[4],[]):C(),!0}();case 1:return function(){d(),C();let t=!1;for(;2!==o.getToken()&&17!==o.getToken();){if(5===o.getToken()){if(t||R(4,[],[]),b(","),C(),2===o.getToken()&&S)break}else t&&R(6,[],[]);(10!==o.getToken()?(R(3,[],[2,5]),0):(D(!1),6===o.getToken()?(b(":"),C(),e()||R(4,[],[2,5])):R(5,[],[2,5]),i.pop(),1))||R(4,[],[2,5]),t=!0}return h(),2!==o.getToken()?R(7,[2],[]):C(),!0}();case 10:return D(!0);default:return function(){switch(o.getToken()){case 11:const e=o.getTokenValue();let t=Number(e);isNaN(t)&&(R(2),t=0),y(t);break;case 7:y(null);break;case 8:y(!0);break;case 9:y(!1);break;default:return!1}return C(),!0}()}}()?17!==o.getToken()&&R(9,[],[]):R(4,[],[])}(e,{onObjectBegin:()=>{const e={};a(e),s.push(i),i=e,o=null},onObjectProperty:e=>{o=e},onObjectEnd:()=>{i=s.pop()},onArrayBegin:()=>{const e=[];a(e),s.push(i),i=e,o=null},onArrayEnd:()=>{i=s.pop()},onLiteralValue:a,onError:(e,n,r)=>{t.push({error:e,offset:n,length:r})}},n),i[0]},v=function e(t,n,r=!1){if(function(e,t,n=!1){return t>=e.offset&&t<e.offset+e.length||n&&t===e.offset+e.length}(t,n,r)){const o=t.children;if(Array.isArray(o))for(let t=0;t<o.length&&o[t].offset<=n;t++){const i=e(o[t],n,r);if(i)return i}return t}},k=function e(t){if(!t.parent||!t.parent.children)return[];const n=e(t.parent);if("property"===t.parent.type){const e=t.parent.children[0].value;n.push(e)}else if("array"===t.parent.type){const e=t.parent.children.indexOf(t);-1!==e&&n.push(e)}return n},_=function e(t){switch(t.type){case"array":return t.children.map(e);case"object":const n=Object.create(null);for(let r of t.children){const t=r.children[1];t&&(n[r.children[0].value]=e(t))}return n;case"null":case"string":case"number":case"boolean":return t.value;default:return}};var T,S,C,R,D,w,x,P,O,M,N,E,q,A,j,I,L,F,$,W,V,U,H,B,z,K,J,G,X,Z,Y,Q,ee,te,ne,re,oe,ie,se,ae,ce,ue,le,de,fe,he,pe,me,ge,ye,be,ve,ke,_e,Te,Se,Ce,Re,De,we,xe,Pe,Oe,Me,Ne,Ee,qe,Ae,je,Ie,Le,Fe,$e,We,Ve,Ue,He,Be,ze,Ke,Je,Ge,Xe,Ze,Ye;function Qe(e,t){if(e===t)return!0;if(null==e||null==t)return!1;if(typeof e!=typeof t)return!1;if("object"!=typeof e)return!1;if(Array.isArray(e)!==Array.isArray(t))return!1;let n,r;if(Array.isArray(e)){if(e.length!==t.length)return!1;for(n=0;n<e.length;n++)if(!Qe(e[n],t[n]))return!1}else{const o=[];for(r in e)o.push(r);o.sort();const i=[];for(r in t)i.push(r);if(i.sort(),!Qe(o,i))return!1;for(n=0;n<o.length;n++)if(!Qe(e[o[n]],t[o[n]]))return!1}return!0}function et(e){return"number"==typeof e}function tt(e){return void 0!==e}function nt(e){return"boolean"==typeof e}function rt(e){return"string"==typeof e}function ot(e){return"object"==typeof e&&null!==e&&!Array.isArray(e)}function it(e,t){const n=e.length-t.length;return n>0?e.lastIndexOf(t)===n:0===n&&e===t}function st(e){let t="";(function(e,t){if(e.length<4)return!1;for(let n=0;n<4;n++)if(e[n]!==t[n])return!1;return!0})(e,"(?i)")&&(e=e.substring(4),t="i");try{return new RegExp(e,t+"u")}catch(n){try{return new RegExp(e,t)}catch(e){return}}}function at(e){let t=0;for(let n=0;n<e.length;n++){t++;const r=e.charCodeAt(n);55296<=r&&r<=56319&&n++}return t}!function(e){e[e.InvalidSymbol=1]="InvalidSymbol",e[e.InvalidNumberFormat=2]="InvalidNumberFormat",e[e.PropertyNameExpected=3]="PropertyNameExpected",e[e.ValueExpected=4]="ValueExpected",e[e.ColonExpected=5]="ColonExpected",e[e.CommaExpected=6]="CommaExpected",e[e.CloseBraceExpected=7]="CloseBraceExpected",e[e.CloseBracketExpected=8]="CloseBracketExpected",e[e.EndOfFileExpected=9]="EndOfFileExpected",e[e.InvalidCommentToken=10]="InvalidCommentToken",e[e.UnexpectedEndOfComment=11]="UnexpectedEndOfComment",e[e.UnexpectedEndOfString=12]="UnexpectedEndOfString",e[e.UnexpectedEndOfNumber=13]="UnexpectedEndOfNumber",e[e.InvalidUnicode=14]="InvalidUnicode",e[e.InvalidEscapeCharacter=15]="InvalidEscapeCharacter",e[e.InvalidCharacter=16]="InvalidCharacter"}(T||(T={})),function(e){e.is=function(e){return"string"==typeof e}}(S||(S={})),function(e){e.is=function(e){return"string"==typeof e}}(C||(C={})),function(e){e.MIN_VALUE=-2147483648,e.MAX_VALUE=2147483647,e.is=function(t){return"number"==typeof t&&e.MIN_VALUE<=t&&t<=e.MAX_VALUE}}(R||(R={})),function(e){e.MIN_VALUE=0,e.MAX_VALUE=2147483647,e.is=function(t){return"number"==typeof t&&e.MIN_VALUE<=t&&t<=e.MAX_VALUE}}(D||(D={})),function(e){e.create=function(e,t){return e===Number.MAX_VALUE&&(e=D.MAX_VALUE),t===Number.MAX_VALUE&&(t=D.MAX_VALUE),{line:e,character:t}},e.is=function(e){let t=e;return Je.objectLiteral(t)&&Je.uinteger(t.line)&&Je.uinteger(t.character)}}(w||(w={})),function(e){e.create=function(e,t,n,r){if(Je.uinteger(e)&&Je.uinteger(t)&&Je.uinteger(n)&&Je.uinteger(r))return{start:w.create(e,t),end:w.create(n,r)};if(w.is(e)&&w.is(t))return{start:e,end:t};throw new Error(`Range#create called with invalid arguments[${e}, ${t}, ${n}, ${r}]`)},e.is=function(e){let t=e;return Je.objectLiteral(t)&&w.is(t.start)&&w.is(t.end)}}(x||(x={})),function(e){e.create=function(e,t){return{uri:e,range:t}},e.is=function(e){let t=e;return Je.objectLiteral(t)&&x.is(t.range)&&(Je.string(t.uri)||Je.undefined(t.uri))}}(P||(P={})),function(e){e.create=function(e,t,n,r){return{targetUri:e,targetRange:t,targetSelectionRange:n,originSelectionRange:r}},e.is=function(e){let t=e;return Je.objectLiteral(t)&&x.is(t.targetRange)&&Je.string(t.targetUri)&&x.is(t.targetSelectionRange)&&(x.is(t.originSelectionRange)||Je.undefined(t.originSelectionRange))}}(O||(O={})),function(e){e.create=function(e,t,n,r){return{red:e,green:t,blue:n,alpha:r}},e.is=function(e){const t=e;return Je.objectLiteral(t)&&Je.numberRange(t.red,0,1)&&Je.numberRange(t.green,0,1)&&Je.numberRange(t.blue,0,1)&&Je.numberRange(t.alpha,0,1)}}(M||(M={})),function(e){e.create=function(e,t){return{range:e,color:t}},e.is=function(e){const t=e;return Je.objectLiteral(t)&&x.is(t.range)&&M.is(t.color)}}(N||(N={})),function(e){e.create=function(e,t,n){return{label:e,textEdit:t,additionalTextEdits:n}},e.is=function(e){const t=e;return Je.objectLiteral(t)&&Je.string(t.label)&&(Je.undefined(t.textEdit)||V.is(t))&&(Je.undefined(t.additionalTextEdits)||Je.typedArray(t.additionalTextEdits,V.is))}}(E||(E={})),function(e){e.Comment="comment",e.Imports="imports",e.Region="region"}(q||(q={})),function(e){e.create=function(e,t,n,r,o,i){const s={startLine:e,endLine:t};return Je.defined(n)&&(s.startCharacter=n),Je.defined(r)&&(s.endCharacter=r),Je.defined(o)&&(s.kind=o),Je.defined(i)&&(s.collapsedText=i),s},e.is=function(e){const t=e;return Je.objectLiteral(t)&&Je.uinteger(t.startLine)&&Je.uinteger(t.startLine)&&(Je.undefined(t.startCharacter)||Je.uinteger(t.startCharacter))&&(Je.undefined(t.endCharacter)||Je.uinteger(t.endCharacter))&&(Je.undefined(t.kind)||Je.string(t.kind))}}(A||(A={})),function(e){e.create=function(e,t){return{location:e,message:t}},e.is=function(e){let t=e;return Je.defined(t)&&P.is(t.location)&&Je.string(t.message)}}(j||(j={})),function(e){e.Error=1,e.Warning=2,e.Information=3,e.Hint=4}(I||(I={})),function(e){e.Unnecessary=1,e.Deprecated=2}(L||(L={})),function(e){e.is=function(e){const t=e;return Je.objectLiteral(t)&&Je.string(t.href)}}(F||(F={})),function(e){e.create=function(e,t,n,r,o,i){let s={range:e,message:t};return Je.defined(n)&&(s.severity=n),Je.defined(r)&&(s.code=r),Je.defined(o)&&(s.source=o),Je.defined(i)&&(s.relatedInformation=i),s},e.is=function(e){var t;let n=e;return Je.defined(n)&&x.is(n.range)&&Je.string(n.message)&&(Je.number(n.severity)||Je.undefined(n.severity))&&(Je.integer(n.code)||Je.string(n.code)||Je.undefined(n.code))&&(Je.undefined(n.codeDescription)||Je.string(null===(t=n.codeDescription)||void 0===t?void 0:t.href))&&(Je.string(n.source)||Je.undefined(n.source))&&(Je.undefined(n.relatedInformation)||Je.typedArray(n.relatedInformation,j.is))}}($||($={})),function(e){e.create=function(e,t,...n){let r={title:e,command:t};return Je.defined(n)&&n.length>0&&(r.arguments=n),r},e.is=function(e){let t=e;return Je.defined(t)&&Je.string(t.title)&&Je.string(t.command)}}(W||(W={})),function(e){e.replace=function(e,t){return{range:e,newText:t}},e.insert=function(e,t){return{range:{start:e,end:e},newText:t}},e.del=function(e){return{range:e,newText:""}},e.is=function(e){const t=e;return Je.objectLiteral(t)&&Je.string(t.newText)&&x.is(t.range)}}(V||(V={})),function(e){e.create=function(e,t,n){const r={label:e};return void 0!==t&&(r.needsConfirmation=t),void 0!==n&&(r.description=n),r},e.is=function(e){const t=e;return Je.objectLiteral(t)&&Je.string(t.label)&&(Je.boolean(t.needsConfirmation)||void 0===t.needsConfirmation)&&(Je.string(t.description)||void 0===t.description)}}(U||(U={})),function(e){e.is=function(e){const t=e;return Je.string(t)}}(H||(H={})),function(e){e.replace=function(e,t,n){return{range:e,newText:t,annotationId:n}},e.insert=function(e,t,n){return{range:{start:e,end:e},newText:t,annotationId:n}},e.del=function(e,t){return{range:e,newText:"",annotationId:t}},e.is=function(e){const t=e;return V.is(t)&&(U.is(t.annotationId)||H.is(t.annotationId))}}(B||(B={})),function(e){e.create=function(e,t){return{textDocument:e,edits:t}},e.is=function(e){let t=e;return Je.defined(t)&&Q.is(t.textDocument)&&Array.isArray(t.edits)}}(z||(z={})),function(e){e.create=function(e,t,n){let r={kind:"create",uri:e};return void 0===t||void 0===t.overwrite&&void 0===t.ignoreIfExists||(r.options=t),void 0!==n&&(r.annotationId=n),r},e.is=function(e){let t=e;return t&&"create"===t.kind&&Je.string(t.uri)&&(void 0===t.options||(void 0===t.options.overwrite||Je.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||Je.boolean(t.options.ignoreIfExists)))&&(void 0===t.annotationId||H.is(t.annotationId))}}(K||(K={})),function(e){e.create=function(e,t,n,r){let o={kind:"rename",oldUri:e,newUri:t};return void 0===n||void 0===n.overwrite&&void 0===n.ignoreIfExists||(o.options=n),void 0!==r&&(o.annotationId=r),o},e.is=function(e){let t=e;return t&&"rename"===t.kind&&Je.string(t.oldUri)&&Je.string(t.newUri)&&(void 0===t.options||(void 0===t.options.overwrite||Je.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||Je.boolean(t.options.ignoreIfExists)))&&(void 0===t.annotationId||H.is(t.annotationId))}}(J||(J={})),function(e){e.create=function(e,t,n){let r={kind:"delete",uri:e};return void 0===t||void 0===t.recursive&&void 0===t.ignoreIfNotExists||(r.options=t),void 0!==n&&(r.annotationId=n),r},e.is=function(e){let t=e;return t&&"delete"===t.kind&&Je.string(t.uri)&&(void 0===t.options||(void 0===t.options.recursive||Je.boolean(t.options.recursive))&&(void 0===t.options.ignoreIfNotExists||Je.boolean(t.options.ignoreIfNotExists)))&&(void 0===t.annotationId||H.is(t.annotationId))}}(G||(G={})),function(e){e.is=function(e){let t=e;return t&&(void 0!==t.changes||void 0!==t.documentChanges)&&(void 0===t.documentChanges||t.documentChanges.every((e=>Je.string(e.kind)?K.is(e)||J.is(e)||G.is(e):z.is(e))))}}(X||(X={})),function(e){e.create=function(e){return{uri:e}},e.is=function(e){let t=e;return Je.defined(t)&&Je.string(t.uri)}}(Z||(Z={})),function(e){e.create=function(e,t){return{uri:e,version:t}},e.is=function(e){let t=e;return Je.defined(t)&&Je.string(t.uri)&&Je.integer(t.version)}}(Y||(Y={})),function(e){e.create=function(e,t){return{uri:e,version:t}},e.is=function(e){let t=e;return Je.defined(t)&&Je.string(t.uri)&&(null===t.version||Je.integer(t.version))}}(Q||(Q={})),function(e){e.create=function(e,t,n,r){return{uri:e,languageId:t,version:n,text:r}},e.is=function(e){let t=e;return Je.defined(t)&&Je.string(t.uri)&&Je.string(t.languageId)&&Je.integer(t.version)&&Je.string(t.text)}}(ee||(ee={})),function(e){e.PlainText="plaintext",e.Markdown="markdown",e.is=function(t){const n=t;return n===e.PlainText||n===e.Markdown}}(te||(te={})),function(e){e.is=function(e){const t=e;return Je.objectLiteral(e)&&te.is(t.kind)&&Je.string(t.value)}}(ne||(ne={})),function(e){e.Text=1,e.Method=2,e.Function=3,e.Constructor=4,e.Field=5,e.Variable=6,e.Class=7,e.Interface=8,e.Module=9,e.Property=10,e.Unit=11,e.Value=12,e.Enum=13,e.Keyword=14,e.Snippet=15,e.Color=16,e.File=17,e.Reference=18,e.Folder=19,e.EnumMember=20,e.Constant=21,e.Struct=22,e.Event=23,e.Operator=24,e.TypeParameter=25}(re||(re={})),function(e){e.PlainText=1,e.Snippet=2}(oe||(oe={})),function(e){e.Deprecated=1}(ie||(ie={})),function(e){e.create=function(e,t,n){return{newText:e,insert:t,replace:n}},e.is=function(e){const t=e;return t&&Je.string(t.newText)&&x.is(t.insert)&&x.is(t.replace)}}(se||(se={})),function(e){e.asIs=1,e.adjustIndentation=2}(ae||(ae={})),function(e){e.is=function(e){const t=e;return t&&(Je.string(t.detail)||void 0===t.detail)&&(Je.string(t.description)||void 0===t.description)}}(ce||(ce={})),function(e){e.create=function(e){return{label:e}}}(ue||(ue={})),function(e){e.create=function(e,t){return{items:e||[],isIncomplete:!!t}}}(le||(le={})),function(e){e.fromPlainText=function(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")},e.is=function(e){const t=e;return Je.string(t)||Je.objectLiteral(t)&&Je.string(t.language)&&Je.string(t.value)}}(de||(de={})),function(e){e.is=function(e){let t=e;return!!t&&Je.objectLiteral(t)&&(ne.is(t.contents)||de.is(t.contents)||Je.typedArray(t.contents,de.is))&&(void 0===e.range||x.is(e.range))}}(fe||(fe={})),function(e){e.create=function(e,t){return t?{label:e,documentation:t}:{label:e}}}(he||(he={})),function(e){e.create=function(e,t,...n){let r={label:e};return Je.defined(t)&&(r.documentation=t),Je.defined(n)?r.parameters=n:r.parameters=[],r}}(pe||(pe={})),function(e){e.Text=1,e.Read=2,e.Write=3}(me||(me={})),function(e){e.create=function(e,t){let n={range:e};return Je.number(t)&&(n.kind=t),n}}(ge||(ge={})),function(e){e.File=1,e.Module=2,e.Namespace=3,e.Package=4,e.Class=5,e.Method=6,e.Property=7,e.Field=8,e.Constructor=9,e.Enum=10,e.Interface=11,e.Function=12,e.Variable=13,e.Constant=14,e.String=15,e.Number=16,e.Boolean=17,e.Array=18,e.Object=19,e.Key=20,e.Null=21,e.EnumMember=22,e.Struct=23,e.Event=24,e.Operator=25,e.TypeParameter=26}(ye||(ye={})),function(e){e.Deprecated=1}(be||(be={})),function(e){e.create=function(e,t,n,r,o){let i={name:e,kind:t,location:{uri:r,range:n}};return o&&(i.containerName=o),i}}(ve||(ve={})),function(e){e.create=function(e,t,n,r){return void 0!==r?{name:e,kind:t,location:{uri:n,range:r}}:{name:e,kind:t,location:{uri:n}}}}(ke||(ke={})),function(e){e.create=function(e,t,n,r,o,i){let s={name:e,detail:t,kind:n,range:r,selectionRange:o};return void 0!==i&&(s.children=i),s},e.is=function(e){let t=e;return t&&Je.string(t.name)&&Je.number(t.kind)&&x.is(t.range)&&x.is(t.selectionRange)&&(void 0===t.detail||Je.string(t.detail))&&(void 0===t.deprecated||Je.boolean(t.deprecated))&&(void 0===t.children||Array.isArray(t.children))&&(void 0===t.tags||Array.isArray(t.tags))}}(_e||(_e={})),function(e){e.Empty="",e.QuickFix="quickfix",e.Refactor="refactor",e.RefactorExtract="refactor.extract",e.RefactorInline="refactor.inline",e.RefactorRewrite="refactor.rewrite",e.Source="source",e.SourceOrganizeImports="source.organizeImports",e.SourceFixAll="source.fixAll"}(Te||(Te={})),function(e){e.Invoked=1,e.Automatic=2}(Se||(Se={})),function(e){e.create=function(e,t,n){let r={diagnostics:e};return null!=t&&(r.only=t),null!=n&&(r.triggerKind=n),r},e.is=function(e){let t=e;return Je.defined(t)&&Je.typedArray(t.diagnostics,$.is)&&(void 0===t.only||Je.typedArray(t.only,Je.string))&&(void 0===t.triggerKind||t.triggerKind===Se.Invoked||t.triggerKind===Se.Automatic)}}(Ce||(Ce={})),function(e){e.create=function(e,t,n){let r={title:e},o=!0;return"string"==typeof t?(o=!1,r.kind=t):W.is(t)?r.command=t:r.edit=t,o&&void 0!==n&&(r.kind=n),r},e.is=function(e){let t=e;return t&&Je.string(t.title)&&(void 0===t.diagnostics||Je.typedArray(t.diagnostics,$.is))&&(void 0===t.kind||Je.string(t.kind))&&(void 0!==t.edit||void 0!==t.command)&&(void 0===t.command||W.is(t.command))&&(void 0===t.isPreferred||Je.boolean(t.isPreferred))&&(void 0===t.edit||X.is(t.edit))}}(Re||(Re={})),function(e){e.create=function(e,t){let n={range:e};return Je.defined(t)&&(n.data=t),n},e.is=function(e){let t=e;return Je.defined(t)&&x.is(t.range)&&(Je.undefined(t.command)||W.is(t.command))}}(De||(De={})),function(e){e.create=function(e,t){return{tabSize:e,insertSpaces:t}},e.is=function(e){let t=e;return Je.defined(t)&&Je.uinteger(t.tabSize)&&Je.boolean(t.insertSpaces)}}(we||(we={})),function(e){e.create=function(e,t,n){return{range:e,target:t,data:n}},e.is=function(e){let t=e;return Je.defined(t)&&x.is(t.range)&&(Je.undefined(t.target)||Je.string(t.target))}}(xe||(xe={})),function(e){e.create=function(e,t){return{range:e,parent:t}},e.is=function(t){let n=t;return Je.objectLiteral(n)&&x.is(n.range)&&(void 0===n.parent||e.is(n.parent))}}(Pe||(Pe={})),function(e){e.namespace="namespace",e.type="type",e.class="class",e.enum="enum",e.interface="interface",e.struct="struct",e.typeParameter="typeParameter",e.parameter="parameter",e.variable="variable",e.property="property",e.enumMember="enumMember",e.event="event",e.function="function",e.method="method",e.macro="macro",e.keyword="keyword",e.modifier="modifier",e.comment="comment",e.string="string",e.number="number",e.regexp="regexp",e.operator="operator",e.decorator="decorator"}(Oe||(Oe={})),function(e){e.declaration="declaration",e.definition="definition",e.readonly="readonly",e.static="static",e.deprecated="deprecated",e.abstract="abstract",e.async="async",e.modification="modification",e.documentation="documentation",e.defaultLibrary="defaultLibrary"}(Me||(Me={})),function(e){e.is=function(e){const t=e;return Je.objectLiteral(t)&&(void 0===t.resultId||"string"==typeof t.resultId)&&Array.isArray(t.data)&&(0===t.data.length||"number"==typeof t.data[0])}}(Ne||(Ne={})),function(e){e.create=function(e,t){return{range:e,text:t}},e.is=function(e){const t=e;return null!=t&&x.is(t.range)&&Je.string(t.text)}}(Ee||(Ee={})),function(e){e.create=function(e,t,n){return{range:e,variableName:t,caseSensitiveLookup:n}},e.is=function(e){const t=e;return null!=t&&x.is(t.range)&&Je.boolean(t.caseSensitiveLookup)&&(Je.string(t.variableName)||void 0===t.variableName)}}(qe||(qe={})),function(e){e.create=function(e,t){return{range:e,expression:t}},e.is=function(e){const t=e;return null!=t&&x.is(t.range)&&(Je.string(t.expression)||void 0===t.expression)}}(Ae||(Ae={})),function(e){e.create=function(e,t){return{frameId:e,stoppedLocation:t}},e.is=function(e){const t=e;return Je.defined(t)&&x.is(e.stoppedLocation)}}(je||(je={})),function(e){e.Type=1,e.Parameter=2,e.is=function(e){return 1===e||2===e}}(Ie||(Ie={})),function(e){e.create=function(e){return{value:e}},e.is=function(e){const t=e;return Je.objectLiteral(t)&&(void 0===t.tooltip||Je.string(t.tooltip)||ne.is(t.tooltip))&&(void 0===t.location||P.is(t.location))&&(void 0===t.command||W.is(t.command))}}(Le||(Le={})),function(e){e.create=function(e,t,n){const r={position:e,label:t};return void 0!==n&&(r.kind=n),r},e.is=function(e){const t=e;return Je.objectLiteral(t)&&w.is(t.position)&&(Je.string(t.label)||Je.typedArray(t.label,Le.is))&&(void 0===t.kind||Ie.is(t.kind))&&void 0===t.textEdits||Je.typedArray(t.textEdits,V.is)&&(void 0===t.tooltip||Je.string(t.tooltip)||ne.is(t.tooltip))&&(void 0===t.paddingLeft||Je.boolean(t.paddingLeft))&&(void 0===t.paddingRight||Je.boolean(t.paddingRight))}}(Fe||(Fe={})),function(e){e.createSnippet=function(e){return{kind:"snippet",value:e}}}($e||($e={})),function(e){e.create=function(e,t,n,r){return{insertText:e,filterText:t,range:n,command:r}}}(We||(We={})),function(e){e.create=function(e){return{items:e}}}(Ve||(Ve={})),function(e){e.Invoked=0,e.Automatic=1}(Ue||(Ue={})),function(e){e.create=function(e,t){return{range:e,text:t}}}(He||(He={})),function(e){e.create=function(e,t){return{triggerKind:e,selectedCompletionInfo:t}}}(Be||(Be={})),function(e){e.is=function(e){const t=e;return Je.objectLiteral(t)&&C.is(t.uri)&&Je.string(t.name)}}(ze||(ze={})),function(e){function t(e,n){if(e.length<=1)return e;const r=e.length/2|0,o=e.slice(0,r),i=e.slice(r);t(o,n),t(i,n);let s=0,a=0,c=0;for(;s<o.length&&a<i.length;){let t=n(o[s],i[a]);e[c++]=t<=0?o[s++]:i[a++]}for(;s<o.length;)e[c++]=o[s++];for(;a<i.length;)e[c++]=i[a++];return e}e.create=function(e,t,n,r){return new ct(e,t,n,r)},e.is=function(e){let t=e;return!!(Je.defined(t)&&Je.string(t.uri)&&(Je.undefined(t.languageId)||Je.string(t.languageId))&&Je.uinteger(t.lineCount)&&Je.func(t.getText)&&Je.func(t.positionAt)&&Je.func(t.offsetAt))},e.applyEdits=function(e,n){let r=e.getText(),o=t(n,((e,t)=>{let n=e.range.start.line-t.range.start.line;return 0===n?e.range.start.character-t.range.start.character:n})),i=r.length;for(let t=o.length-1;t>=0;t--){let n=o[t],s=e.offsetAt(n.range.start),a=e.offsetAt(n.range.end);if(!(a<=i))throw new Error("Overlapping edit");r=r.substring(0,s)+n.newText+r.substring(a,r.length),i=s}return r}}(Ke||(Ke={}));class ct{constructor(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content}update(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0}getLineOffsets(){if(void 0===this._lineOffsets){let e=[],t=this._content,n=!0;for(let r=0;r<t.length;r++){n&&(e.push(r),n=!1);let o=t.charAt(r);n="\r"===o||"\n"===o,"\r"===o&&r+1<t.length&&"\n"===t.charAt(r+1)&&r++}n&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let t=this.getLineOffsets(),n=0,r=t.length;if(0===r)return w.create(0,e);for(;n<r;){let o=Math.floor((n+r)/2);t[o]>e?r=o:n=o+1}let o=n-1;return w.create(o,e-t[o])}offsetAt(e){let t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;let n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)}get lineCount(){return this.getLineOffsets().length}}!function(e){const t=Object.prototype.toString;e.defined=function(e){return void 0!==e},e.undefined=function(e){return void 0===e},e.boolean=function(e){return!0===e||!1===e},e.string=function(e){return"[object String]"===t.call(e)},e.number=function(e){return"[object Number]"===t.call(e)},e.numberRange=function(e,n,r){return"[object Number]"===t.call(e)&&n<=e&&e<=r},e.integer=function(e){return"[object Number]"===t.call(e)&&-2147483648<=e&&e<=2147483647},e.uinteger=function(e){return"[object Number]"===t.call(e)&&0<=e&&e<=2147483647},e.func=function(e){return"[object Function]"===t.call(e)},e.objectLiteral=function(e){return null!==e&&"object"==typeof e},e.typedArray=function(e,t){return Array.isArray(e)&&e.every(t)}}(Je||(Je={}));class ut{constructor(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){const t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content}update(e,t){for(const t of e)if(ut.isIncremental(t)){const e=ht(t.range),n=this.offsetAt(e.start),r=this.offsetAt(e.end);this._content=this._content.substring(0,n)+t.text+this._content.substring(r,this._content.length);const o=Math.max(e.start.line,0),i=Math.max(e.end.line,0);let s=this._lineOffsets;const a=dt(t.text,!1,n);if(i-o===a.length)for(let e=0,t=a.length;e<t;e++)s[e+o+1]=a[e];else a.length<1e4?s.splice(o+1,i-o,...a):this._lineOffsets=s=s.slice(0,o+1).concat(a,s.slice(i+1));const c=t.text.length-(r-n);if(0!==c)for(let e=o+1+a.length,t=s.length;e<t;e++)s[e]=s[e]+c}else{if(!ut.isFull(t))throw new Error("Unknown change event received");this._content=t.text,this._lineOffsets=void 0}this._version=t}getLineOffsets(){return void 0===this._lineOffsets&&(this._lineOffsets=dt(this._content,!0)),this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);const t=this.getLineOffsets();let n=0,r=t.length;if(0===r)return{line:0,character:e};for(;n<r;){const o=Math.floor((n+r)/2);t[o]>e?r=o:n=o+1}const o=n-1;return{line:o,character:(e=this.ensureBeforeEOL(e,t[o]))-t[o]}}offsetAt(e){const t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;const n=t[e.line];if(e.character<=0)return n;const r=e.line+1<t.length?t[e.line+1]:this._content.length,o=Math.min(n+e.character,r);return this.ensureBeforeEOL(o,n)}ensureBeforeEOL(e,t){for(;e>t&&ft(this._content.charCodeAt(e-1));)e--;return e}get lineCount(){return this.getLineOffsets().length}static isIncremental(e){const t=e;return null!=t&&"string"==typeof t.text&&void 0!==t.range&&(void 0===t.rangeLength||"number"==typeof t.rangeLength)}static isFull(e){const t=e;return null!=t&&"string"==typeof t.text&&void 0===t.range&&void 0===t.rangeLength}}function lt(e,t){if(e.length<=1)return e;const n=e.length/2|0,r=e.slice(0,n),o=e.slice(n);lt(r,t),lt(o,t);let i=0,s=0,a=0;for(;i<r.length&&s<o.length;){const n=t(r[i],o[s]);e[a++]=n<=0?r[i++]:o[s++]}for(;i<r.length;)e[a++]=r[i++];for(;s<o.length;)e[a++]=o[s++];return e}function dt(e,t,n=0){const r=t?[n]:[];for(let t=0;t<e.length;t++){const o=e.charCodeAt(t);ft(o)&&(13===o&&t+1<e.length&&10===e.charCodeAt(t+1)&&t++,r.push(n+t+1))}return r}function ft(e){return 13===e||10===e}function ht(e){const t=e.start,n=e.end;return t.line>n.line||t.line===n.line&&t.character>n.character?{start:n,end:t}:e}function pt(e){const t=ht(e.range);return t!==e.range?{newText:e.newText,range:t}:e}!function(e){e.create=function(e,t,n,r){return new ut(e,t,n,r)},e.update=function(e,t,n){if(e instanceof ut)return e.update(t,n),e;throw new Error("TextDocument.update: document must be created by TextDocument.create")},e.applyEdits=function(e,t){const n=e.getText(),r=lt(t.map(pt),((e,t)=>{const n=e.range.start.line-t.range.start.line;return 0===n?e.range.start.character-t.range.start.character:n}));let o=0;const i=[];for(const t of r){const r=e.offsetAt(t.range.start);if(r<o)throw new Error("Overlapping edit");r>o&&i.push(n.substring(o,r)),t.newText.length&&i.push(t.newText),o=e.offsetAt(t.range.end)}return i.push(n.substr(o)),i.join("")}}(Ge||(Ge={})),function(e){e[e.Undefined=0]="Undefined",e[e.EnumValueMismatch=1]="EnumValueMismatch",e[e.Deprecated=2]="Deprecated",e[e.UnexpectedEndOfComment=257]="UnexpectedEndOfComment",e[e.UnexpectedEndOfString=258]="UnexpectedEndOfString",e[e.UnexpectedEndOfNumber=259]="UnexpectedEndOfNumber",e[e.InvalidUnicode=260]="InvalidUnicode",e[e.InvalidEscapeCharacter=261]="InvalidEscapeCharacter",e[e.InvalidCharacter=262]="InvalidCharacter",e[e.PropertyExpected=513]="PropertyExpected",e[e.CommaExpected=514]="CommaExpected",e[e.ColonExpected=515]="ColonExpected",e[e.ValueExpected=516]="ValueExpected",e[e.CommaOrCloseBacketExpected=517]="CommaOrCloseBacketExpected",e[e.CommaOrCloseBraceExpected=518]="CommaOrCloseBraceExpected",e[e.TrailingComma=519]="TrailingComma",e[e.DuplicateKey=520]="DuplicateKey",e[e.CommentNotPermitted=521]="CommentNotPermitted",e[e.PropertyKeysMustBeDoublequoted=528]="PropertyKeysMustBeDoublequoted",e[e.SchemaResolveError=768]="SchemaResolveError",e[e.SchemaUnsupportedFeature=769]="SchemaUnsupportedFeature"}(Xe||(Xe={})),function(e){e[e.v3=3]="v3",e[e.v4=4]="v4",e[e.v6=6]="v6",e[e.v7=7]="v7",e[e.v2019_09=19]="v2019_09",e[e.v2020_12=20]="v2020_12"}(Ze||(Ze={})),function(e){e.LATEST={textDocument:{completion:{completionItem:{documentationFormat:[te.Markdown,te.PlainText],commitCharactersSupport:!0,labelDetailsSupport:!0}}}}}(Ye||(Ye={}));var mt=n(7608),gt=n(5747);const yt={"color-hex":{errorMessage:gt.t("Invalid color format. Use #RGB, #RGBA, #RRGGBB or #RRGGBBAA."),pattern:/^#([0-9A-Fa-f]{3,4}|([0-9A-Fa-f]{2}){3,4})$/},"date-time":{errorMessage:gt.t("String is not a RFC3339 date-time."),pattern:/^(\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]|60)(\.[0-9]+)?(Z|(\+|-)([01][0-9]|2[0-3]):([0-5][0-9]))$/i},date:{errorMessage:gt.t("String is not a RFC3339 date."),pattern:/^(\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/i},time:{errorMessage:gt.t("String is not a RFC3339 time."),pattern:/^([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]|60)(\.[0-9]+)?(Z|(\+|-)([01][0-9]|2[0-3]):([0-5][0-9]))$/i},email:{errorMessage:gt.t("String is not an e-mail address."),pattern:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}))$/},hostname:{errorMessage:gt.t("String is not a hostname."),pattern:/^(?=.{1,253}\.?$)[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*\.?$/i},ipv4:{errorMessage:gt.t("String is not an IPv4 address."),pattern:/^(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\.){3}(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)$/},ipv6:{errorMessage:gt.t("String is not an IPv6 address."),pattern:/^((([0-9a-f]{1,4}:){7}([0-9a-f]{1,4}|:))|(([0-9a-f]{1,4}:){6}(:[0-9a-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){5}(((:[0-9a-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){4}(((:[0-9a-f]{1,4}){1,3})|((:[0-9a-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){3}(((:[0-9a-f]{1,4}){1,4})|((:[0-9a-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){2}(((:[0-9a-f]{1,4}){1,5})|((:[0-9a-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){1}(((:[0-9a-f]{1,4}){1,6})|((:[0-9a-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9a-f]{1,4}){1,7})|((:[0-9a-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))$/i}};class bt{constructor(e,t,n=0){this.offset=t,this.length=n,this.parent=e}get children(){return[]}toString(){return"type: "+this.type+" ("+this.offset+"/"+this.length+")"+(this.parent?" parent: {"+this.parent.toString()+"}":"")}}class vt extends bt{constructor(e,t){super(e,t),this.type="null",this.value=null}}class kt extends bt{constructor(e,t,n){super(e,n),this.type="boolean",this.value=t}}class _t extends bt{constructor(e,t){super(e,t),this.type="array",this.items=[]}get children(){return this.items}}class Tt extends bt{constructor(e,t){super(e,t),this.type="number",this.isInteger=!0,this.value=Number.NaN}}class St extends bt{constructor(e,t,n){super(e,t,n),this.type="string",this.value=""}}class Ct extends bt{constructor(e,t,n){super(e,t),this.type="property",this.colonOffset=-1,this.keyNode=n}get children(){return this.valueNode?[this.keyNode,this.valueNode]:[this.keyNode]}}class Rt extends bt{constructor(e,t){super(e,t),this.type="object",this.properties=[]}get children(){return this.properties}}function Dt(e){return nt(e)?e?{}:{not:{}}:e}var wt;!function(e){e[e.Key=0]="Key",e[e.Enum=1]="Enum"}(wt||(wt={}));const xt="http://json-schema.org/",Pt="https://json-schema.org/";function Ot(e){e.startsWith(xt)&&(e=Pt+e.substring(xt.length));try{return mt.URI.parse(e).toString(!0)}catch(t){return e}}function Mt(e){return Nt[Ot(e)]??void 0}const Nt={"https://json-schema.org/draft-03/schema":Ze.v3,"https://json-schema.org/draft-04/schema":Ze.v4,"https://json-schema.org/draft-06/schema":Ze.v6,"https://json-schema.org/draft-07/schema":Ze.v7,"https://json-schema.org/draft/2019-09/schema":Ze.v2019_09,"https://json-schema.org/draft/2020-12/schema":Ze.v2020_12};class Et{constructor(e){this.schemaDraft=e}}class qt{constructor(e=-1,t){this.focusOffset=e,this.exclude=t,this.schemas=[]}add(e){this.schemas.push(e)}merge(e){Array.prototype.push.apply(this.schemas,e.schemas)}include(e){return(-1===this.focusOffset||Ft(e,this.focusOffset))&&e!==this.exclude}newSub(){return new qt(-1,this.exclude)}}class At{constructor(){}get schemas(){return[]}add(e){}merge(e){}include(e){return!0}newSub(){return this}}At.instance=new At;class jt{constructor(){this.problems=[],this.propertiesMatches=0,this.processedProperties=new Set,this.propertiesValueMatches=0,this.primaryValueMatches=0,this.enumValueMatch=!1,this.enumValues=void 0}hasProblems(){return!!this.problems.length}merge(e){this.problems=this.problems.concat(e.problems),this.propertiesMatches+=e.propertiesMatches,this.propertiesValueMatches+=e.propertiesValueMatches,this.mergeProcessedProperties(e)}mergeEnumValues(e){!this.enumValueMatch&&!e.enumValueMatch&&this.enumValues&&e.enumValues&&(this.enumValues=this.enumValues.concat(e.enumValues))}updateEnumMismatchProblemMessages(){if(!this.enumValueMatch&&this.enumValues)for(const e of this.problems)e.code===Xe.EnumValueMismatch&&(e.message=gt.t("Value is not accepted. Valid values: {0}.",this.enumValues.map((e=>JSON.stringify(e))).join(", ")))}mergePropertyMatch(e){this.problems=this.problems.concat(e.problems),this.propertiesMatches++,(e.enumValueMatch||!e.hasProblems()&&e.propertiesMatches)&&this.propertiesValueMatches++,e.enumValueMatch&&e.enumValues&&1===e.enumValues.length&&this.primaryValueMatches++}mergeProcessedProperties(e){e.processedProperties.forEach((e=>this.processedProperties.add(e)))}compare(e){const t=this.hasProblems();return t!==e.hasProblems()?t?-1:1:this.enumValueMatch!==e.enumValueMatch?e.enumValueMatch?-1:1:this.primaryValueMatches!==e.primaryValueMatches?this.primaryValueMatches-e.primaryValueMatches:this.propertiesValueMatches!==e.propertiesValueMatches?this.propertiesValueMatches-e.propertiesValueMatches:this.propertiesMatches-e.propertiesMatches}}function It(e){return _(e)}function Lt(e){return k(e)}function Ft(e,t,n=!1){return t>=e.offset&&t<e.offset+e.length||n&&t===e.offset+e.length}class $t{constructor(e,t=[],n=[]){this.root=e,this.syntaxErrors=t,this.comments=n}getNodeFromOffset(e,t=!1){if(this.root)return v(this.root,e,t)}visit(e){if(this.root){const t=n=>{let r=e(n);const o=n.children;if(Array.isArray(o))for(let e=0;e<o.length&&r;e++)r=t(o[e]);return r};t(this.root)}}validate(e,t,n=I.Warning,r){if(this.root&&t){const o=new jt;return Vt(this.root,t,o,At.instance,new Et(r??Wt(t))),o.problems.map((t=>{const r=x.create(e.positionAt(t.location.offset),e.positionAt(t.location.offset+t.location.length));return $.create(r,t.message,t.severity??n,t.code)}))}}getMatchingSchemas(e,t=-1,n){if(this.root&&e){const r=new qt(t,n),o=Wt(e),i=new Et(o);return Vt(this.root,e,new jt,r,i),r.schemas}return[]}}function Wt(e,t=Ze.v2020_12){let n=e.$schema;return n?Mt(n)??t:t}function Vt(e,t,n,r,o){if(!e||!r.include(e))return;if("property"===e.type)return Vt(e.valueNode,t,n,r,o);const i=e;switch(function(){function e(e){return i.type===e||"integer"===e&&"number"===i.type&&i.isInteger}if(Array.isArray(t.type)?t.type.some(e)||n.problems.push({location:{offset:i.offset,length:i.length},message:t.errorMessage||gt.t("Incorrect type. Expected one of {0}.",t.type.join(", "))}):t.type&&(e(t.type)||n.problems.push({location:{offset:i.offset,length:i.length},message:t.errorMessage||gt.t('Incorrect type. Expected "{0}".',t.type)})),Array.isArray(t.allOf))for(const e of t.allOf){const t=new jt,s=r.newSub();Vt(i,Dt(e),t,s,o),n.merge(t),r.merge(s)}const s=Dt(t.not);if(s){const e=new jt,a=r.newSub();Vt(i,s,e,a,o),e.hasProblems()||n.problems.push({location:{offset:i.offset,length:i.length},message:t.errorMessage||gt.t("Matches a schema that is not allowed.")});for(const e of a.schemas)e.inverted=!e.inverted,r.add(e)}const a=(e,t)=>{const s=[];let a;for(const n of e){const e=Dt(n),c=new jt,u=r.newSub();if(Vt(i,e,c,u,o),c.hasProblems()||s.push(e),a)if(t||c.hasProblems()||a.validationResult.hasProblems()){const t=c.compare(a.validationResult);t>0?a={schema:e,validationResult:c,matchingSchemas:u}:0===t&&(a.matchingSchemas.merge(u),a.validationResult.mergeEnumValues(c))}else a.matchingSchemas.merge(u),a.validationResult.propertiesMatches+=c.propertiesMatches,a.validationResult.propertiesValueMatches+=c.propertiesValueMatches,a.validationResult.mergeProcessedProperties(c);else a={schema:e,validationResult:c,matchingSchemas:u}}return s.length>1&&t&&n.problems.push({location:{offset:i.offset,length:1},message:gt.t("Matches multiple schemas when only one must validate.")}),a&&(a.validationResult.updateEnumMismatchProblemMessages(),n.merge(a.validationResult),r.merge(a.matchingSchemas)),s.length};Array.isArray(t.anyOf)&&a(t.anyOf,!1),Array.isArray(t.oneOf)&&a(t.oneOf,!0);const c=e=>{const t=new jt,s=r.newSub();Vt(i,Dt(e),t,s,o),n.merge(t),r.merge(s)},u=Dt(t.if);if(u&&((e,t,s)=>{const a=Dt(e),u=new jt,l=r.newSub();Vt(i,a,u,l,o),r.merge(l),n.mergeProcessedProperties(u),u.hasProblems()?s&&c(s):t&&c(t)})(u,Dt(t.then),Dt(t.else)),Array.isArray(t.enum)){const e=It(i);let r=!1;for(const n of t.enum)if(Qe(e,n)){r=!0;break}n.enumValues=t.enum,n.enumValueMatch=r,r||n.problems.push({location:{offset:i.offset,length:i.length},code:Xe.EnumValueMismatch,message:t.errorMessage||gt.t("Value is not accepted. Valid values: {0}.",t.enum.map((e=>JSON.stringify(e))).join(", "))})}tt(t.const)&&(Qe(It(i),t.const)?n.enumValueMatch=!0:(n.problems.push({location:{offset:i.offset,length:i.length},code:Xe.EnumValueMismatch,message:t.errorMessage||gt.t("Value must be {0}.",JSON.stringify(t.const))}),n.enumValueMatch=!1),n.enumValues=[t.const]);let l=t.deprecationMessage;if(l||t.deprecated){l=l||gt.t("Value is deprecated");let e="property"===i.parent?.type?i.parent:i;n.problems.push({location:{offset:e.offset,length:e.length},severity:I.Warning,message:l,code:Xe.Deprecated})}}(),i.type){case"object":!function(e){const i=Object.create(null),s=new Set;for(const t of e.properties){const e=t.keyNode.value;i[e]=t.valueNode,s.add(e)}if(Array.isArray(t.required))for(const r of t.required)if(!i[r]){const t=e.parent&&"property"===e.parent.type&&e.parent.keyNode,o=t?{offset:t.offset,length:t.length}:{offset:e.offset,length:1};n.problems.push({location:o,message:gt.t('Missing property "{0}".',r)})}const a=e=>{s.delete(e),n.processedProperties.add(e)};if(t.properties)for(const e of Object.keys(t.properties)){a(e);const s=t.properties[e],c=i[e];if(c)if(nt(s))if(s)n.propertiesMatches++,n.propertiesValueMatches++;else{const r=c.parent;n.problems.push({location:{offset:r.keyNode.offset,length:r.keyNode.length},message:t.errorMessage||gt.t("Property {0} is not allowed.",e)})}else{const e=new jt;Vt(c,s,e,r,o),n.mergePropertyMatch(e)}}if(t.patternProperties)for(const e of Object.keys(t.patternProperties)){const c=st(e);if(c){const u=[];for(const a of s)if(c.test(a)){u.push(a);const s=i[a];if(s){const i=t.patternProperties[e];if(nt(i))if(i)n.propertiesMatches++,n.propertiesValueMatches++;else{const e=s.parent;n.problems.push({location:{offset:e.keyNode.offset,length:e.keyNode.length},message:t.errorMessage||gt.t("Property {0} is not allowed.",a)})}else{const e=new jt;Vt(s,i,e,r,o),n.mergePropertyMatch(e)}}}u.forEach(a)}}const c=t.additionalProperties;if(void 0!==c)for(const e of s){a(e);const s=i[e];if(s)if(!1===c){const r=s.parent;n.problems.push({location:{offset:r.keyNode.offset,length:r.keyNode.length},message:t.errorMessage||gt.t("Property {0} is not allowed.",e)})}else if(!0!==c){const e=new jt;Vt(s,c,e,r,o),n.mergePropertyMatch(e)}}const u=t.unevaluatedProperties;if(void 0!==u){const e=[];for(const a of s)if(!n.processedProperties.has(a)){e.push(a);const s=i[a];if(s)if(!1===u){const e=s.parent;n.problems.push({location:{offset:e.keyNode.offset,length:e.keyNode.length},message:t.errorMessage||gt.t("Property {0} is not allowed.",a)})}else if(!0!==u){const e=new jt;Vt(s,u,e,r,o),n.mergePropertyMatch(e)}}e.forEach(a)}if(et(t.maxProperties)&&e.properties.length>t.maxProperties&&n.problems.push({location:{offset:e.offset,length:e.length},message:gt.t("Object has more properties than limit of {0}.",t.maxProperties)}),et(t.minProperties)&&e.properties.length<t.minProperties&&n.problems.push({location:{offset:e.offset,length:e.length},message:gt.t("Object has fewer properties than the required number of {0}",t.minProperties)}),t.dependentRequired)for(const e in t.dependentRequired){const n=i[e],r=t.dependentRequired[e];n&&Array.isArray(r)&&d(e,r)}if(t.dependentSchemas)for(const e in t.dependentSchemas){const n=i[e],r=t.dependentSchemas[e];n&&ot(r)&&d(e,r)}if(t.dependencies)for(const e in t.dependencies)i[e]&&d(e,t.dependencies[e]);const l=Dt(t.propertyNames);if(l)for(const t of e.properties){const e=t.keyNode;e&&Vt(e,l,n,At.instance,o)}function d(t,s){if(Array.isArray(s))for(const r of s)i[r]?n.propertiesValueMatches++:n.problems.push({location:{offset:e.offset,length:e.length},message:gt.t("Object is missing property {0} required by property {1}.",r,t)});else{const t=Dt(s);if(t){const i=new jt;Vt(e,t,i,r,o),n.mergePropertyMatch(i)}}}}(i);break;case"array":!function(e){let i,s;o.schemaDraft>=Ze.v2020_12?(i=t.prefixItems,s=Array.isArray(t.items)?void 0:t.items):(i=Array.isArray(t.items)?t.items:void 0,s=Array.isArray(t.items)?t.additionalItems:t.items);let a=0;if(void 0!==i){const l=Math.min(i.length,e.items.length);for(;a<l;a++){const d=Dt(i[a]),f=new jt,h=e.items[a];h&&(Vt(h,d,f,r,o),n.mergePropertyMatch(f)),n.processedProperties.add(String(a))}}if(void 0!==s&&a<e.items.length)if("boolean"==typeof s)for(!1===s&&n.problems.push({location:{offset:e.offset,length:e.length},message:gt.t("Array has too many items according to schema. Expected {0} or fewer.",a)});a<e.items.length;a++)n.processedProperties.add(String(a)),n.propertiesValueMatches++;else for(;a<e.items.length;a++){const p=new jt;Vt(e.items[a],s,p,r,o),n.mergePropertyMatch(p),n.processedProperties.add(String(a))}const c=Dt(t.contains);if(c){let m=0;for(let g=0;g<e.items.length;g++){const y=e.items[g],b=new jt;Vt(y,c,b,At.instance,o),b.hasProblems()||(m++,o.schemaDraft>=Ze.v2020_12&&n.processedProperties.add(String(g)))}0!==m||et(t.minContains)||n.problems.push({location:{offset:e.offset,length:e.length},message:t.errorMessage||gt.t("Array does not contain required item.")}),et(t.minContains)&&m<t.minContains&&n.problems.push({location:{offset:e.offset,length:e.length},message:t.errorMessage||gt.t("Array has too few items that match the contains contraint. Expected {0} or more.",t.minContains)}),et(t.maxContains)&&m>t.maxContains&&n.problems.push({location:{offset:e.offset,length:e.length},message:t.errorMessage||gt.t("Array has too many items that match the contains contraint. Expected {0} or less.",t.maxContains)})}const u=t.unevaluatedItems;if(void 0!==u)for(let v=0;v<e.items.length;v++){if(!n.processedProperties.has(String(v)))if(!1===u)n.problems.push({location:{offset:e.offset,length:e.length},message:gt.t("Item does not match any validation rule from the array.")});else{const k=new jt;Vt(e.items[v],t.unevaluatedItems,k,r,o),n.mergePropertyMatch(k)}n.processedProperties.add(String(v)),n.propertiesValueMatches++}if(et(t.minItems)&&e.items.length<t.minItems&&n.problems.push({location:{offset:e.offset,length:e.length},message:gt.t("Array has too few items. Expected {0} or more.",t.minItems)}),et(t.maxItems)&&e.items.length>t.maxItems&&n.problems.push({location:{offset:e.offset,length:e.length},message:gt.t("Array has too many items. Expected {0} or fewer.",t.maxItems)}),!0===t.uniqueItems){const _=It(e);function T(){for(let e=0;e<_.length-1;e++){const t=_[e];for(let n=e+1;n<_.length;n++)if(Qe(t,_[n]))return!0}return!1}T()&&n.problems.push({location:{offset:e.offset,length:e.length},message:gt.t("Array has duplicate items.")})}}(i);break;case"string":!function(e){if(et(t.minLength)&&at(e.value)<t.minLength&&n.problems.push({location:{offset:e.offset,length:e.length},message:gt.t("String is shorter than the minimum length of {0}.",t.minLength)}),et(t.maxLength)&&at(e.value)>t.maxLength&&n.problems.push({location:{offset:e.offset,length:e.length},message:gt.t("String is longer than the maximum length of {0}.",t.maxLength)}),rt(t.pattern)){const r=st(t.pattern);r?.test(e.value)||n.problems.push({location:{offset:e.offset,length:e.length},message:t.patternErrorMessage||t.errorMessage||gt.t('String does not match the pattern of "{0}".',t.pattern)})}if(t.format)switch(t.format){case"uri":case"uri-reference":{let r;if(e.value){const n=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/.exec(e.value);n?n[2]||"uri"!==t.format||(r=gt.t("URI with a scheme is expected.")):r=gt.t("URI is expected.")}else r=gt.t("URI expected.");r&&n.problems.push({location:{offset:e.offset,length:e.length},message:t.patternErrorMessage||t.errorMessage||gt.t("String is not a URI: {0}",r)})}break;case"color-hex":case"date-time":case"date":case"time":case"email":case"hostname":case"ipv4":case"ipv6":const r=yt[t.format];e.value&&r.pattern.exec(e.value)||n.problems.push({location:{offset:e.offset,length:e.length},message:t.patternErrorMessage||t.errorMessage||r.errorMessage})}}(i);break;case"number":!function(e){const r=e.value;function o(e){const t=/^(-?\d+)(?:\.(\d+))?(?:e([-+]\d+))?$/.exec(e.toString());return t&&{value:Number(t[1]+(t[2]||"")),multiplier:(t[2]?.length||0)-(parseInt(t[3])||0)}}if(et(t.multipleOf)){let i=-1;if(Number.isInteger(t.multipleOf))i=r%t.multipleOf;else{let e=o(t.multipleOf),n=o(r);if(e&&n){const t=10**Math.abs(n.multiplier-e.multiplier);n.multiplier<e.multiplier?n.value*=t:e.value*=t,i=n.value%e.value}}0!==i&&n.problems.push({location:{offset:e.offset,length:e.length},message:gt.t("Value is not divisible by {0}.",t.multipleOf)})}function i(e,t){return et(t)?t:nt(t)&&t?e:void 0}function s(e,t){if(!nt(t)||!t)return e}const a=i(t.minimum,t.exclusiveMinimum);et(a)&&r<=a&&n.problems.push({location:{offset:e.offset,length:e.length},message:gt.t("Value is below the exclusive minimum of {0}.",a)});const c=i(t.maximum,t.exclusiveMaximum);et(c)&&r>=c&&n.problems.push({location:{offset:e.offset,length:e.length},message:gt.t("Value is above the exclusive maximum of {0}.",c)});const u=s(t.minimum,t.exclusiveMinimum);et(u)&&r<u&&n.problems.push({location:{offset:e.offset,length:e.length},message:gt.t("Value is below the minimum of {0}.",u)});const l=s(t.maximum,t.exclusiveMaximum);et(l)&&r>l&&n.problems.push({location:{offset:e.offset,length:e.length},message:gt.t("Value is above the maximum of {0}.",l)})}(i)}r.add({node:i,schema:t})}function Ut(e,t,n){if(null!==e&&"object"==typeof e){const r=t+"\t";if(Array.isArray(e)){if(0===e.length)return"[]";let o="[\n";for(let t=0;t<e.length;t++)o+=r+Ut(e[t],r,n),t<e.length-1&&(o+=","),o+="\n";return o+=t+"]",o}{const o=Object.keys(e);if(0===o.length)return"{}";let i="{\n";for(let t=0;t<o.length;t++){const s=o[t];i+=r+JSON.stringify(s)+": "+Ut(e[s],r,n),t<o.length-1&&(i+=","),i+="\n"}return i+=t+"}",i}}return n(e)}class Ht{constructor(e,t=[],n=Promise,r={}){this.schemaService=e,this.contributions=t,this.promiseConstructor=n,this.clientCapabilities=r}doResolve(e){for(let t=this.contributions.length-1;t>=0;t--){const n=this.contributions[t].resolveCompletion;if(n){const t=n(e);if(t)return t}}return this.promiseConstructor.resolve(e)}doComplete(e,t,n){const r={items:[],isIncomplete:!1},o=e.getText(),i=e.offsetAt(t);let s=n.getNodeFromOffset(i,!0);if(this.isInComment(e,s?s.offset:0,i))return Promise.resolve(r);if(s&&i===s.offset+s.length&&i>0){const e=o[i-1];("object"===s.type&&"}"===e||"array"===s.type&&"]"===e)&&(s=s.parent)}const a=this.getCurrentWord(e,i);let c;if(!s||"string"!==s.type&&"number"!==s.type&&"boolean"!==s.type&&"null"!==s.type){let n=i-a.length;n>0&&'"'===o[n-1]&&n--,c=x.create(e.positionAt(n),t)}else c=x.create(e.positionAt(s.offset),e.positionAt(s.offset+s.length));const u=new Map,l={add:e=>{let t=e.label;const n=u.get(t);if(n)n.documentation||(n.documentation=e.documentation),n.detail||(n.detail=e.detail),n.labelDetails||(n.labelDetails=e.labelDetails);else{if(t=t.replace(/[\n]/g,"↵"),t.length>60){const e=t.substr(0,57).trim()+"...";u.has(e)||(t=e)}e.textEdit=V.replace(c,e.insertText),e.label=t,u.set(t,e),r.items.push(e)}},setAsIncomplete:()=>{r.isIncomplete=!0},error:e=>{console.error(e)},getNumberOfProposals:()=>r.items.length};return this.schemaService.getSchemaForResource(e.uri,n).then((t=>{const d=[];let f,h=!0,p="";if(s&&"string"===s.type){const e=s.parent;e&&"property"===e.type&&e.keyNode===s&&(h=!e.valueNode,f=e,p=o.substr(s.offset+1,s.length-2),e&&(s=e.parent))}if(s&&"object"===s.type){if(s.offset===i)return r;s.properties.forEach((e=>{f&&f===e||u.set(e.keyNode.value,ue.create("__"))}));let m="";h&&(m=this.evaluateSeparatorAfter(e,e.offsetAt(c.end))),t?this.getPropertyCompletions(t,n,s,h,m,l):this.getSchemaLessPropertyCompletions(n,s,p,l);const g=Lt(s);this.contributions.forEach((t=>{const n=t.collectPropertyCompletions(e.uri,g,a,h,""===m,l);n&&d.push(n)})),!t&&a.length>0&&'"'!==o.charAt(i-a.length-1)&&(l.add({kind:re.Property,label:this.getLabelForValue(a),insertText:this.getInsertTextForProperty(a,void 0,!1,m),insertTextFormat:oe.Snippet,documentation:""}),l.setAsIncomplete())}const m={};return t?this.getValueCompletions(t,n,s,i,e,l,m):this.getSchemaLessValueCompletions(n,s,i,e,l),this.contributions.length>0&&this.getContributedValueCompletions(n,s,i,e,l,d),this.promiseConstructor.all(d).then((()=>{if(0===l.getNumberOfProposals()){let t=i;!s||"string"!==s.type&&"number"!==s.type&&"boolean"!==s.type&&"null"!==s.type||(t=s.offset+s.length);const n=this.evaluateSeparatorAfter(e,t);this.addFillerValueCompletions(m,n,l)}return r}))}))}getPropertyCompletions(e,t,n,r,o,i){t.getMatchingSchemas(e.schema,n.offset).forEach((e=>{if(e.node===n&&!e.inverted){const t=e.schema.properties;t&&Object.keys(t).forEach((e=>{const n=t[e];if("object"==typeof n&&!n.deprecationMessage&&!n.doNotSuggest){const t={kind:re.Property,label:e,insertText:this.getInsertTextForProperty(e,n,r,o),insertTextFormat:oe.Snippet,filterText:this.getFilterTextForValue(e),documentation:this.fromMarkup(n.markdownDescription)||n.description||""};void 0!==n.completionDetail&&(t.detail=n.completionDetail),void 0!==n.suggestSortText&&(t.sortText=n.suggestSortText),t.insertText&&it(t.insertText,`$1${o}`)&&(t.command={title:"Suggest",command:"editor.action.triggerSuggest"}),i.add(t)}}));const n=e.schema.propertyNames;if("object"==typeof n&&!n.deprecationMessage&&!n.doNotSuggest){const e=(e,t,s,a)=>{const c={kind:re.Property,label:e,insertText:this.getInsertTextForProperty(e,void 0,r,o),insertTextFormat:oe.Snippet,filterText:this.getFilterTextForValue(e),documentation:t||this.fromMarkup(n.markdownDescription)||n.description||"",sortText:a,detail:s};c.insertText&&it(c.insertText,`$1${o}`)&&(c.command={title:"Suggest",command:"editor.action.triggerSuggest"}),i.add(c)};if(n.enum)for(let t=0;t<n.enum.length;t++){let r;n.markdownEnumDescriptions&&t<n.markdownEnumDescriptions.length?r=this.fromMarkup(n.markdownEnumDescriptions[t]):n.enumDescriptions&&t<n.enumDescriptions.length&&(r=n.enumDescriptions[t]);const o=n.enumSortTexts?.[t],i=n.enumDetails?.[t];e(n.enum[t],r,i,o)}n.const&&e(n.const,void 0,n.completionDetail,n.suggestSortText)}}}))}getSchemaLessPropertyCompletions(e,t,n,r){const o=e=>{e.properties.forEach((e=>{const t=e.keyNode.value;r.add({kind:re.Property,label:t,insertText:this.getInsertTextForValue(t,""),insertTextFormat:oe.Snippet,filterText:this.getFilterTextForValue(t),documentation:""})}))};if(t.parent)if("property"===t.parent.type){const n=t.parent.keyNode.value;e.visit((e=>("property"===e.type&&e!==t.parent&&e.keyNode.value===n&&e.valueNode&&"object"===e.valueNode.type&&o(e.valueNode),!0)))}else"array"===t.parent.type&&t.parent.items.forEach((e=>{"object"===e.type&&e!==t&&o(e)}));else"object"===t.type&&r.add({kind:re.Property,label:"$schema",insertText:this.getInsertTextForProperty("$schema",void 0,!0,""),insertTextFormat:oe.Snippet,documentation:"",filterText:this.getFilterTextForValue("$schema")})}getSchemaLessValueCompletions(e,t,n,r,o){let i=n;if(!t||"string"!==t.type&&"number"!==t.type&&"boolean"!==t.type&&"null"!==t.type||(i=t.offset+t.length,t=t.parent),!t)return o.add({kind:this.getSuggestionKind("object"),label:"Empty object",insertText:this.getInsertTextForValue({},""),insertTextFormat:oe.Snippet,documentation:""}),void o.add({kind:this.getSuggestionKind("array"),label:"Empty array",insertText:this.getInsertTextForValue([],""),insertTextFormat:oe.Snippet,documentation:""});const s=this.evaluateSeparatorAfter(r,i),a=e=>{e.parent&&!Ft(e.parent,n,!0)&&o.add({kind:this.getSuggestionKind(e.type),label:this.getLabelTextForMatchingNode(e,r),insertText:this.getInsertTextForMatchingNode(e,r,s),insertTextFormat:oe.Snippet,documentation:""}),"boolean"===e.type&&this.addBooleanValueCompletion(!e.value,s,o)};if("property"===t.type&&n>(t.colonOffset||0)){const r=t.valueNode;if(r&&(n>r.offset+r.length||"object"===r.type||"array"===r.type))return;const i=t.keyNode.value;e.visit((e=>("property"===e.type&&e.keyNode.value===i&&e.valueNode&&a(e.valueNode),!0))),"$schema"===i&&t.parent&&!t.parent.parent&&this.addDollarSchemaCompletions(s,o)}if("array"===t.type)if(t.parent&&"property"===t.parent.type){const n=t.parent.keyNode.value;e.visit((e=>("property"===e.type&&e.keyNode.value===n&&e.valueNode&&"array"===e.valueNode.type&&e.valueNode.items.forEach(a),!0)))}else t.items.forEach(a)}getValueCompletions(e,t,n,r,o,i,s){let a,c,u=r;if(!n||"string"!==n.type&&"number"!==n.type&&"boolean"!==n.type&&"null"!==n.type||(u=n.offset+n.length,c=n,n=n.parent),n){if("property"===n.type&&r>(n.colonOffset||0)){const e=n.valueNode;if(e&&r>e.offset+e.length)return;a=n.keyNode.value,n=n.parent}if(n&&(void 0!==a||"array"===n.type)){const l=this.evaluateSeparatorAfter(o,u),d=t.getMatchingSchemas(e.schema,n.offset,c);for(const e of d)if(e.node===n&&!e.inverted&&e.schema){if("array"===n.type&&e.schema.items){let t=i;if(e.schema.uniqueItems){const e=new Set;n.children.forEach((t=>{"array"!==t.type&&"object"!==t.type&&e.add(this.getLabelForValue(It(t)))})),t={...i,add(t){e.has(t.label)||i.add(t)}}}if(Array.isArray(e.schema.items)){const i=this.findItemAtOffset(n,o,r);i<e.schema.items.length&&this.addSchemaValueCompletions(e.schema.items[i],l,t,s)}else this.addSchemaValueCompletions(e.schema.items,l,t,s)}if(void 0!==a){let t=!1;if(e.schema.properties){const n=e.schema.properties[a];n&&(t=!0,this.addSchemaValueCompletions(n,l,i,s))}if(e.schema.patternProperties&&!t)for(const n of Object.keys(e.schema.patternProperties)){const r=st(n);if(r?.test(a)){t=!0;const r=e.schema.patternProperties[n];this.addSchemaValueCompletions(r,l,i,s)}}if(e.schema.additionalProperties&&!t){const t=e.schema.additionalProperties;this.addSchemaValueCompletions(t,l,i,s)}}}"$schema"!==a||n.parent||this.addDollarSchemaCompletions(l,i),s.boolean&&(this.addBooleanValueCompletion(!0,l,i),this.addBooleanValueCompletion(!1,l,i)),s.null&&this.addNullValueCompletion(l,i)}}else this.addSchemaValueCompletions(e.schema,"",i,s)}getContributedValueCompletions(e,t,n,r,o,i){if(t){if("string"!==t.type&&"number"!==t.type&&"boolean"!==t.type&&"null"!==t.type||(t=t.parent),t&&"property"===t.type&&n>(t.colonOffset||0)){const e=t.keyNode.value,s=t.valueNode;if((!s||n<=s.offset+s.length)&&t.parent){const n=Lt(t.parent);this.contributions.forEach((t=>{const s=t.collectValueCompletions(r.uri,n,e,o);s&&i.push(s)}))}}}else this.contributions.forEach((e=>{const t=e.collectDefaultCompletions(r.uri,o);t&&i.push(t)}))}addSchemaValueCompletions(e,t,n,r){"object"==typeof e&&(this.addEnumValueCompletions(e,t,n),this.addDefaultValueCompletions(e,t,n),this.collectTypes(e,r),Array.isArray(e.allOf)&&e.allOf.forEach((e=>this.addSchemaValueCompletions(e,t,n,r))),Array.isArray(e.anyOf)&&e.anyOf.forEach((e=>this.addSchemaValueCompletions(e,t,n,r))),Array.isArray(e.oneOf)&&e.oneOf.forEach((e=>this.addSchemaValueCompletions(e,t,n,r))))}addDefaultValueCompletions(e,t,n,r=0){let o=!1;if(tt(e.default)){let i=e.type,s=e.default;for(let e=r;e>0;e--)s=[s],i="array";const a={kind:this.getSuggestionKind(i),label:this.getLabelForValue(s),insertText:this.getInsertTextForValue(s,t),insertTextFormat:oe.Snippet};this.doesSupportsLabelDetails()?a.labelDetails={description:gt.t("Default value")}:a.detail=gt.t("Default value"),n.add(a),o=!0}Array.isArray(e.examples)&&e.examples.forEach((i=>{let s=e.type,a=i;for(let e=r;e>0;e--)a=[a],s="array";n.add({kind:this.getSuggestionKind(s),label:this.getLabelForValue(a),insertText:this.getInsertTextForValue(a,t),insertTextFormat:oe.Snippet}),o=!0})),Array.isArray(e.defaultSnippets)&&e.defaultSnippets.forEach((i=>{let s,a,c=e.type,u=i.body,l=i.label;if(tt(u)){let n=e.type;for(let e=r;e>0;e--)u=[u],n="array";s=this.getInsertTextForSnippetValue(u,t),a=this.getFilterTextForSnippetValue(u),l=l||this.getLabelForSnippetValue(u)}else{if("string"!=typeof i.bodyText)return;{let e="",n="",o="";for(let t=r;t>0;t--)e=e+o+"[\n",n=n+"\n"+o+"]",o+="\t",c="array";s=e+o+i.bodyText.split("\n").join("\n"+o)+n+t,l=l||s,a=s.replace(/[\n]/g,"")}}n.add({kind:this.getSuggestionKind(c),label:l,documentation:this.fromMarkup(i.markdownDescription)||i.description,insertText:s,insertTextFormat:oe.Snippet,filterText:a}),o=!0})),!o&&"object"==typeof e.items&&!Array.isArray(e.items)&&r<5&&this.addDefaultValueCompletions(e.items,t,n,r+1)}addEnumValueCompletions(e,t,n){if(tt(e.const)&&n.add({kind:this.getSuggestionKind(e.type),label:this.getLabelForValue(e.const),insertText:this.getInsertTextForValue(e.const,t),insertTextFormat:oe.Snippet,documentation:this.fromMarkup(e.markdownDescription)||e.description}),Array.isArray(e.enum))for(let r=0,o=e.enum.length;r<o;r++){const o=e.enum[r];let i=this.fromMarkup(e.markdownDescription)||e.description;e.markdownEnumDescriptions&&r<e.markdownEnumDescriptions.length&&this.doesSupportMarkdown()?i=this.fromMarkup(e.markdownEnumDescriptions[r]):e.enumDescriptions&&r<e.enumDescriptions.length&&(i=e.enumDescriptions[r]),n.add({kind:this.getSuggestionKind(e.type),label:this.getLabelForValue(o),insertText:this.getInsertTextForValue(o,t),insertTextFormat:oe.Snippet,sortText:e.enumSortTexts?.[r],detail:e.enumDetails?.[r],documentation:i})}}collectTypes(e,t){if(Array.isArray(e.enum)||tt(e.const))return;const n=e.type;Array.isArray(n)?n.forEach((e=>t[e]=!0)):n&&(t[n]=!0)}addFillerValueCompletions(e,t,n){e.object&&n.add({kind:this.getSuggestionKind("object"),label:"{}",insertText:this.getInsertTextForGuessedValue({},t),insertTextFormat:oe.Snippet,detail:gt.t("New object"),documentation:""}),e.array&&n.add({kind:this.getSuggestionKind("array"),label:"[]",insertText:this.getInsertTextForGuessedValue([],t),insertTextFormat:oe.Snippet,detail:gt.t("New array"),documentation:""})}addBooleanValueCompletion(e,t,n){n.add({kind:this.getSuggestionKind("boolean"),label:e?"true":"false",insertText:this.getInsertTextForValue(e,t),insertTextFormat:oe.Snippet,documentation:""})}addNullValueCompletion(e,t){t.add({kind:this.getSuggestionKind("null"),label:"null",insertText:"null"+e,insertTextFormat:oe.Snippet,documentation:""})}addDollarSchemaCompletions(e,t){this.schemaService.getRegisteredSchemaIds((e=>"http"===e||"https"===e)).forEach((n=>{n.startsWith("https://json-schema.org/draft-")&&(n+="#"),t.add({kind:re.Module,label:this.getLabelForValue(n),filterText:this.getFilterTextForValue(n),insertText:this.getInsertTextForValue(n,e),insertTextFormat:oe.Snippet,documentation:""})}))}getLabelForValue(e){return JSON.stringify(e)}getValueFromLabel(e){return JSON.parse(e)}getFilterTextForValue(e){return JSON.stringify(e)}getFilterTextForSnippetValue(e){return JSON.stringify(e).replace(/\$\{\d+:([^}]+)\}|\$\d+/g,"$1")}getLabelForSnippetValue(e){return JSON.stringify(e).replace(/\$\{\d+:([^}]+)\}|\$\d+/g,"$1")}getInsertTextForPlainText(e){return e.replace(/[\\\$\}]/g,"\\$&")}getInsertTextForValue(e,t){const n=JSON.stringify(e,null,"\t");return"{}"===n?"{$1}"+t:"[]"===n?"[$1]"+t:this.getInsertTextForPlainText(n+t)}getInsertTextForSnippetValue(e,t){return Ut(e,"",(e=>"string"==typeof e&&"^"===e[0]?e.substr(1):JSON.stringify(e)))+t}getInsertTextForGuessedValue(e,t){switch(typeof e){case"object":return null===e?"${1:null}"+t:this.getInsertTextForValue(e,t);case"string":let n=JSON.stringify(e);return n=n.substr(1,n.length-2),n=this.getInsertTextForPlainText(n),'"${1:'+n+'}"'+t;case"number":case"boolean":return"${1:"+JSON.stringify(e)+"}"+t}return this.getInsertTextForValue(e,t)}getSuggestionKind(e){if(Array.isArray(e)){const t=e;e=t.length>0?t[0]:void 0}if(!e)return re.Value;switch(e){case"string":default:return re.Value;case"object":return re.Module;case"property":return re.Property}}getLabelTextForMatchingNode(e,t){switch(e.type){case"array":return"[]";case"object":return"{}";default:return t.getText().substr(e.offset,e.length)}}getInsertTextForMatchingNode(e,t,n){switch(e.type){case"array":return this.getInsertTextForValue([],n);case"object":return this.getInsertTextForValue({},n);default:const r=t.getText().substr(e.offset,e.length)+n;return this.getInsertTextForPlainText(r)}}getInsertTextForProperty(e,t,n,r){const o=this.getInsertTextForValue(e,"");if(!n)return o;const i=o+": ";let s,a=0;if(t){if(Array.isArray(t.defaultSnippets)){if(1===t.defaultSnippets.length){const e=t.defaultSnippets[0].body;tt(e)&&(s=this.getInsertTextForSnippetValue(e,""))}a+=t.defaultSnippets.length}if(t.enum&&(s||1!==t.enum.length||(s=this.getInsertTextForGuessedValue(t.enum[0],"")),a+=t.enum.length),tt(t.const)&&(s||(s=this.getInsertTextForGuessedValue(t.const,"")),a++),tt(t.default)&&(s||(s=this.getInsertTextForGuessedValue(t.default,"")),a++),Array.isArray(t.examples)&&t.examples.length&&(s||(s=this.getInsertTextForGuessedValue(t.examples[0],"")),a+=t.examples.length),0===a){let e=Array.isArray(t.type)?t.type[0]:t.type;switch(e||(t.properties?e="object":t.items&&(e="array")),e){case"boolean":s="$1";break;case"string":s='"$1"';break;case"object":s="{$1}";break;case"array":s="[$1]";break;case"number":case"integer":s="${1:0}";break;case"null":s="${1:null}";break;default:return o}}}return(!s||a>1)&&(s="$1"),i+s+r}getCurrentWord(e,t){let n=t-1;const r=e.getText();for(;n>=0&&-1===' \t\n\r\v":{[,]}'.indexOf(r.charAt(n));)n--;return r.substring(n+1,t)}evaluateSeparatorAfter(e,t){const n=m(e.getText(),!0);switch(n.setPosition(t),n.scan()){case 5:case 2:case 4:case 17:return"";default:return","}}findItemAtOffset(e,t,n){const r=m(t.getText(),!0),o=e.items;for(let e=o.length-1;e>=0;e--){const t=o[e];if(n>t.offset+t.length)return r.setPosition(t.offset+t.length),5===r.scan()&&n>=r.getTokenOffset()+r.getTokenLength()?e+1:e;if(n>=t.offset)return e}return 0}isInComment(e,t,n){const r=m(e.getText(),!1);r.setPosition(t);let o=r.scan();for(;17!==o&&r.getTokenOffset()+r.getTokenLength()<n;)o=r.scan();return(12===o||13===o)&&r.getTokenOffset()<=n}fromMarkup(e){if(e&&this.doesSupportMarkdown())return{kind:te.Markdown,value:e}}doesSupportMarkdown(){if(!tt(this.supportsMarkdown)){const e=this.clientCapabilities.textDocument?.completion?.completionItem?.documentationFormat;this.supportsMarkdown=Array.isArray(e)&&-1!==e.indexOf(te.Markdown)}return this.supportsMarkdown}doesSupportsCommitCharacters(){return tt(this.supportsCommitCharacters)||(this.labelDetailsSupport=this.clientCapabilities.textDocument?.completion?.completionItem?.commitCharactersSupport),this.supportsCommitCharacters}doesSupportsLabelDetails(){return tt(this.labelDetailsSupport)||(this.labelDetailsSupport=this.clientCapabilities.textDocument?.completion?.completionItem?.labelDetailsSupport),this.labelDetailsSupport}}class Bt{constructor(e,t=[],n){this.schemaService=e,this.contributions=t,this.promise=n||Promise}doHover(e,t,n){const r=e.offsetAt(t);let o=n.getNodeFromOffset(r);if(!o||("object"===o.type||"array"===o.type)&&r>o.offset+1&&r<o.offset+o.length-1)return this.promise.resolve(null);const i=o;if("string"===o.type){const e=o.parent;if(e&&"property"===e.type&&e.keyNode===o&&(o=e.valueNode,!o))return this.promise.resolve(null)}const s=x.create(e.positionAt(i.offset),e.positionAt(i.offset+i.length)),a=e=>({contents:e,range:s}),c=Lt(o);for(let t=this.contributions.length-1;t>=0;t--){const n=this.contributions[t].getInfoContribution(e.uri,c);if(n)return n.then((e=>a(e)))}return this.schemaService.getSchemaForResource(e.uri,n).then((e=>{if(e&&o){let r,i,s,c;n.getMatchingSchemas(e.schema,o.offset).every((e=>{if(e.node===o&&!e.inverted&&e.schema&&(r=r||e.schema.title,i=i||e.schema.markdownDescription||zt(e.schema.description),e.schema.enum)){const t=e.schema.enum.indexOf(It(o));e.schema.markdownEnumDescriptions?s=e.schema.markdownEnumDescriptions[t]:e.schema.enumDescriptions&&(s=zt(e.schema.enumDescriptions[t])),s&&(c=e.schema.enum[t],"string"!=typeof c&&(c=JSON.stringify(c)))}return!0}));let u="";return r&&(u=zt(r)),i&&(u.length>0&&(u+="\n\n"),u+=i),s&&(u.length>0&&(u+="\n\n"),u+=`\`${t=c,-1!==t.indexOf("`")?"`` "+t+" ``":t}\`: ${s}`),a([u])}var t;return null}))}}function zt(e){if(e)return e.replace(/([^\n\r])(\r?\n)([^\n\r])/gm,"$1\n\n$3").replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}class Kt{constructor(e,t){this.jsonSchemaService=e,this.promise=t,this.validationEnabled=!0}configure(e){e&&(this.validationEnabled=!1!==e.validate,this.commentSeverity=e.allowComments?void 0:I.Error)}doValidation(e,t,n,r){if(!this.validationEnabled)return this.promise.resolve([]);const o=[],i={},s=e=>{const t=e.range.start.line+" "+e.range.start.character+" "+e.message;i[t]||(i[t]=!0,o.push(e))},a=r=>{let i=n?.trailingCommas?Zt(n.trailingCommas):I.Error,a=n?.comments?Zt(n.comments):this.commentSeverity,c=n?.schemaValidation?Zt(n.schemaValidation):I.Warning,u=n?.schemaRequest?Zt(n.schemaRequest):I.Warning;if(r){const o=(n,r)=>{if(t.root&&u){const o=t.root,i="object"===o.type?o.properties[0]:void 0;if(i&&"$schema"===i.keyNode.value){const t=i.valueNode||i,o=x.create(e.positionAt(t.offset),e.positionAt(t.offset+t.length));s($.create(o,n,u,r))}else{const t=x.create(e.positionAt(o.offset),e.positionAt(o.offset+1));s($.create(t,n,u,r))}}};if(r.errors.length)o(r.errors[0],Xe.SchemaResolveError);else if(c){for(const e of r.warnings)o(e,Xe.SchemaUnsupportedFeature);const i=t.validate(e,r.schema,c,n?.schemaDraft);i&&i.forEach(s)}Gt(r.schema)&&(a=void 0),Xt(r.schema)&&(i=void 0)}for(const e of t.syntaxErrors){if(e.code===Xe.TrailingComma){if("number"!=typeof i)continue;e.severity=i}s(e)}if("number"==typeof a){const e=gt.t("Comments are not permitted in JSON.");t.comments.forEach((t=>{s($.create(t,e,a,Xe.CommentNotPermitted))}))}return o};if(r){const e=r.id||"schemaservice://untitled/"+Jt++;return this.jsonSchemaService.registerExternalSchema({uri:e,schema:r}).getResolvedSchema().then((e=>a(e)))}return this.jsonSchemaService.getSchemaForResource(e.uri,t).then((e=>a(e)))}getLanguageStatus(e,t){return{schemas:this.jsonSchemaService.getSchemaURIsForResource(e.uri,t)}}}let Jt=0;function Gt(e){if(e&&"object"==typeof e){if(nt(e.allowComments))return e.allowComments;if(e.allOf)for(const t of e.allOf){const e=Gt(t);if(nt(e))return e}}}function Xt(e){if(e&&"object"==typeof e){if(nt(e.allowTrailingCommas))return e.allowTrailingCommas;const t=e;if(nt(t.allowsTrailingCommas))return t.allowsTrailingCommas;if(e.allOf)for(const t of e.allOf){const e=Xt(t);if(nt(e))return e}}}function Zt(e){switch(e){case"error":return I.Error;case"warning":return I.Warning;case"ignore":return}}const Yt=48,Qt=57,en=65,tn=97,nn=102;function rn(e){return e<Yt?0:e<=Qt?e-Yt:(e<tn&&(e+=tn-en),e>=tn&&e<=nn?e-tn+10:0)}function on(e){if("#"===e[0])switch(e.length){case 4:return{red:17*rn(e.charCodeAt(1))/255,green:17*rn(e.charCodeAt(2))/255,blue:17*rn(e.charCodeAt(3))/255,alpha:1};case 5:return{red:17*rn(e.charCodeAt(1))/255,green:17*rn(e.charCodeAt(2))/255,blue:17*rn(e.charCodeAt(3))/255,alpha:17*rn(e.charCodeAt(4))/255};case 7:return{red:(16*rn(e.charCodeAt(1))+rn(e.charCodeAt(2)))/255,green:(16*rn(e.charCodeAt(3))+rn(e.charCodeAt(4)))/255,blue:(16*rn(e.charCodeAt(5))+rn(e.charCodeAt(6)))/255,alpha:1};case 9:return{red:(16*rn(e.charCodeAt(1))+rn(e.charCodeAt(2)))/255,green:(16*rn(e.charCodeAt(3))+rn(e.charCodeAt(4)))/255,blue:(16*rn(e.charCodeAt(5))+rn(e.charCodeAt(6)))/255,alpha:(16*rn(e.charCodeAt(7))+rn(e.charCodeAt(8)))/255}}}class sn{constructor(e){this.schemaService=e}findDocumentSymbols(e,t,n={resultLimit:Number.MAX_VALUE}){const r=t.root;if(!r)return[];let o=n.resultLimit||Number.MAX_VALUE;const i=e.uri;if(("vscode://defaultsettings/keybindings.json"===i||it(i.toLowerCase(),"/user/keybindings.json"))&&"array"===r.type){const t=[];for(const s of r.items)if("object"===s.type)for(const r of s.properties)if("key"===r.keyNode.value&&r.valueNode){const a=P.create(e.uri,an(e,s));if(t.push({name:cn(r.valueNode),kind:ye.Function,location:a}),o--,o<=0)return n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(i),t}return t}const s=[{node:r,containerName:""}];let a=0,c=!1;const u=[],l=(t,n)=>{"array"===t.type?t.items.forEach((e=>{e&&s.push({node:e,containerName:n})})):"object"===t.type&&t.properties.forEach((t=>{const r=t.valueNode;if(r)if(o>0){o--;const i=P.create(e.uri,an(e,t)),a=n?n+"."+t.keyNode.value:t.keyNode.value;u.push({name:this.getKeyLabel(t),kind:this.getSymbolKind(r.type),location:i,containerName:n}),s.push({node:r,containerName:a})}else c=!0}))};for(;a<s.length;){const e=s[a++];l(e.node,e.containerName)}return c&&n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(i),u}findDocumentSymbols2(e,t,n={resultLimit:Number.MAX_VALUE}){const r=t.root;if(!r)return[];let o=n.resultLimit||Number.MAX_VALUE;const i=e.uri;if(("vscode://defaultsettings/keybindings.json"===i||it(i.toLowerCase(),"/user/keybindings.json"))&&"array"===r.type){const t=[];for(const s of r.items)if("object"===s.type)for(const r of s.properties)if("key"===r.keyNode.value&&r.valueNode){const a=an(e,s),c=an(e,r.keyNode);if(t.push({name:cn(r.valueNode),kind:ye.Function,range:a,selectionRange:c}),o--,o<=0)return n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(i),t}return t}const s=[],a=[{node:r,result:s}];let c=0,u=!1;const l=(t,n)=>{"array"===t.type?t.items.forEach(((t,r)=>{if(t)if(o>0){o--;const i=an(e,t),s=i,c={name:String(r),kind:this.getSymbolKind(t.type),range:i,selectionRange:s,children:[]};n.push(c),a.push({result:c.children,node:t})}else u=!0})):"object"===t.type&&t.properties.forEach((t=>{const r=t.valueNode;if(r)if(o>0){o--;const i=an(e,t),s=an(e,t.keyNode),c=[],u={name:this.getKeyLabel(t),kind:this.getSymbolKind(r.type),range:i,selectionRange:s,children:c,detail:this.getDetail(r)};n.push(u),a.push({result:c,node:r})}else u=!0}))};for(;c<a.length;){const e=a[c++];l(e.node,e.result)}return u&&n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(i),s}getSymbolKind(e){switch(e){case"object":return ye.Module;case"string":return ye.String;case"number":return ye.Number;case"array":return ye.Array;case"boolean":return ye.Boolean;default:return ye.Variable}}getKeyLabel(e){let t=e.keyNode.value;return t&&(t=t.replace(/[\n]/g,"↵")),t&&t.trim()?t:`"${t}"`}getDetail(e){if(e)return"boolean"===e.type||"number"===e.type||"null"===e.type||"string"===e.type?String(e.value):"array"===e.type?e.children.length?void 0:"[]":"object"===e.type?e.children.length?void 0:"{}":void 0}findDocumentColors(e,t,n){return this.schemaService.getSchemaForResource(e.uri,t).then((r=>{const o=[];if(r){let i=n&&"number"==typeof n.resultLimit?n.resultLimit:Number.MAX_VALUE;const s=t.getMatchingSchemas(r.schema),a={};for(const t of s)if(!t.inverted&&t.schema&&("color"===t.schema.format||"color-hex"===t.schema.format)&&t.node&&"string"===t.node.type){const r=String(t.node.offset);if(!a[r]){const s=on(It(t.node));if(s){const n=an(e,t.node);o.push({color:s,range:n})}if(a[r]=!0,i--,i<=0)return n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(e.uri),o}}}return o}))}getColorPresentations(e,t,n,r){const o=[],i=Math.round(255*n.red),s=Math.round(255*n.green),a=Math.round(255*n.blue);function c(e){const t=e.toString(16);return 2!==t.length?"0"+t:t}let u;return u=1===n.alpha?`#${c(i)}${c(s)}${c(a)}`:`#${c(i)}${c(s)}${c(a)}${c(Math.round(255*n.alpha))}`,o.push({label:u,textEdit:V.replace(r,JSON.stringify(u))}),o}}function an(e,t){return x.create(e.positionAt(t.offset),e.positionAt(t.offset+t.length))}function cn(e){return It(e)||gt.t("<empty>")}const un={schemaAssociations:[],schemas:{"https://json-schema.org/draft-04/schema":{definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},positiveInteger:{type:"integer",minimum:0},positiveIntegerDefault0:{allOf:[{$ref:"#/definitions/positiveInteger"},{default:0}]},simpleTypes:{type:"string",enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},minItems:1,uniqueItems:!0}},type:"object",properties:{id:{type:"string",format:"uri"},$schema:{type:"string",format:"uri"},title:{type:"string"},description:{type:"string"},default:{},multipleOf:{type:"number",minimum:0,exclusiveMinimum:!0},maximum:{type:"number"},exclusiveMaximum:{type:"boolean",default:!1},minimum:{type:"number"},exclusiveMinimum:{type:"boolean",default:!1},maxLength:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minLength:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},pattern:{type:"string",format:"regex"},additionalItems:{anyOf:[{type:"boolean"},{$ref:"#"}],default:{}},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:{}},maxItems:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minItems:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},uniqueItems:{type:"boolean",default:!1},maxProperties:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minProperties:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},required:{allOf:[{$ref:"#/definitions/stringArray"}]},additionalProperties:{anyOf:[{type:"boolean"},{$ref:"#"}],default:{}},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},enum:{type:"array",minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{anyOf:[{type:"string",enum:["date-time","uri","email","hostname","ipv4","ipv6","regex"]},{type:"string"}]},allOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},anyOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},oneOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},not:{allOf:[{$ref:"#"}]}},dependencies:{exclusiveMaximum:["maximum"],exclusiveMinimum:["minimum"]},default:{}},"https://json-schema.org/draft-07/schema":{definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},nonNegativeInteger:{type:"integer",minimum:0},nonNegativeIntegerDefault0:{allOf:[{$ref:"#/definitions/nonNegativeInteger"},{default:0}]},simpleTypes:{enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},uniqueItems:!0,default:[]}},type:["object","boolean"],properties:{$id:{type:"string",format:"uri-reference"},$schema:{type:"string",format:"uri"},$ref:{type:"string",format:"uri-reference"},$comment:{type:"string"},title:{type:"string"},description:{type:"string"},default:!0,readOnly:{type:"boolean",default:!1},examples:{type:"array",items:!0},multipleOf:{type:"number",exclusiveMinimum:0},maximum:{type:"number"},exclusiveMaximum:{type:"number"},minimum:{type:"number"},exclusiveMinimum:{type:"number"},maxLength:{$ref:"#/definitions/nonNegativeInteger"},minLength:{$ref:"#/definitions/nonNegativeIntegerDefault0"},pattern:{type:"string",format:"regex"},additionalItems:{$ref:"#"},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:!0},maxItems:{$ref:"#/definitions/nonNegativeInteger"},minItems:{$ref:"#/definitions/nonNegativeIntegerDefault0"},uniqueItems:{type:"boolean",default:!1},contains:{$ref:"#"},maxProperties:{$ref:"#/definitions/nonNegativeInteger"},minProperties:{$ref:"#/definitions/nonNegativeIntegerDefault0"},required:{$ref:"#/definitions/stringArray"},additionalProperties:{$ref:"#"},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},propertyNames:{format:"regex"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},propertyNames:{$ref:"#"},const:!0,enum:{type:"array",items:!0,minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{type:"string"},contentMediaType:{type:"string"},contentEncoding:{type:"string"},if:{$ref:"#"},then:{$ref:"#"},else:{$ref:"#"},allOf:{$ref:"#/definitions/schemaArray"},anyOf:{$ref:"#/definitions/schemaArray"},oneOf:{$ref:"#/definitions/schemaArray"},not:{$ref:"#"}},default:!0}}},ln={id:gt.t("A unique identifier for the schema."),$schema:gt.t("The schema to verify this document against."),title:gt.t("A descriptive title of the schema."),description:gt.t("A long description of the schema. Used in hover menus and suggestions."),default:gt.t("A default value. Used by suggestions."),multipleOf:gt.t("A number that should cleanly divide the current value (i.e. have no remainder)."),maximum:gt.t("The maximum numerical value, inclusive by default."),exclusiveMaximum:gt.t("Makes the maximum property exclusive."),minimum:gt.t("The minimum numerical value, inclusive by default."),exclusiveMinimum:gt.t("Makes the minimum property exclusive."),maxLength:gt.t("The maximum length of a string."),minLength:gt.t("The minimum length of a string."),pattern:gt.t("A regular expression to match the string against. It is not implicitly anchored."),additionalItems:gt.t("For arrays, only when items is set as an array. If items are a schema, this schema validates items after the ones specified by the items schema. If false, additional items will cause validation to fail."),items:gt.t("For arrays. Can either be a schema to validate every element against or an array of schemas to validate each item against in order (the first schema will validate the first element, the second schema will validate the second element, and so on."),maxItems:gt.t("The maximum number of items that can be inside an array. Inclusive."),minItems:gt.t("The minimum number of items that can be inside an array. Inclusive."),uniqueItems:gt.t("If all of the items in the array must be unique. Defaults to false."),maxProperties:gt.t("The maximum number of properties an object can have. Inclusive."),minProperties:gt.t("The minimum number of properties an object can have. Inclusive."),required:gt.t("An array of strings that lists the names of all properties required on this object."),additionalProperties:gt.t("Either a schema or a boolean. If a schema, used to validate all properties not matched by 'properties', 'propertyNames', or 'patternProperties'. If false, any properties not defined by the adajacent keywords will cause this schema to fail."),definitions:gt.t("Not used for validation. Place subschemas here that you wish to reference inline with $ref."),properties:gt.t("A map of property names to schemas for each property."),patternProperties:gt.t("A map of regular expressions on property names to schemas for matching properties."),dependencies:gt.t("A map of property names to either an array of property names or a schema. An array of property names means the property named in the key depends on the properties in the array being present in the object in order to be valid. If the value is a schema, then the schema is only applied to the object if the property in the key exists on the object."),enum:gt.t("The set of literal values that are valid."),type:gt.t("Either a string of one of the basic schema types (number, integer, null, array, object, boolean, string) or an array of strings specifying a subset of those types."),format:gt.t("Describes the format expected for the value. By default, not used for validation"),allOf:gt.t("An array of schemas, all of which must match."),anyOf:gt.t("An array of schemas, where at least one must match."),oneOf:gt.t("An array of schemas, exactly one of which must match."),not:gt.t("A schema which must not match."),$id:gt.t("A unique identifier for the schema."),$ref:gt.t("Reference a definition hosted on any location."),$comment:gt.t("Comments from schema authors to readers or maintainers of the schema."),readOnly:gt.t("Indicates that the value of the instance is managed exclusively by the owning authority."),examples:gt.t("Sample JSON values associated with a particular schema, for the purpose of illustrating usage."),contains:gt.t('An array instance is valid against "contains" if at least one of its elements is valid against the given schema.'),propertyNames:gt.t("If the instance is an object, this keyword validates if every property name in the instance validates against the provided schema."),const:gt.t("An instance validates successfully against this keyword if its value is equal to the value of the keyword."),contentMediaType:gt.t("Describes the media type of a string property."),contentEncoding:gt.t("Describes the content encoding of a string property."),if:gt.t('The validation outcome of the "if" subschema controls which of the "then" or "else" keywords are evaluated.'),then:gt.t('The "then" subschema is used for validation when the "if" subschema succeeds.'),else:gt.t('The "else" subschema is used for validation when the "if" subschema fails.')};for(const e in un.schemas){const t=un.schemas[e];for(const e in t.properties){let n=t.properties[e];"boolean"==typeof n&&(n=t.properties[e]={});const r=ln[e];r&&(n.description=r)}}function dn(e,t){if("string"!=typeof e)throw new TypeError("Expected a string");const n=String(e);let r="";const o=!!t&&!!t.extended,i=!!t&&!!t.globstar;let s=!1;const a=t&&"string"==typeof t.flags?t.flags:"";let c;for(let e=0,t=n.length;e<t;e++)switch(c=n[e],c){case"/":case"$":case"^":case"+":case".":case"(":case")":case"=":case"!":case"|":r+="\\"+c;break;case"?":if(o){r+=".";break}case"[":case"]":if(o){r+=c;break}case"{":if(o){s=!0,r+="(";break}case"}":if(o){s=!1,r+=")";break}case",":if(s){r+="|";break}r+="\\"+c;break;case"*":const t=n[e-1];let a=1;for(;"*"===n[e+1];)a++,e++;const u=n[e+1];i?!(a>1)||"/"!==t&&void 0!==t&&"{"!==t&&","!==t||"/"!==u&&void 0!==u&&","!==u&&"}"!==u?r+="([^/]*)":("/"===u?e++:"/"===t&&r.endsWith("\\/")&&(r=r.substr(0,r.length-2)),r+="((?:[^/]*(?:/|$))*)"):r+=".*";break;default:r+=c}return a&&~a.indexOf("g")||(r="^"+r+"$"),new RegExp(r,a)}class fn{constructor(e,t,n){this.folderUri=t,this.uris=n,this.globWrappers=[];try{for(let t of e){const e="!"!==t[0];e||(t=t.substring(1)),t.length>0&&("/"===t[0]&&(t=t.substring(1)),this.globWrappers.push({regexp:dn("**/"+t,{extended:!0,globstar:!0}),include:e}))}t&&((t=bn(t)).endsWith("/")||(t+="/"),this.folderUri=t)}catch(e){this.globWrappers.length=0,this.uris=[]}}matchesPattern(e){if(this.folderUri&&!e.startsWith(this.folderUri))return!1;let t=!1;for(const{regexp:n,include:r}of this.globWrappers)n.test(e)&&(t=r);return t}getURIs(){return this.uris}}class hn{constructor(e,t,n){this.service=e,this.uri=t,this.dependencies=new Set,this.anchors=void 0,n&&(this.unresolvedSchema=this.service.promise.resolve(new pn(n)))}getUnresolvedSchema(){return this.unresolvedSchema||(this.unresolvedSchema=this.service.loadSchema(this.uri)),this.unresolvedSchema}getResolvedSchema(){return this.resolvedSchema||(this.resolvedSchema=this.getUnresolvedSchema().then((e=>this.service.resolveSchemaContent(e,this)))),this.resolvedSchema}clearSchema(){const e=!!this.unresolvedSchema;return this.resolvedSchema=void 0,this.unresolvedSchema=void 0,this.dependencies.clear(),this.anchors=void 0,e}}class pn{constructor(e,t=[]){this.schema=e,this.errors=t}}class mn{constructor(e,t=[],n=[],r){this.schema=e,this.errors=t,this.warnings=n,this.schemaDraft=r}getSection(e){const t=this.getSectionRecursive(e,this.schema);if(t)return Dt(t)}getSectionRecursive(e,t){if(!t||"boolean"==typeof t||0===e.length)return t;const n=e.shift();if(t.properties&&(t.properties[n],1))return this.getSectionRecursive(e,t.properties[n]);if(t.patternProperties)for(const r of Object.keys(t.patternProperties)){const o=st(r);if(o?.test(n))return this.getSectionRecursive(e,t.patternProperties[r])}else{if("object"==typeof t.additionalProperties)return this.getSectionRecursive(e,t.additionalProperties);if(n.match("[0-9]+"))if(Array.isArray(t.items)){const r=parseInt(n,10);if(!isNaN(r)&&t.items[r])return this.getSectionRecursive(e,t.items[r])}else if(t.items)return this.getSectionRecursive(e,t.items)}}}class gn{constructor(e,t,n){this.contextService=t,this.requestService=e,this.promiseConstructor=n||Promise,this.callOnDispose=[],this.contributionSchemas={},this.contributionAssociations=[],this.schemasById={},this.filePatternAssociations=[],this.registeredSchemasIds={}}getRegisteredSchemaIds(e){return Object.keys(this.registeredSchemasIds).filter((t=>{const n=mt.URI.parse(t).scheme;return"schemaservice"!==n&&(!e||e(n))}))}get promise(){return this.promiseConstructor}dispose(){for(;this.callOnDispose.length>0;)this.callOnDispose.pop()()}onResourceChange(e){this.cachedSchemaForResource=void 0;let t=!1;const n=[e=Ot(e)],r=Object.keys(this.schemasById).map((e=>this.schemasById[e]));for(;n.length;){const e=n.pop();for(let o=0;o<r.length;o++){const i=r[o];i&&(i.uri===e||i.dependencies.has(e))&&(i.uri!==e&&n.push(i.uri),i.clearSchema()&&(t=!0),r[o]=void 0)}}return t}setSchemaContributions(e){if(e.schemas){const t=e.schemas;for(const e in t){const n=Ot(e);this.contributionSchemas[n]=this.addSchemaHandle(n,t[e])}}if(Array.isArray(e.schemaAssociations)){const t=e.schemaAssociations;for(let e of t){const t=e.uris.map(Ot),n=this.addFilePatternAssociation(e.pattern,e.folderUri,t);this.contributionAssociations.push(n)}}}addSchemaHandle(e,t){const n=new hn(this,e,t);return this.schemasById[e]=n,n}getOrAddSchemaHandle(e,t){return this.schemasById[e]||this.addSchemaHandle(e,t)}addFilePatternAssociation(e,t,n){const r=new fn(e,t,n);return this.filePatternAssociations.push(r),r}registerExternalSchema(e){const t=Ot(e.uri);return this.registeredSchemasIds[t]=!0,this.cachedSchemaForResource=void 0,e.fileMatch&&e.fileMatch.length&&this.addFilePatternAssociation(e.fileMatch,e.folderUri,[t]),e.schema?this.addSchemaHandle(t,e.schema):this.getOrAddSchemaHandle(t)}clearExternalSchemas(){this.schemasById={},this.filePatternAssociations=[],this.registeredSchemasIds={},this.cachedSchemaForResource=void 0;for(const e in this.contributionSchemas)this.schemasById[e]=this.contributionSchemas[e],this.registeredSchemasIds[e]=!0;for(const e of this.contributionAssociations)this.filePatternAssociations.push(e)}getResolvedSchema(e){const t=Ot(e),n=this.schemasById[t];return n?n.getResolvedSchema():this.promise.resolve(void 0)}loadSchema(e){if(!this.requestService){const t=gt.t("Unable to load schema from '{0}'. No schema request service available",vn(e));return this.promise.resolve(new pn({},[t]))}return this.requestService(e).then((t=>{if(!t){const t=gt.t("Unable to load schema from '{0}': No content.",vn(e));return new pn({},[t])}const n=[];65279===t.charCodeAt(0)&&(n.push(gt.t("Problem reading content from '{0}': UTF-8 with BOM detected, only UTF 8 is allowed.",vn(e))),t=t.trimStart());let r={};const o=[];return r=b(t,o),o.length&&n.push(gt.t("Unable to parse content from '{0}': Parse error at offset {1}.",vn(e),o[0].offset)),new pn(r,n)}),(t=>{let n=t.toString();const r=t.toString().split("Error: ");return r.length>1&&(n=r[1]),it(n,".")&&(n=n.substr(0,n.length-1)),new pn({},[gt.t("Unable to load schema from '{0}': {1}.",vn(e),n)])}))}resolveSchemaContent(e,t){const n=e.errors.slice(0),r=e.schema,o=r.$schema?Mt(r.$schema):void 0;if(o===Ze.v3)return this.promise.resolve(new mn({},[gt.t("Draft-03 schemas are not supported.")],[],o));let i=new Set;const s=this.contextService,a=(e,t,r,o)=>{let i;i=void 0===o||0===o.length?t:"/"===o.charAt(0)?((e,t)=>{t=decodeURIComponent(t);let n=e;return"/"===t[0]&&(t=t.substring(1)),t.split("/").some((e=>(e=e.replace(/~1/g,"/").replace(/~0/g,"~"),n=n[e],!n))),n})(t,o):((e,t,n)=>(t.anchors||(t.anchors=l(e)),t.anchors.get(n)))(t,r,o),i?((e,t)=>{for(const n in t)t.hasOwnProperty(n)&&"id"!==n&&"$id"!==n&&(e[n]=t[n])})(e,i):n.push(gt.t("$ref '{0}' in '{1}' can not be resolved.",o||"",r.uri))},c=(e,t,r,o)=>{s&&!/^[A-Za-z][A-Za-z0-9+\-.+]*:\/.*/.test(t)&&(t=s.resolveRelativePath(t,o.uri)),t=Ot(t);const i=this.getOrAddSchemaHandle(t);return i.getUnresolvedSchema().then((s=>{if(o.dependencies.add(t),s.errors.length){const e=r?t+"#"+r:t;n.push(gt.t("Problems loading reference '{0}': {1}",e,s.errors[0]))}return a(e,s.schema,i,r),u(e,s.schema,i)}))},u=(e,t,n)=>{const r=[];return this.traverseNodes(e,(e=>{const o=new Set;for(;e.$ref;){const i=e.$ref,s=i.split("#",2);if(delete e.$ref,s[0].length>0)return void r.push(c(e,s[0],s[1],n));if(!o.has(i)){const r=s[1];a(e,t,n,r),o.add(i)}}e.$recursiveRef&&i.add("$recursiveRef"),e.$dynamicRef&&i.add("$dynamicRef")})),this.promise.all(r)},l=e=>{const t=new Map;return this.traverseNodes(e,(e=>{const r=e.$id||e.id,o=rt(r)&&"#"===r.charAt(0)?r.substring(1):e.$anchor;o&&(t.has(o)?n.push(gt.t("Duplicate anchor declaration: '{0}'",o)):t.set(o,e)),e.$recursiveAnchor&&i.add("$recursiveAnchor"),e.$dynamicAnchor&&i.add("$dynamicAnchor")})),t};return u(r,r,t).then((e=>{let t=[];return i.size&&t.push(gt.t("The schema uses meta-schema features ({0}) that are not yet supported by the validator.",Array.from(i.keys()).join(", "))),new mn(r,n,t,o)}))}traverseNodes(e,t){if(!e||"object"!=typeof e)return Promise.resolve(null);const n=new Set,r=(...e)=>{for(const t of e)ot(t)&&a.push(t)},o=(...e)=>{for(const t of e)if(ot(t))for(const e in t){const n=t[e];ot(n)&&a.push(n)}},i=(...e)=>{for(const t of e)if(Array.isArray(t))for(const e of t)ot(e)&&a.push(e)},s=e=>{if(Array.isArray(e))for(const t of e)ot(t)&&a.push(t);else ot(e)&&a.push(e)},a=[e];let c=a.pop();for(;c;)n.has(c)||(n.add(c),t(c),r(c.additionalItems,c.additionalProperties,c.not,c.contains,c.propertyNames,c.if,c.then,c.else,c.unevaluatedItems,c.unevaluatedProperties),o(c.definitions,c.$defs,c.properties,c.patternProperties,c.dependencies,c.dependentSchemas),i(c.anyOf,c.allOf,c.oneOf,c.prefixItems),s(c.items)),c=a.pop()}getSchemaFromProperty(e,t){if("object"===t.root?.type)for(const n of t.root.properties)if("$schema"===n.keyNode.value&&"string"===n.valueNode?.type){let t=n.valueNode.value;return this.contextService&&!/^\w[\w\d+.-]*:/.test(t)&&(t=this.contextService.resolveRelativePath(t,e)),t}}getAssociatedSchemas(e){const t=Object.create(null),n=[],r=bn(e);for(const e of this.filePatternAssociations)if(e.matchesPattern(r))for(const r of e.getURIs())t[r]||(n.push(r),t[r]=!0);return n}getSchemaURIsForResource(e,t){let n=t&&this.getSchemaFromProperty(e,t);return n?[n]:this.getAssociatedSchemas(e)}getSchemaForResource(e,t){if(t){let n=this.getSchemaFromProperty(e,t);if(n){const e=Ot(n);return this.getOrAddSchemaHandle(e).getResolvedSchema()}}if(this.cachedSchemaForResource&&this.cachedSchemaForResource.resource===e)return this.cachedSchemaForResource.resolvedSchema;const n=this.getAssociatedSchemas(e),r=n.length>0?this.createCombinedSchema(e,n).getResolvedSchema():this.promise.resolve(void 0);return this.cachedSchemaForResource={resource:e,resolvedSchema:r},r}createCombinedSchema(e,t){if(1===t.length)return this.getOrAddSchemaHandle(t[0]);{const n="schemaservice://combinedSchema/"+encodeURIComponent(e),r={allOf:t.map((e=>({$ref:e})))};return this.addSchemaHandle(n,r)}}getMatchingSchemas(e,t,n){if(n){const e=n.id||"schemaservice://untitled/matchingSchemas/"+yn++;return this.addSchemaHandle(e,n).getResolvedSchema().then((e=>t.getMatchingSchemas(e.schema).filter((e=>!e.inverted))))}return this.getSchemaForResource(e.uri,t).then((e=>e?t.getMatchingSchemas(e.schema).filter((e=>!e.inverted)):[]))}}let yn=0;function bn(e){try{return mt.URI.parse(e).with({fragment:null,query:null}).toString(!0)}catch(t){return e}}function vn(e){try{const t=mt.URI.parse(e);if("file"===t.scheme)return t.fsPath}catch(e){}return e}function kn(e,t){const n=[],r=[],o=[];let i=-1;const s=m(e.getText(),!1);let a=s.scan();function c(e){n.push(e),r.push(o.length)}for(;17!==a;){switch(a){case 1:case 3:{const t=e.positionAt(s.getTokenOffset()).line,n={startLine:t,endLine:t,kind:1===a?"object":"array"};o.push(n);break}case 2:case 4:{const t=2===a?"object":"array";if(o.length>0&&o[o.length-1].kind===t){const t=o.pop(),n=e.positionAt(s.getTokenOffset()).line;t&&n>t.startLine+1&&i!==t.startLine&&(t.endLine=n-1,c(t),i=t.startLine)}break}case 13:{const t=e.positionAt(s.getTokenOffset()).line,n=e.positionAt(s.getTokenOffset()+s.getTokenLength()).line;1===s.getTokenError()&&t+1<e.lineCount?s.setPosition(e.offsetAt(w.create(t+1,0))):t<n&&(c({startLine:t,endLine:n,kind:q.Comment}),i=t);break}case 12:{const t=e.getText().substr(s.getTokenOffset(),s.getTokenLength()).match(/^\/\/\s*#(region\b)|(endregion\b)/);if(t){const n=e.positionAt(s.getTokenOffset()).line;if(t[1]){const e={startLine:n,endLine:n,kind:q.Region};o.push(e)}else{let e=o.length-1;for(;e>=0&&o[e].kind!==q.Region;)e--;if(e>=0){const t=o[e];o.length=e,n>t.startLine&&i!==t.startLine&&(t.endLine=n,c(t),i=t.startLine)}}}break}}a=s.scan()}const u=t&&t.rangeLimit;if("number"!=typeof u||n.length<=u)return n;t&&t.onRangeLimitExceeded&&t.onRangeLimitExceeded(e.uri);const l=[];for(let e of r)e<30&&(l[e]=(l[e]||0)+1);let d=0,f=0;for(let e=0;e<l.length;e++){const t=l[e];if(t){if(t+d>u){f=e;break}d+=t}}const h=[];for(let e=0;e<n.length;e++){const t=r[e];"number"==typeof t&&(t<f||t===f&&d++<u)&&h.push(n[e])}return h}function _n(e,t,n){function r(t,n){return x.create(e.positionAt(t),e.positionAt(n))}const o=m(e.getText(),!0);function i(e,t){return o.setPosition(e),o.scan()===t?o.getTokenOffset()+o.getTokenLength():-1}return t.map((function(t){let o=e.offsetAt(t),s=n.getNodeFromOffset(o,!0);const a=[];for(;s;){switch(s.type){case"string":case"object":case"array":const e=s.offset+1,t=s.offset+s.length-1;e<t&&o>=e&&o<=t&&a.push(r(e,t)),a.push(r(s.offset,s.offset+s.length));break;case"number":case"boolean":case"null":case"property":a.push(r(s.offset,s.offset+s.length))}if("property"===s.type||s.parent&&"array"===s.parent.type){const e=i(s.offset+s.length,5);-1!==e&&a.push(r(s.offset,e))}s=s.parent}let c;for(let e=a.length-1;e>=0;e--)c=Pe.create(a[e],c);return c||(c=Pe.create(x.create(t,t))),c}))}function Tn(e,t,n){let o;if(n){const t=e.offsetAt(n.start);o={offset:t,length:e.offsetAt(n.end)-t}}const i={tabSize:t?t.tabSize:4,insertSpaces:!0===t?.insertSpaces,insertFinalNewline:!0===t?.insertFinalNewline,eol:"\n",keepLines:!0===t?.keepLines};return function(e,t,n){return function(e,t,n){let o,i,s,a,u;if(t){for(a=t.offset,u=a+t.length,s=a;s>0&&!h(e,s-1);)s--;let r=u;for(;r<e.length&&!h(e,r);)r++;i=e.substring(s,r),o=function(e,t){let n=0,r=0;const o=t.tabSize||4;for(;n<e.length;){let t=e.charAt(n);if(t===c[1])r++;else{if("\t"!==t)break;r+=o}n++}return Math.floor(r/o)}(i,n)}else i=e,o=0,s=0,a=0,u=e.length;const p=function(e,t){for(let e=0;e<t.length;e++){const n=t.charAt(e);if("\r"===n)return e+1<t.length&&"\n"===t.charAt(e+1)?"\r\n":"\r";if("\n"===n)return"\n"}return e&&e.eol||"\n"}(n,e),m=d.includes(p);let g,y=0,b=0;g=n.insertSpaces?c[n.tabSize||4]??f(c[1],n.tabSize||4):"\t";const v="\t"===g?"\t":" ";let k=r(i,!1),_=!1;function T(){if(y>1)return f(p,y)+f(g,o+b);const e=g.length*(o+b);return!m||e>l[v][p].length?p+f(g,o+b):e<=0?p:l[v][p][e]}function S(){let e=k.scan();for(y=0;15===e||14===e;)14===e&&n.keepLines?y+=1:14===e&&(y=1),e=k.scan();return _=16===e||0!==k.getTokenError(),e}const C=[];function R(n,r,o){_||t&&!(r<u&&o>a)||e.substring(r,o)===n||C.push({offset:r,length:o-r,content:n})}let D=S();if(n.keepLines&&y>0&&R(f(p,y),0,0),17!==D){let e=k.getTokenOffset()+s;R(g.length*o<20&&n.insertSpaces?c[g.length*o]:f(g,o),s,e)}for(;17!==D;){let e=k.getTokenOffset()+k.getTokenLength()+s,t=S(),r="",o=!1;for(;0===y&&(12===t||13===t);){let n=k.getTokenOffset()+s;R(c[1],e,n),e=k.getTokenOffset()+k.getTokenLength()+s,o=12===t,r=o?T():"",t=S()}if(2===t)1!==D&&b--,n.keepLines&&y>0||!n.keepLines&&1!==D?r=T():n.keepLines&&(r=c[1]);else if(4===t)3!==D&&b--,n.keepLines&&y>0||!n.keepLines&&3!==D?r=T():n.keepLines&&(r=c[1]);else{switch(D){case 3:case 1:b++,r=n.keepLines&&y>0||!n.keepLines?T():c[1];break;case 5:r=n.keepLines&&y>0||!n.keepLines?T():c[1];break;case 12:r=T();break;case 13:y>0?r=T():o||(r=c[1]);break;case 6:n.keepLines&&y>0?r=T():o||(r=c[1]);break;case 10:n.keepLines&&y>0?r=T():6!==t||o||(r="");break;case 7:case 8:case 9:case 11:case 2:case 4:n.keepLines&&y>0?r=T():12!==t&&13!==t||o?5!==t&&17!==t&&(_=!0):r=c[1];break;case 16:_=!0}y>0&&(12===t||13===t)&&(r=T())}17===t&&(r=n.keepLines&&y>0?T():n.insertFinalNewline?p:""),R(r,e,k.getTokenOffset()+s),D=t}return C}(e,t,n)}(e.getText(),o,i).map((t=>V.replace(x.create(e.positionAt(t.offset),e.positionAt(t.offset+t.length)),t.content)))}var Sn;!function(e){e[e.Object=0]="Object",e[e.Array=1]="Array"}(Sn||(Sn={}));class Cn{constructor(e,t){this.propertyName=e??"",this.beginningLineNumber=t,this.childrenProperties=[],this.lastProperty=!1,this.noKeyName=!1}addChildProperty(e){if(e.parent=this,this.childrenProperties.length>0){let t=0;t=e.noKeyName?this.childrenProperties.length:function(e,t,n){const r=t.propertyName.toLowerCase(),o=e[0].propertyName.toLowerCase(),i=e[e.length-1].propertyName.toLowerCase();if(r<o)return 0;if(r>i)return e.length;let s=0,a=e.length-1;for(;s<=a;){let r=a+s>>1,o=n(t,e[r]);if(o>0)s=r+1;else{if(!(o<0))return r;a=r-1}}return-s-1}(this.childrenProperties,e,Rn),t<0&&(t=-1*t-1),this.childrenProperties.splice(t,0,e)}else this.childrenProperties.push(e);return e}}function Rn(e,t){const n=e.propertyName.toLowerCase(),r=t.propertyName.toLowerCase();return n<r?-1:n>r?1:0}function Dn(e,t,n){if(0!==t.childrenProperties.length)if(t.type===Sn.Object){let r=1/0;for(const e of t.childrenProperties)e.beginningLineNumber<r&&(r=e.beginningLineNumber);n+=r-t.beginningLineNumber,t.childrenProperties.sort(((e,t)=>e.propertyName.localeCompare(t.propertyName))),e.push(new xn(n,t.childrenProperties))}else t.type===Sn.Array&&wn(e,t,n)}function wn(e,t,n){for(const r of t.childrenProperties){if(r.type===Sn.Object){let o=1/0;for(const e of r.childrenProperties)e.beginningLineNumber<o&&(o=e.beginningLineNumber);const i=o-r.beginningLineNumber;e.push(new xn(n+r.beginningLineNumber-t.beginningLineNumber+i,r.childrenProperties))}r.type===Sn.Array&&wn(e,r,n+r.beginningLineNumber-t.beginningLineNumber)}}class xn{constructor(e,t){this.beginningLineNumber=e,this.propertyTreeArray=t}}function Pn(e,t){const n=[];return t.visit((r=>{if("property"===r.type&&"$ref"===r.keyNode.value&&"string"===r.valueNode?.type){const o=r.valueNode.value,i=function(e,t){const n=function(e){return"#"===e?[]:"#"!==e[0]||"/"!==e[1]?null:e.substring(2).split(/\//).map(Nn)}(t);return n?Mn(n,e.root):null}(t,o);if(i){const t=e.positionAt(i.offset);n.push({target:`${e.uri}#${t.line+1},${t.character+1}`,range:On(e,r.valueNode)})}}return!0})),Promise.resolve(n)}function On(e,t){return x.create(e.positionAt(t.offset+1),e.positionAt(t.offset+t.length-1))}function Mn(e,t){if(!t)return null;if(0===e.length)return t;const n=e.shift();if(t&&"object"===t.type){const r=t.properties.find((e=>e.keyNode.value===n));return r?Mn(e,r.valueNode):null}if(t&&"array"===t.type&&n.match(/^(0|[1-9][0-9]*)$/)){const r=Number.parseInt(n),o=t.items[r];return o?Mn(e,o):null}return null}function Nn(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}function En(e){const t=e.promiseConstructor||Promise,n=new gn(e.schemaRequestService,e.workspaceContext,t);n.setSchemaContributions(un);const r=new Ht(n,e.contributions,t,e.clientCapabilities),o=new Bt(n,e.contributions,t),i=new sn(n),s=new Kt(n,t);return{configure:e=>{n.clearExternalSchemas(),e.schemas?.forEach(n.registerExternalSchema.bind(n)),s.configure(e)},resetSchema:e=>n.onResourceChange(e),doValidation:s.doValidation.bind(s),getLanguageStatus:s.getLanguageStatus.bind(s),parseJSONDocument:e=>function(e,t){const n=[];let r=-1;const o=e.getText(),i=m(o,!1),s=t&&t.collectComments?[]:void 0;function a(){for(;;){const t=i.scan();switch(l(),t){case 12:case 13:Array.isArray(s)&&s.push(x.create(e.positionAt(i.getTokenOffset()),e.positionAt(i.getTokenOffset()+i.getTokenLength())));break;case 15:case 14:break;default:return t}}}function c(t,o,i,s,a=I.Error){if(0===n.length||i!==r){const c=x.create(e.positionAt(i),e.positionAt(s));n.push($.create(c,t,a,o,e.languageId)),r=i}}function u(e,t,n=void 0,r=[],s=[]){let u=i.getTokenOffset(),l=i.getTokenOffset()+i.getTokenLength();if(u===l&&u>0){for(u--;u>0&&/\s/.test(o.charAt(u));)u--;l=u+1}if(c(e,t,u,l),n&&d(n,!1),r.length+s.length>0){let e=i.getToken();for(;17!==e;){if(-1!==r.indexOf(e)){a();break}if(-1!==s.indexOf(e))break;e=a()}}return n}function l(){switch(i.getTokenError()){case 4:return u(gt.t("Invalid unicode sequence in string."),Xe.InvalidUnicode),!0;case 5:return u(gt.t("Invalid escape character in string."),Xe.InvalidEscapeCharacter),!0;case 3:return u(gt.t("Unexpected end of number."),Xe.UnexpectedEndOfNumber),!0;case 1:return u(gt.t("Unexpected end of comment."),Xe.UnexpectedEndOfComment),!0;case 2:return u(gt.t("Unexpected end of string."),Xe.UnexpectedEndOfString),!0;case 6:return u(gt.t("Invalid characters in string. Control characters must be escaped."),Xe.InvalidCharacter),!0}return!1}function d(e,t){return e.length=i.getTokenOffset()+i.getTokenLength()-e.offset,t&&a(),e}const f=new St(void 0,0,0);function h(t,n){const r=new Ct(t,i.getTokenOffset(),f);let o=p(r);if(!o){if(16!==i.getToken())return;{u(gt.t("Property keys must be doublequoted"),Xe.PropertyKeysMustBeDoublequoted);const e=new St(r,i.getTokenOffset(),i.getTokenLength());e.value=i.getTokenValue(),o=e,a()}}if(r.keyNode=o,"//"!==o.value){const e=n[o.value];e?(c(gt.t("Duplicate object key"),Xe.DuplicateKey,r.keyNode.offset,r.keyNode.offset+r.keyNode.length,I.Warning),ot(e)&&c(gt.t("Duplicate object key"),Xe.DuplicateKey,e.keyNode.offset,e.keyNode.offset+e.keyNode.length,I.Warning),n[o.value]=!0):n[o.value]=r}if(6===i.getToken())r.colonOffset=i.getTokenOffset(),a();else if(u(gt.t("Colon expected"),Xe.ColonExpected),10===i.getToken()&&e.positionAt(o.offset+o.length).line<e.positionAt(i.getTokenOffset()).line)return r.length=o.length,r;const s=g(r);return s?(r.valueNode=s,r.length=s.offset+s.length-r.offset,r):u(gt.t("Value expected"),Xe.ValueExpected,r,[],[2,5])}function p(e){if(10!==i.getToken())return;const t=new St(e,i.getTokenOffset());return t.value=i.getTokenValue(),d(t,!0)}function g(e){return function(e){if(3!==i.getToken())return;const t=new _t(e,i.getTokenOffset());a();let n=!1;for(;4!==i.getToken()&&17!==i.getToken();){if(5===i.getToken()){n||u(gt.t("Value expected"),Xe.ValueExpected);const e=i.getTokenOffset();if(a(),4===i.getToken()){n&&c(gt.t("Trailing comma"),Xe.TrailingComma,e,e+1);continue}}else n&&u(gt.t("Expected comma"),Xe.CommaExpected);const e=g(t);e?t.items.push(e):u(gt.t("Value expected"),Xe.ValueExpected,void 0,[],[4,5]),n=!0}return 4!==i.getToken()?u(gt.t("Expected comma or closing bracket"),Xe.CommaOrCloseBacketExpected,t):d(t,!0)}(e)||function(e){if(1!==i.getToken())return;const t=new Rt(e,i.getTokenOffset()),n=Object.create(null);a();let r=!1;for(;2!==i.getToken()&&17!==i.getToken();){if(5===i.getToken()){r||u(gt.t("Property expected"),Xe.PropertyExpected);const e=i.getTokenOffset();if(a(),2===i.getToken()){r&&c(gt.t("Trailing comma"),Xe.TrailingComma,e,e+1);continue}}else r&&u(gt.t("Expected comma"),Xe.CommaExpected);const e=h(t,n);e?t.properties.push(e):u(gt.t("Property expected"),Xe.PropertyExpected,void 0,[],[2,5]),r=!0}return 2!==i.getToken()?u(gt.t("Expected comma or closing brace"),Xe.CommaOrCloseBraceExpected,t):d(t,!0)}(e)||p(e)||function(e){if(11!==i.getToken())return;const t=new Tt(e,i.getTokenOffset());if(0===i.getTokenError()){const e=i.getTokenValue();try{const n=JSON.parse(e);if(!et(n))return u(gt.t("Invalid number format."),Xe.Undefined,t);t.value=n}catch(e){return u(gt.t("Invalid number format."),Xe.Undefined,t)}t.isInteger=-1===e.indexOf(".")}return d(t,!0)}(e)||function(e){switch(i.getToken()){case 7:return d(new vt(e,i.getTokenOffset()),!0);case 8:return d(new kt(e,!0,i.getTokenOffset()),!0);case 9:return d(new kt(e,!1,i.getTokenOffset()),!0);default:return}}(e)}let y;return 17!==a()&&(y=g(y),y?17!==i.getToken()&&u(gt.t("End of file expected."),Xe.Undefined):u(gt.t("Expected a JSON object, array or literal."),Xe.Undefined)),new $t(y,n,s)}(e,{collectComments:!0}),newJSONDocument:(e,t,n)=>function(e,t=[],n=[]){return new $t(e,t,n)}(e,t,n),getMatchingSchemas:n.getMatchingSchemas.bind(n),doResolve:r.doResolve.bind(r),doComplete:r.doComplete.bind(r),findDocumentSymbols:i.findDocumentSymbols.bind(i),findDocumentSymbols2:i.findDocumentSymbols2.bind(i),findDocumentColors:i.findDocumentColors.bind(i),getColorPresentations:i.getColorPresentations.bind(i),doHover:o.doHover.bind(o),getFoldingRanges:kn,getSelectionRanges:_n,findDefinition:()=>Promise.resolve([]),findLinks:Pn,format:(e,t,n)=>Tn(e,n,t),sort:(e,t)=>function(e,t){const n={...t,keepLines:!1},r=Ge.applyEdits(e,Tn(e,n,void 0)),o=Ge.create("test://test.json","json",0,r),i=function(e,t){if(0===t.childrenProperties.length)return e;const n=Ge.create("test://test.json","json",0,e.getText()),r=[];for(Dn(r,t,t.beginningLineNumber);r.length>0;){const t=r.shift(),o=t.propertyTreeArray;let i=t.beginningLineNumber;for(let t=0;t<o.length;t++){const s=o[t],a=x.create(w.create(s.beginningLineNumber,0),w.create(s.endLineNumber+1,0)),c=e.getText(a),u=Ge.create("test://test.json","json",0,c);if(!0===s.lastProperty&&t!==o.length-1){const e=s.lineWhereToAddComma-s.beginningLineNumber,t=s.indexWhereToAddComa,n={range:x.create(w.create(e,t),w.create(e,t)),text:","};Ge.update(u,[n],1)}else if(!1===s.lastProperty&&t===o.length-1){const e=s.commaIndex,t=s.commaLine-s.beginningLineNumber,n={range:x.create(w.create(t,e),w.create(t,e+1)),text:""};Ge.update(u,[n],1)}const l=s.endLineNumber-s.beginningLineNumber+1,d={range:x.create(w.create(i,0),w.create(i+l,0)),text:u.getText()};Ge.update(n,[d],1),Dn(r,s,i),i+=l}}return n}(o,function(e){const t=e.getText(),n=m(t,!1);let r,o,i,s=new Cn,a=s,c=s,u=s,l=0,d=0,f=-1,h=-1,p=0,g=0,y=[],b=!1,v=!1;for(;17!==(r=n.scan());){if(!0===b&&14!==r&&15!==r&&12!==r&&13!==r&&void 0===c.endLineNumber){let e=n.getTokenStartLine();2===i||4===i?u.endLineNumber=e-1:c.endLineNumber=e-1,p=e,b=!1}if(!0===v&&14!==r&&15!==r&&12!==r&&13!==r&&(p=n.getTokenStartLine(),v=!1),n.getTokenStartLine()!==l){for(let t=l;t<n.getTokenStartLine();t++)d+=e.getText(x.create(w.create(t,0),w.create(t+1,0))).length;l=n.getTokenStartLine()}switch(r){case 10:if(void 0===o||1===o||5===o&&y[y.length-1]===Sn.Object){const e=new Cn(n.getTokenValue(),p);u=c,c=a.addChildProperty(e)}break;case 3:if(void 0===s.beginningLineNumber&&(s.beginningLineNumber=n.getTokenStartLine()),y[y.length-1]===Sn.Object)a=c;else if(y[y.length-1]===Sn.Array){const e=new Cn(n.getTokenValue(),p);e.noKeyName=!0,u=c,c=a.addChildProperty(e),a=c}y.push(Sn.Array),c.type=Sn.Array,p=n.getTokenStartLine(),p++;break;case 1:if(void 0===s.beginningLineNumber)s.beginningLineNumber=n.getTokenStartLine();else if(y[y.length-1]===Sn.Array){const e=new Cn(n.getTokenValue(),p);e.noKeyName=!0,u=c,c=a.addChildProperty(e)}c.type=Sn.Object,y.push(Sn.Object),a=c,p=n.getTokenStartLine(),p++;break;case 4:g=n.getTokenStartLine(),y.pop(),void 0!==c.endLineNumber||2!==o&&4!==o||(c.endLineNumber=g-1,c.lastProperty=!0,c.lineWhereToAddComma=f,c.indexWhereToAddComa=h,u=c,c=c?c.parent:void 0,a=c),s.endLineNumber=g,p=g+1;break;case 2:g=n.getTokenStartLine(),y.pop(),1!==o&&(void 0===c.endLineNumber&&(c.endLineNumber=g-1,c.lastProperty=!0,c.lineWhereToAddComma=f,c.indexWhereToAddComa=h),u=c,c=c?c.parent:void 0,a=c),s.endLineNumber=n.getTokenStartLine(),p=g+1;break;case 5:g=n.getTokenStartLine(),void 0!==c.endLineNumber||y[y.length-1]!==Sn.Object&&(y[y.length-1]!==Sn.Array||2!==o&&4!==o)||(c.endLineNumber=g,c.commaIndex=n.getTokenOffset()-d,c.commaLine=g),2!==o&&4!==o||(u=c,c=c?c.parent:void 0,a=c),p=g+1;break;case 13:5!==o||f!==n.getTokenStartLine()||(y[y.length-1]!==Sn.Array||2!==i&&4!==i)&&y[y.length-1]!==Sn.Object||(y[y.length-1]!==Sn.Array||2!==i&&4!==i)&&y[y.length-1]!==Sn.Object||(c.endLineNumber=void 0,b=!0),1!==o&&3!==o||f!==n.getTokenStartLine()||(v=!0)}14!==r&&13!==r&&12!==r&&15!==r&&(i=o,o=r,f=n.getTokenStartLine(),h=n.getTokenOffset()+n.getTokenLength()-d)}return s}(o)),s=Tn(i,n,void 0),a=Ge.applyEdits(i,s);return[V.replace(x.create(w.create(0,0),e.positionAt(e.getText().length)),a)]}(e,t)}}},3281:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.ProgressType=t.ProgressToken=t.createMessageConnection=t.NullLogger=t.ConnectionOptions=t.ConnectionStrategy=t.AbstractMessageBuffer=t.WriteableStreamMessageWriter=t.AbstractMessageWriter=t.MessageWriter=t.ReadableStreamMessageReader=t.AbstractMessageReader=t.MessageReader=t.SharedArrayReceiverStrategy=t.SharedArraySenderStrategy=t.CancellationToken=t.CancellationTokenSource=t.Emitter=t.Event=t.Disposable=t.LRUCache=t.Touch=t.LinkedMap=t.ParameterStructures=t.NotificationType9=t.NotificationType8=t.NotificationType7=t.NotificationType6=t.NotificationType5=t.NotificationType4=t.NotificationType3=t.NotificationType2=t.NotificationType1=t.NotificationType0=t.NotificationType=t.ErrorCodes=t.ResponseError=t.RequestType9=t.RequestType8=t.RequestType7=t.RequestType6=t.RequestType5=t.RequestType4=t.RequestType3=t.RequestType2=t.RequestType1=t.RequestType0=t.RequestType=t.Message=t.RAL=void 0,t.MessageStrategy=t.CancellationStrategy=t.CancellationSenderStrategy=t.RequestCancellationReceiverStrategy=t.IdCancellationReceiverStrategy=t.CancellationReceiverStrategy=t.ConnectionError=t.ConnectionErrors=t.LogTraceNotification=t.SetTraceNotification=t.TraceFormat=t.TraceValues=t.TraceValue=t.Trace=void 0;const o=n(6177);Object.defineProperty(t,"Message",{enumerable:!0,get:function(){return o.Message}}),Object.defineProperty(t,"RequestType",{enumerable:!0,get:function(){return o.RequestType}}),Object.defineProperty(t,"RequestType0",{enumerable:!0,get:function(){return o.RequestType0}}),Object.defineProperty(t,"RequestType1",{enumerable:!0,get:function(){return o.RequestType1}}),Object.defineProperty(t,"RequestType2",{enumerable:!0,get:function(){return o.RequestType2}}),Object.defineProperty(t,"RequestType3",{enumerable:!0,get:function(){return o.RequestType3}}),Object.defineProperty(t,"RequestType4",{enumerable:!0,get:function(){return o.RequestType4}}),Object.defineProperty(t,"RequestType5",{enumerable:!0,get:function(){return o.RequestType5}}),Object.defineProperty(t,"RequestType6",{enumerable:!0,get:function(){return o.RequestType6}}),Object.defineProperty(t,"RequestType7",{enumerable:!0,get:function(){return o.RequestType7}}),Object.defineProperty(t,"RequestType8",{enumerable:!0,get:function(){return o.RequestType8}}),Object.defineProperty(t,"RequestType9",{enumerable:!0,get:function(){return o.RequestType9}}),Object.defineProperty(t,"ResponseError",{enumerable:!0,get:function(){return o.ResponseError}}),Object.defineProperty(t,"ErrorCodes",{enumerable:!0,get:function(){return o.ErrorCodes}}),Object.defineProperty(t,"NotificationType",{enumerable:!0,get:function(){return o.NotificationType}}),Object.defineProperty(t,"NotificationType0",{enumerable:!0,get:function(){return o.NotificationType0}}),Object.defineProperty(t,"NotificationType1",{enumerable:!0,get:function(){return o.NotificationType1}}),Object.defineProperty(t,"NotificationType2",{enumerable:!0,get:function(){return o.NotificationType2}}),Object.defineProperty(t,"NotificationType3",{enumerable:!0,get:function(){return o.NotificationType3}}),Object.defineProperty(t,"NotificationType4",{enumerable:!0,get:function(){return o.NotificationType4}}),Object.defineProperty(t,"NotificationType5",{enumerable:!0,get:function(){return o.NotificationType5}}),Object.defineProperty(t,"NotificationType6",{enumerable:!0,get:function(){return o.NotificationType6}}),Object.defineProperty(t,"NotificationType7",{enumerable:!0,get:function(){return o.NotificationType7}}),Object.defineProperty(t,"NotificationType8",{enumerable:!0,get:function(){return o.NotificationType8}}),Object.defineProperty(t,"NotificationType9",{enumerable:!0,get:function(){return o.NotificationType9}}),Object.defineProperty(t,"ParameterStructures",{enumerable:!0,get:function(){return o.ParameterStructures}});const i=n(3352);Object.defineProperty(t,"LinkedMap",{enumerable:!0,get:function(){return i.LinkedMap}}),Object.defineProperty(t,"LRUCache",{enumerable:!0,get:function(){return i.LRUCache}}),Object.defineProperty(t,"Touch",{enumerable:!0,get:function(){return i.Touch}});const s=n(4019);Object.defineProperty(t,"Disposable",{enumerable:!0,get:function(){return s.Disposable}});const a=n(2676);Object.defineProperty(t,"Event",{enumerable:!0,get:function(){return a.Event}}),Object.defineProperty(t,"Emitter",{enumerable:!0,get:function(){return a.Emitter}});const c=n(9850);Object.defineProperty(t,"CancellationTokenSource",{enumerable:!0,get:function(){return c.CancellationTokenSource}}),Object.defineProperty(t,"CancellationToken",{enumerable:!0,get:function(){return c.CancellationToken}});const u=n(4996);Object.defineProperty(t,"SharedArraySenderStrategy",{enumerable:!0,get:function(){return u.SharedArraySenderStrategy}}),Object.defineProperty(t,"SharedArrayReceiverStrategy",{enumerable:!0,get:function(){return u.SharedArrayReceiverStrategy}});const l=n(9085);Object.defineProperty(t,"MessageReader",{enumerable:!0,get:function(){return l.MessageReader}}),Object.defineProperty(t,"AbstractMessageReader",{enumerable:!0,get:function(){return l.AbstractMessageReader}}),Object.defineProperty(t,"ReadableStreamMessageReader",{enumerable:!0,get:function(){return l.ReadableStreamMessageReader}});const d=n(3193);Object.defineProperty(t,"MessageWriter",{enumerable:!0,get:function(){return d.MessageWriter}}),Object.defineProperty(t,"AbstractMessageWriter",{enumerable:!0,get:function(){return d.AbstractMessageWriter}}),Object.defineProperty(t,"WriteableStreamMessageWriter",{enumerable:!0,get:function(){return d.WriteableStreamMessageWriter}});const f=n(9244);Object.defineProperty(t,"AbstractMessageBuffer",{enumerable:!0,get:function(){return f.AbstractMessageBuffer}});const h=n(577);Object.defineProperty(t,"ConnectionStrategy",{enumerable:!0,get:function(){return h.ConnectionStrategy}}),Object.defineProperty(t,"ConnectionOptions",{enumerable:!0,get:function(){return h.ConnectionOptions}}),Object.defineProperty(t,"NullLogger",{enumerable:!0,get:function(){return h.NullLogger}}),Object.defineProperty(t,"createMessageConnection",{enumerable:!0,get:function(){return h.createMessageConnection}}),Object.defineProperty(t,"ProgressToken",{enumerable:!0,get:function(){return h.ProgressToken}}),Object.defineProperty(t,"ProgressType",{enumerable:!0,get:function(){return h.ProgressType}}),Object.defineProperty(t,"Trace",{enumerable:!0,get:function(){return h.Trace}}),Object.defineProperty(t,"TraceValue",{enumerable:!0,get:function(){return h.TraceValue}}),Object.defineProperty(t,"TraceFormat",{enumerable:!0,get:function(){return h.TraceFormat}}),Object.defineProperty(t,"SetTraceNotification",{enumerable:!0,get:function(){return h.SetTraceNotification}}),Object.defineProperty(t,"LogTraceNotification",{enumerable:!0,get:function(){return h.LogTraceNotification}}),Object.defineProperty(t,"ConnectionErrors",{enumerable:!0,get:function(){return h.ConnectionErrors}}),Object.defineProperty(t,"ConnectionError",{enumerable:!0,get:function(){return h.ConnectionError}}),Object.defineProperty(t,"CancellationReceiverStrategy",{enumerable:!0,get:function(){return h.CancellationReceiverStrategy}}),Object.defineProperty(t,"IdCancellationReceiverStrategy",{enumerable:!0,get:function(){return h.IdCancellationReceiverStrategy}}),Object.defineProperty(t,"RequestCancellationReceiverStrategy",{enumerable:!0,get:function(){return h.RequestCancellationReceiverStrategy}}),Object.defineProperty(t,"CancellationSenderStrategy",{enumerable:!0,get:function(){return h.CancellationSenderStrategy}}),Object.defineProperty(t,"CancellationStrategy",{enumerable:!0,get:function(){return h.CancellationStrategy}}),Object.defineProperty(t,"MessageStrategy",{enumerable:!0,get:function(){return h.MessageStrategy}}),Object.defineProperty(t,"TraceValues",{enumerable:!0,get:function(){return h.TraceValues}});const p=r(n(9590));t.RAL=p.default},9850:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t}),a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.CancellationTokenSource=t.CancellationToken=void 0;const c=a(n(9590)),u=s(n(8585)),l=n(2676);var d;!function(e){e.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:l.Event.None}),e.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:l.Event.None}),e.is=function(t){const n=t;return n&&(n===e.None||n===e.Cancelled||u.boolean(n.isCancellationRequested)&&!!n.onCancellationRequested)}}(d||(t.CancellationToken=d={}));const f=Object.freeze((function(e,t){const n=(0,c.default)().timer.setTimeout(e.bind(t),0);return{dispose(){n.dispose()}}}));class h{_isCancelled=!1;_emitter;cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?f:(this._emitter||(this._emitter=new l.Emitter),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=void 0)}}t.CancellationTokenSource=class{_token;get token(){return this._token||(this._token=new h),this._token}cancel(){this._token?this._token.cancel():this._token=d.Cancelled}dispose(){this._token?this._token instanceof h&&this._token.dispose():this._token=d.None}}},577:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t}),a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.ConnectionOptions=t.MessageStrategy=t.CancellationStrategy=t.CancellationSenderStrategy=t.CancellationReceiverStrategy=t.RequestCancellationReceiverStrategy=t.IdCancellationReceiverStrategy=t.ConnectionStrategy=t.ConnectionError=t.ConnectionErrors=t.LogTraceNotification=t.SetTraceNotification=t.TraceFormat=t.TraceValues=t.TraceValue=t.Trace=t.NullLogger=t.ProgressType=t.ProgressToken=void 0,t.createMessageConnection=function(e,n,r,o){const i=void 0!==r?r:t.NullLogger;let s=0,a=0,v=0;const C="2.0",D=o?.maxParallelism??-1;let w,x=0;const M=new Map;let q;const A=new Map,j=new Map;let I,L,F=new d.LinkedMap,$=new Map,W=new Set,V=new Map,U=b.Off,H=k.Text,B=N.New;const z=new f.Emitter,K=new f.Emitter,J=new f.Emitter,G=new f.Emitter,X=new f.Emitter,Z=o&&o.cancellationStrategy?o.cancellationStrategy:P.Message;function Y(e){}function Q(){return B===N.Listening}function ee(){return B===N.Closed}function te(){return B===N.Disposed}function ne(){B!==N.New&&B!==N.Listening||(B=N.Closed,K.fire(void 0))}function re(e){if(null===e)throw new Error("Can't send requests with id null since the response can't be correlated.");return"req-"+e.toString()}function oe(){I||0===F.size||-1!==D&&x>=D||(I=(0,c.default)().timer.setImmediate((async()=>{if(I=void 0,0===F.size)return;if(-1!==D&&x>=D)return;const e=F.shift();let t;try{x++;const n=o?.messageStrategy;t=O.is(n)?n.handleMessage(e,ie):ie(e)}catch(e){i.error(`Processing message queue failed: ${e.toString()}`)}finally{t instanceof Promise?t.then((()=>{x--,oe()})).catch((e=>{i.error(`Processing message queue failed: ${e.toString()}`)})):x--,oe()}})))}async function ie(e){return l.Message.isRequest(e)?async function(e){if(te())return Promise.resolve();function t(t,r,o){const i={jsonrpc:C,id:e.id};return t instanceof l.ResponseError?i.error=t.toJson():i.result=void 0===t?null:t,ue(i,r,o),n.write(i)}function r(t,r,o){const i={jsonrpc:C,id:e.id,error:t.toJson()};return ue(i,r,o),n.write(i)}!function(e){if(U!==b.Off&&L)if(H===k.Text){let t;U!==b.Verbose&&U!==b.Compact||!e.params||(t=`Params: ${ce(e.params)}`),L.log(`Received request '${e.method} - (${e.id})'.`,t)}else de("receive-request",e)}(e);const o=M.get(e.method);let i,s;o&&(i=o.type,s=o.handler);const a=Date.now();if(s||w){const n=e.id??String(Date.now()),o=R.is(Z.receiver)?Z.receiver.createCancellationTokenSource(n):Z.receiver.createCancellationTokenSource(e);null!==e.id&&W.has(e.id)&&o.cancel(),null!==e.id&&V.set(n,o);try{let n;if(s)if(void 0===e.params){if(void 0!==i&&0!==i.numberOfParams)return r(new l.ResponseError(l.ErrorCodes.InvalidParams,`Request ${e.method} defines ${i.numberOfParams} params but received none.`),e.method,a);n=s(o.token)}else if(Array.isArray(e.params)){if(void 0!==i&&i.parameterStructures===l.ParameterStructures.byName)return r(new l.ResponseError(l.ErrorCodes.InvalidParams,`Request ${e.method} defines parameters by name but received parameters by position`),e.method,a);n=s(...e.params,o.token)}else{if(void 0!==i&&i.parameterStructures===l.ParameterStructures.byPosition)return r(new l.ResponseError(l.ErrorCodes.InvalidParams,`Request ${e.method} defines parameters by position but received parameters by name`),e.method,a);n=s(e.params,o.token)}else w&&(n=w(e.method,e.params,o.token));const c=await n;await t(c,e.method,a)}catch(n){n instanceof l.ResponseError?await t(n,e.method,a):n&&u.string(n.message)?await r(new l.ResponseError(l.ErrorCodes.InternalError,`Request ${e.method} failed with message: ${n.message}`),e.method,a):await r(new l.ResponseError(l.ErrorCodes.InternalError,`Request ${e.method} failed unexpectedly without providing any details.`),e.method,a)}finally{V.delete(n)}}else await r(new l.ResponseError(l.ErrorCodes.MethodNotFound,`Unhandled method ${e.method}`),e.method,a)}(e):l.Message.isNotification(e)?async function(e){if(te())return;let t,n;if(e.method===p.type.method){const t=e.params.id;return W.delete(t),void le(e)}{const r=A.get(e.method);r&&(n=r.handler,t=r.type)}if(n||q)try{if(le(e),n)if(void 0===e.params)void 0!==t&&0!==t.numberOfParams&&t.parameterStructures!==l.ParameterStructures.byName&&i.error(`Notification ${e.method} defines ${t.numberOfParams} params but received none.`),await n();else if(Array.isArray(e.params)){const r=e.params;e.method===g.type.method&&2===r.length&&m.is(r[0])?await n({token:r[0],value:r[1]}):(void 0!==t&&(t.parameterStructures===l.ParameterStructures.byName&&i.error(`Notification ${e.method} defines parameters by name but received parameters by position`),t.numberOfParams!==e.params.length&&i.error(`Notification ${e.method} defines ${t.numberOfParams} params but received ${r.length} arguments`)),await n(...r))}else void 0!==t&&t.parameterStructures===l.ParameterStructures.byPosition&&i.error(`Notification ${e.method} defines parameters by position but received parameters by name`),await n(e.params);else q&&await q(e.method,e.params)}catch(t){t.message?i.error(`Notification handler '${e.method}' failed with message: ${t.message}`):i.error(`Notification handler '${e.method}' failed unexpectedly.`)}else J.fire(e)}(e):l.Message.isResponse(e)?ae(e):function(e){if(!e)return void i.error("Received empty message.");i.error(`Received message which is neither a response nor a notification message:\n${JSON.stringify(e,null,4)}`);const t=e;if(u.string(t.id)||u.number(t.id)){const e=t.id,n=$.get(e);n&&n.reject(new Error("The received response has neither a result nor an error property."))}}(e)}e.onClose(ne),e.onError((function(e){z.fire([e,void 0,void 0])})),n.onClose(ne),n.onError((function(e){z.fire(e)}));const se=e=>{try{if(l.Message.isNotification(e)&&e.method===p.type.method){const t=e.params.id,r=re(t),s=F.get(r);if(l.Message.isRequest(s)){const a=o?.connectionStrategy,c=a&&a.cancelUndispatched?a.cancelUndispatched(s,Y):void 0;if(c&&(void 0!==c.error||void 0!==c.result))return F.delete(r),V.delete(t),c.id=s.id,ue(c,e.method,Date.now()),void n.write(c).catch((()=>i.error("Sending response for canceled message failed.")))}const a=V.get(t);if(void 0!==a)return a.cancel(),void le(e);W.add(t)}!function(e,t){var n;l.Message.isRequest(t)?e.set(re(t.id),t):l.Message.isResponse(t)?-1===D?e.set(null===(n=t.id)?"res-unknown-"+(++v).toString():"res-"+n.toString(),t):ae(t):e.set("not-"+(++a).toString(),t)}(F,e)}finally{oe()}};function ae(e){if(!te())if(null===e.id)e.error?i.error(`Received response message without id: Error is: \n${JSON.stringify(e.error,void 0,4)}`):i.error("Received response message without id. No further error information provided.");else{const t=e.id,n=$.get(t);if(function(e,t){if(U!==b.Off&&L)if(H===k.Text){let n;if(U!==b.Verbose&&U!==b.Compact||(e.error&&e.error.data?n=`Error data: ${ce(e.error.data)}`:e.result?n=`Result: ${ce(e.result)}`:void 0===e.error&&(n="No result returned.")),t){const r=e.error?` Request failed: ${e.error.message} (${e.error.code}).`:"";L.log(`Received response '${t.method} - (${e.id})' in ${Date.now()-t.timerStart}ms.${r}`,n)}else L.log(`Received response ${e.id} without active response promise.`,n)}else de("receive-response",e)}(e,n),void 0!==n){$.delete(t);try{if(e.error){const t=e.error;n.reject(new l.ResponseError(t.code,t.message,t.data))}else{if(void 0===e.result)throw new Error("Should never happen.");n.resolve(e.result)}}catch(e){e.message?i.error(`Response handler '${n.method}' failed with message: ${e.message}`):i.error(`Response handler '${n.method}' failed unexpectedly.`)}}}}function ce(e){if(null!=e)switch(U){case b.Verbose:return JSON.stringify(e,null,4);case b.Compact:return JSON.stringify(e);default:return}}function ue(e,t,n){if(U!==b.Off&&L)if(H===k.Text){let r;U!==b.Verbose&&U!==b.Compact||(e.error&&e.error.data?r=`Error data: ${ce(e.error.data)}`:e.result?r=`Result: ${ce(e.result)}`:void 0===e.error&&(r="No result returned.")),L.log(`Sending response '${t} - (${e.id})'. Processing request took ${Date.now()-n}ms`,r)}else de("send-response",e)}function le(e){if(U!==b.Off&&L&&e.method!==T.type.method)if(H===k.Text){let t;U!==b.Verbose&&U!==b.Compact||(t=e.params?`Params: ${ce(e.params)}`:"No parameters provided."),L.log(`Received notification '${e.method}'.`,t)}else de("receive-notification",e)}function de(e,t){if(!L||U===b.Off)return;const n={isLSPMessage:!0,type:e,message:t,timestamp:Date.now()};L.log(n)}function fe(){if(ee())throw new E(S.Closed,"Connection is closed.");if(te())throw new E(S.Disposed,"Connection is disposed.")}function he(e){return void 0===e?null:e}function pe(e){return null===e?void 0:e}function me(e){return null!=e&&!Array.isArray(e)&&"object"==typeof e}function ge(e,t){switch(e){case l.ParameterStructures.auto:return me(t)?pe(t):[he(t)];case l.ParameterStructures.byName:if(!me(t))throw new Error("Received parameters by name but param is not an object literal.");return pe(t);case l.ParameterStructures.byPosition:return[he(t)];default:throw new Error(`Unknown parameter structure ${e.toString()}`)}}function ye(e,t){let n;const r=e.numberOfParams;switch(r){case 0:n=void 0;break;case 1:n=ge(e.parameterStructures,t[0]);break;default:n=[];for(let e=0;e<t.length&&e<r;e++)n.push(he(t[e]));if(t.length<r)for(let e=t.length;e<r;e++)n.push(null)}return n}const be={sendNotification:(e,...t)=>{let r,o;if(fe(),u.string(e)){r=e;const n=t[0];let i=0,s=l.ParameterStructures.auto;l.ParameterStructures.is(n)&&(i=1,s=n);const a=t.length,c=a-i;switch(c){case 0:o=void 0;break;case 1:o=ge(s,t[i]);break;default:if(s===l.ParameterStructures.byName)throw new Error(`Received ${c} parameters for 'by Name' notification parameter structure.`);o=t.slice(i,a).map((e=>he(e)))}}else{const n=t;r=e.method,o=ye(e,n)}const s={jsonrpc:C,method:r,params:o};return function(e){if(U!==b.Off&&L)if(H===k.Text){let t;U!==b.Verbose&&U!==b.Compact||(t=e.params?`Params: ${ce(e.params)}`:"No parameters provided."),L.log(`Sending notification '${e.method}'.`,t)}else de("send-notification",e)}(s),n.write(s).catch((e=>{throw i.error("Sending notification failed."),e}))},onNotification:(e,t)=>{let n;return fe(),u.func(e)?q=e:t&&(u.string(e)?(n=e,A.set(e,{type:void 0,handler:t})):(n=e.method,A.set(e.method,{type:e,handler:t}))),{dispose:()=>{void 0!==n?A.get(n)?.handler===t&&A.delete(n):q===e&&(q=void 0)}}},onProgress:(e,t,n)=>{if(j.has(t))throw new Error(`Progress handler for token ${t} already registered`);return j.set(t,n),{dispose:()=>{j.get(t)===n&&j.delete(t)}}},sendProgress:(e,t,n)=>be.sendNotification(g.type,{token:t,value:n}),onUnhandledProgress:G.event,sendRequest:(e,...t)=>{function r(e,t){const n=Z.sender.sendCancellation(e,t);void 0===n?i.log(`Received no promise from cancellation strategy when cancelling id ${t}`):n.catch((()=>{i.log(`Sending cancellation messages for id ${t} failed.`)}))}let o,a,c;if(fe(),function(){if(!Q())throw new Error("Call listen() first.")}(),u.string(e)){o=e;const n=t[0],r=t[t.length-1];let i=0,s=l.ParameterStructures.auto;l.ParameterStructures.is(n)&&(i=1,s=n);let u=t.length;h.CancellationToken.is(r)&&(u-=1,c=r);const d=u-i;switch(d){case 0:a=void 0;break;case 1:a=ge(s,t[i]);break;default:if(s===l.ParameterStructures.byName)throw new Error(`Received ${d} parameters for 'by Name' request parameter structure.`);a=t.slice(i,u).map((e=>he(e)))}}else{const n=t;o=e.method,a=ye(e,n);const r=e.numberOfParams;c=h.CancellationToken.is(n[r])?n[r]:void 0}const d=s++;let f,p=!1;void 0!==c&&(c.isCancellationRequested?p=!0:f=c.onCancellationRequested((()=>{r(be,d)})));const m={jsonrpc:C,id:d,method:o,params:a};return function(e){if(U!==b.Off&&L)if(H===k.Text){let t;U!==b.Verbose&&U!==b.Compact||!e.params||(t=`Params: ${ce(e.params)}`),L.log(`Sending request '${e.method} - (${e.id})'.`,t)}else de("send-request",e)}(m),"function"==typeof Z.sender.enableCancellation&&Z.sender.enableCancellation(m),new Promise((async(e,t)=>{const s={method:o,timerStart:Date.now(),resolve:t=>{e(t),Z.sender.cleanup(d),f?.dispose()},reject:e=>{t(e),Z.sender.cleanup(d),f?.dispose()}};try{$.set(d,s),await n.write(m),p&&r(be,d)}catch(e){throw $.delete(d),s.reject(new l.ResponseError(l.ErrorCodes.MessageWriteError,e.message?e.message:"Unknown reason")),i.error("Sending request failed."),e}}))},onRequest:(e,t)=>{fe();let n=null;return y.is(e)?(n=void 0,w=e):u.string(e)?(n=null,void 0!==t&&(n=e,M.set(e,{handler:t,type:void 0}))):void 0!==t&&(n=e.method,M.set(e.method,{type:e,handler:t})),{dispose:()=>{null!==n&&(void 0!==n?M.get(n)?.handler===t&&M.delete(n):w===e&&(w=void 0))}}},hasPendingResponse:()=>$.size>0,trace:async(e,t,n)=>{let r=!1,o=k.Text;void 0!==n&&(u.boolean(n)?r=n:(r=n.sendNotification||!1,o=n.traceFormat||k.Text)),U=e,H=o,L=U===b.Off?void 0:t,!r||ee()||te()||await be.sendNotification(_.type,{value:b.toString(e)})},onError:z.event,onClose:K.event,onUnhandledNotification:J.event,onDispose:X.event,end:()=>{n.end()},dispose:()=>{if(te())return;B=N.Disposed,X.fire(void 0);const t=new l.ResponseError(l.ErrorCodes.PendingResponseRejected,"Pending response rejected since connection got disposed");for(const e of $.values())e.reject(t);$=new Map,V=new Map,W=new Set,F=new d.LinkedMap,u.func(n.dispose)&&n.dispose(),u.func(e.dispose)&&e.dispose()},listen:()=>{fe(),function(){if(Q())throw new E(S.AlreadyListening,"Connection is already listening")}(),B=N.Listening,e.listen(se)},inspect:()=>{(0,c.default)().console.log("inspect")}};return be.onNotification(T.type,(e=>{if(U===b.Off||!L)return;const t=U===b.Verbose||U===b.Compact;L.log(e.message,t?e.verbose:void 0)})),be.onNotification(g.type,(async e=>{const t=j.get(e.token);t?await t(e.value):G.fire(e)})),be};const c=a(n(9590)),u=s(n(8585)),l=n(6177),d=n(3352),f=n(2676),h=n(9850);var p,m,g,y,b,v,k,_,T,S,C,R,D,w,x,P,O,M,N;!function(e){e.type=new l.NotificationType("$/cancelRequest")}(p||(p={})),function(e){e.is=function(e){return"string"==typeof e||"number"==typeof e}}(m||(t.ProgressToken=m={})),function(e){e.type=new l.NotificationType("$/progress")}(g||(g={})),t.ProgressType=class{__;_pr;constructor(){}},function(e){e.is=function(e){return u.func(e)}}(y||(y={})),t.NullLogger=Object.freeze({error:()=>{},warn:()=>{},info:()=>{},log:()=>{}}),function(e){e[e.Off=0]="Off",e[e.Messages=1]="Messages",e[e.Compact=2]="Compact",e[e.Verbose=3]="Verbose"}(b||(t.Trace=b={})),function(e){e.Off="off",e.Messages="messages",e.Compact="compact",e.Verbose="verbose"}(v||(t.TraceValue=v={})),t.TraceValues=v,function(e){e.fromString=function(t){if(!u.string(t))return e.Off;switch(t=t.toLowerCase()){case"off":default:return e.Off;case"messages":return e.Messages;case"compact":return e.Compact;case"verbose":return e.Verbose}},e.toString=function(t){switch(t){case e.Off:return"off";case e.Messages:return"messages";case e.Compact:return"compact";case e.Verbose:return"verbose";default:return"off"}}}(b||(t.Trace=b={})),function(e){e.Text="text",e.JSON="json"}(k||(t.TraceFormat=k={})),function(e){e.fromString=function(t){return u.string(t)&&"json"===(t=t.toLowerCase())?e.JSON:e.Text}}(k||(t.TraceFormat=k={})),function(e){e.type=new l.NotificationType("$/setTrace")}(_||(t.SetTraceNotification=_={})),function(e){e.type=new l.NotificationType("$/logTrace")}(T||(t.LogTraceNotification=T={})),function(e){e[e.Closed=1]="Closed",e[e.Disposed=2]="Disposed",e[e.AlreadyListening=3]="AlreadyListening"}(S||(t.ConnectionErrors=S={}));class E extends Error{code;constructor(e,t){super(t),this.code=e,Object.setPrototypeOf(this,E.prototype)}}t.ConnectionError=E,function(e){e.is=function(e){const t=e;return t&&u.func(t.cancelUndispatched)}}(C||(t.ConnectionStrategy=C={})),function(e){e.is=function(e){const t=e;return t&&(void 0===t.kind||"id"===t.kind)&&u.func(t.createCancellationTokenSource)&&(void 0===t.dispose||u.func(t.dispose))}}(R||(t.IdCancellationReceiverStrategy=R={})),function(e){e.is=function(e){const t=e;return t&&"request"===t.kind&&u.func(t.createCancellationTokenSource)&&(void 0===t.dispose||u.func(t.dispose))}}(D||(t.RequestCancellationReceiverStrategy=D={})),function(e){e.Message=Object.freeze({createCancellationTokenSource:e=>new h.CancellationTokenSource}),e.is=function(e){return R.is(e)||D.is(e)}}(w||(t.CancellationReceiverStrategy=w={})),function(e){e.Message=Object.freeze({sendCancellation:(e,t)=>e.sendNotification(p.type,{id:t}),cleanup(e){}}),e.is=function(e){const t=e;return t&&u.func(t.sendCancellation)&&u.func(t.cleanup)}}(x||(t.CancellationSenderStrategy=x={})),function(e){e.Message=Object.freeze({receiver:w.Message,sender:x.Message}),e.is=function(e){const t=e;return t&&w.is(t.receiver)&&x.is(t.sender)}}(P||(t.CancellationStrategy=P={})),function(e){e.is=function(e){const t=e;return t&&u.func(t.handleMessage)}}(O||(t.MessageStrategy=O={})),function(e){e.is=function(e){const t=e;return t&&(P.is(t.cancellationStrategy)||C.is(t.connectionStrategy)||O.is(t.messageStrategy)||u.number(t.maxParallelism))}}(M||(t.ConnectionOptions=M={})),function(e){e[e.New=1]="New",e[e.Listening=2]="Listening",e[e.Closed=3]="Closed",e[e.Disposed=4]="Disposed"}(N||(N={}))},4019:(e,t)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.Disposable=void 0,function(e){e.create=function(e){return{dispose:e}}}(n||(t.Disposable=n={}))},2676:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.Emitter=t.Event=void 0;const o=r(n(9590));var i;!function(e){const t={dispose(){}};e.None=function(){return t}}(i||(t.Event=i={}));class s{_callbacks;_contexts;add(e,t=null,n){this._callbacks||(this._callbacks=[],this._contexts=[]),this._callbacks.push(e),this._contexts.push(t),Array.isArray(n)&&n.push({dispose:()=>this.remove(e,t)})}remove(e,t=null){if(!this._callbacks)return;let n=!1;for(let r=0,o=this._callbacks.length;r<o;r++)if(this._callbacks[r]===e){if(this._contexts[r]===t)return this._callbacks.splice(r,1),void this._contexts.splice(r,1);n=!0}if(n)throw new Error("When adding a listener with a context, you should remove it with the same context")}invoke(...e){if(!this._callbacks)return[];const t=[],n=this._callbacks.slice(0),r=this._contexts.slice(0);for(let i=0,s=n.length;i<s;i++)try{t.push(n[i].apply(r[i],e))}catch(e){(0,o.default)().console.error(e)}return t}isEmpty(){return!this._callbacks||0===this._callbacks.length}dispose(){this._callbacks=void 0,this._contexts=void 0}}class a{_options;static _noop=function(){};_event;_callbacks;constructor(e){this._options=e}get event(){return this._event||(this._event=(e,t,n)=>{this._callbacks||(this._callbacks=new s),this._options&&this._options.onFirstListenerAdd&&this._callbacks.isEmpty()&&this._options.onFirstListenerAdd(this),this._callbacks.add(e,t);const r={dispose:()=>{this._callbacks&&(this._callbacks.remove(e,t),r.dispose=a._noop,this._options&&this._options.onLastListenerRemove&&this._callbacks.isEmpty()&&this._options.onLastListenerRemove(this))}};return Array.isArray(n)&&n.push(r),r}),this._event}fire(e){this._callbacks&&this._callbacks.invoke.call(this._callbacks,e)}dispose(){this._callbacks&&(this._callbacks.dispose(),this._callbacks=void 0)}}t.Emitter=a},8585:(e,t)=>{"use strict";function n(e){return"string"==typeof e||e instanceof String}function r(e){return Array.isArray(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.boolean=function(e){return!0===e||!1===e},t.string=n,t.number=function(e){return"number"==typeof e||e instanceof Number},t.error=function(e){return e instanceof Error},t.func=function(e){return"function"==typeof e},t.array=r,t.stringArray=function(e){return r(e)&&e.every((e=>n(e)))}},3352:(e,t)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.LRUCache=t.LinkedMap=t.Touch=void 0,function(e){e.None=0,e.First=1,e.AsOld=e.First,e.Last=2,e.AsNew=e.Last}(n||(t.Touch=n={}));class r{[Symbol.toStringTag]="LinkedMap";_map;_head;_tail;_size;_state;constructor(){this._map=new Map,this._head=void 0,this._tail=void 0,this._size=0,this._state=0}clear(){this._map.clear(),this._head=void 0,this._tail=void 0,this._size=0,this._state++}isEmpty(){return!this._head&&!this._tail}get size(){return this._size}get first(){return this._head?.value}get last(){return this._tail?.value}before(e){const t=this._map.get(e);return t?t.previous?.value:void 0}after(e){const t=this._map.get(e);return t?t.next?.value:void 0}has(e){return this._map.has(e)}get(e,t=n.None){const r=this._map.get(e);if(r)return t!==n.None&&this.touch(r,t),r.value}set(e,t,r=n.None){let o=this._map.get(e);if(o)o.value=t,r!==n.None&&this.touch(o,r);else{switch(o={key:e,value:t,next:void 0,previous:void 0},r){case n.None:this.addItemLast(o);break;case n.First:this.addItemFirst(o);break;case n.Last:default:this.addItemLast(o)}this._map.set(e,o),this._size++}return this}delete(e){return!!this.remove(e)}remove(e){const t=this._map.get(e);if(t)return this._map.delete(e),this.removeItem(t),this._size--,t.value}shift(){if(!this._head&&!this._tail)return;if(!this._head||!this._tail)throw new Error("Invalid list");const e=this._head;return this._map.delete(e.key),this.removeItem(e),this._size--,e.value}forEach(e,t){const n=this._state;let r=this._head;for(;r;){if(t?e.bind(t)(r.value,r.key,this):e(r.value,r.key,this),this._state!==n)throw new Error("LinkedMap got modified during iteration.");r=r.next}}keys(){const e=this._state;let t=this._head;const n={[Symbol.iterator]:()=>n,next:()=>{if(this._state!==e)throw new Error("LinkedMap got modified during iteration.");if(t){const e={value:t.key,done:!1};return t=t.next,e}return{value:void 0,done:!0}}};return n}values(){const e=this._state;let t=this._head;const n={[Symbol.iterator]:()=>n,next:()=>{if(this._state!==e)throw new Error("LinkedMap got modified during iteration.");if(t){const e={value:t.value,done:!1};return t=t.next,e}return{value:void 0,done:!0}}};return n}entries(){const e=this._state;let t=this._head;const n={[Symbol.iterator]:()=>n,next:()=>{if(this._state!==e)throw new Error("LinkedMap got modified during iteration.");if(t){const e={value:[t.key,t.value],done:!1};return t=t.next,e}return{value:void 0,done:!0}}};return n}[Symbol.iterator](){return this.entries()}trimOld(e){if(e>=this.size)return;if(0===e)return void this.clear();let t=this._head,n=this.size;for(;t&&n>e;)this._map.delete(t.key),t=t.next,n--;this._head=t,this._size=n,t&&(t.previous=void 0),this._state++}addItemFirst(e){if(this._head||this._tail){if(!this._head)throw new Error("Invalid list");e.next=this._head,this._head.previous=e}else this._tail=e;this._head=e,this._state++}addItemLast(e){if(this._head||this._tail){if(!this._tail)throw new Error("Invalid list");e.previous=this._tail,this._tail.next=e}else this._head=e;this._tail=e,this._state++}removeItem(e){if(e===this._head&&e===this._tail)this._head=void 0,this._tail=void 0;else if(e===this._head){if(!e.next)throw new Error("Invalid list");e.next.previous=void 0,this._head=e.next}else if(e===this._tail){if(!e.previous)throw new Error("Invalid list");e.previous.next=void 0,this._tail=e.previous}else{const t=e.next,n=e.previous;if(!t||!n)throw new Error("Invalid list");t.previous=n,n.next=t}e.next=void 0,e.previous=void 0,this._state++}touch(e,t){if(!this._head||!this._tail)throw new Error("Invalid list");if(t===n.First||t===n.Last)if(t===n.First){if(e===this._head)return;const t=e.next,n=e.previous;e===this._tail?(n.next=void 0,this._tail=n):(t.previous=n,n.next=t),e.previous=void 0,e.next=this._head,this._head.previous=e,this._head=e,this._state++}else if(t===n.Last){if(e===this._tail)return;const t=e.next,n=e.previous;e===this._head?(t.previous=void 0,this._head=t):(t.previous=n,n.next=t),e.next=void 0,e.previous=this._tail,this._tail.next=e,this._tail=e,this._state++}}toJSON(){const e=[];return this.forEach(((t,n)=>{e.push([n,t])})),e}fromJSON(e){this.clear();for(const[t,n]of e)this.set(t,n)}}t.LinkedMap=r,t.LRUCache=class extends r{_limit;_ratio;constructor(e,t=1){super(),this._limit=e,this._ratio=Math.min(Math.max(0,t),1)}get limit(){return this._limit}set limit(e){this._limit=e,this.checkTrim()}get ratio(){return this._ratio}set ratio(e){this._ratio=Math.min(Math.max(0,e),1),this.checkTrim()}get(e,t=n.AsNew){return super.get(e,t)}peek(e){return super.get(e,n.None)}set(e,t){return super.set(e,t,n.Last),this.checkTrim(),this}checkTrim(){this.size>this._limit&&this.trimOld(Math.round(this._limit*this._ratio))}}},9244:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AbstractMessageBuffer=void 0,t.AbstractMessageBuffer=class{_encoding;_chunks;_totalLength;constructor(e="utf-8"){this._encoding=e,this._chunks=[],this._totalLength=0}get encoding(){return this._encoding}append(e){const t="string"==typeof e?this.fromString(e,this._encoding):e;this._chunks.push(t),this._totalLength+=t.byteLength}tryReadHeaders(e=!1){if(0===this._chunks.length)return;let t=0,n=0,r=0,o=0;e:for(;n<this._chunks.length;){const e=this._chunks[n];for(r=0;r<e.length;){switch(e[r]){case 13:switch(t){case 0:t=1;break;case 2:t=3;break;default:t=0}break;case 10:switch(t){case 1:t=2;break;case 3:t=4,r++;break e;default:t=0}break;default:t=0}r++}o+=e.byteLength,n++}if(4!==t)return;const i=this._read(o+r),s=new Map,a=this.toString(i,"ascii").split("\r\n");if(a.length<2)return s;for(let t=0;t<a.length-2;t++){const n=a[t],r=n.indexOf(":");if(-1===r)throw new Error(`Message header must separate key and value using ':'\n${n}`);const o=n.substr(0,r),i=n.substr(r+1).trim();s.set(e?o.toLowerCase():o,i)}return s}tryReadBody(e){if(!(this._totalLength<e))return this._read(e)}get numberOfBytes(){return this._totalLength}_read(e){if(0===e)return this.emptyBuffer();if(e>this._totalLength)throw new Error("Cannot read so many bytes!");if(this._chunks[0].byteLength===e){const t=this._chunks[0];return this._chunks.shift(),this._totalLength-=e,this.asNative(t)}if(this._chunks[0].byteLength>e){const t=this._chunks[0],n=this.asNative(t,e);return this._chunks[0]=t.slice(e),this._totalLength-=e,n}const t=this.allocNative(e);let n=0;for(;e>0;){const r=this._chunks[0];if(r.byteLength>e){const o=r.slice(0,e);t.set(o,n),n+=e,this._chunks[0]=r.slice(e),this._totalLength-=e,e-=e}else t.set(r,n),n+=r.byteLength,this._chunks.shift(),this._totalLength-=r.byteLength,e-=r.byteLength}return t}}},9085:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t}),a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.ReadableStreamMessageReader=t.AbstractMessageReader=t.MessageReader=void 0;const c=a(n(9590)),u=s(n(8585)),l=n(2676),d=n(4323);var f,h;!function(e){e.is=function(e){const t=e;return t&&u.func(t.listen)&&u.func(t.dispose)&&u.func(t.onError)&&u.func(t.onClose)&&u.func(t.onPartialMessage)}}(f||(t.MessageReader=f={}));class p{errorEmitter;closeEmitter;partialMessageEmitter;constructor(){this.errorEmitter=new l.Emitter,this.closeEmitter=new l.Emitter,this.partialMessageEmitter=new l.Emitter}dispose(){this.errorEmitter.dispose(),this.closeEmitter.dispose(),this.partialMessageEmitter.dispose()}get onError(){return this.errorEmitter.event}fireError(e){this.errorEmitter.fire(this.asError(e))}get onClose(){return this.closeEmitter.event}fireClose(){this.closeEmitter.fire(void 0)}get onPartialMessage(){return this.partialMessageEmitter.event}firePartialMessage(e){this.partialMessageEmitter.fire(e)}asError(e){return e instanceof Error?e:new Error(`Reader received error. Reason: ${u.string(e.message)?e.message:"unknown"}`)}}t.AbstractMessageReader=p,function(e){e.fromOptions=function(e){let t,n;const r=new Map;let o;const i=new Map;if(void 0===e||"string"==typeof e)t=e??"utf-8";else{if(t=e.charset??"utf-8",void 0!==e.contentDecoder&&(n=e.contentDecoder,r.set(n.name,n)),void 0!==e.contentDecoders)for(const t of e.contentDecoders)r.set(t.name,t);if(void 0!==e.contentTypeDecoder&&(o=e.contentTypeDecoder,i.set(o.name,o)),void 0!==e.contentTypeDecoders)for(const t of e.contentTypeDecoders)i.set(t.name,t)}return void 0===o&&(o=(0,c.default)().applicationJson.decoder,i.set(o.name,o)),{charset:t,contentDecoder:n,contentDecoders:r,contentTypeDecoder:o,contentTypeDecoders:i}}}(h||(h={})),t.ReadableStreamMessageReader=class extends p{readable;options;callback;nextMessageLength;messageToken;buffer;partialMessageTimer;_partialMessageTimeout;readSemaphore;constructor(e,t){super(),this.readable=e,this.options=h.fromOptions(t),this.buffer=(0,c.default)().messageBuffer.create(this.options.charset),this._partialMessageTimeout=1e4,this.nextMessageLength=-1,this.messageToken=0,this.readSemaphore=new d.Semaphore(1)}set partialMessageTimeout(e){this._partialMessageTimeout=e}get partialMessageTimeout(){return this._partialMessageTimeout}listen(e){this.nextMessageLength=-1,this.messageToken=0,this.partialMessageTimer=void 0,this.callback=e;const t=this.readable.onData((e=>{this.onData(e)}));return this.readable.onError((e=>this.fireError(e))),this.readable.onClose((()=>this.fireClose())),t}onData(e){try{for(this.buffer.append(e);;){if(-1===this.nextMessageLength){const e=this.buffer.tryReadHeaders(!0);if(!e)return;const t=e.get("content-length");if(!t)return void this.fireError(new Error(`Header must provide a Content-Length property.\n${JSON.stringify(Object.fromEntries(e))}`));const n=parseInt(t);if(isNaN(n))return void this.fireError(new Error(`Content-Length value must be a number. Got ${t}`));this.nextMessageLength=n}const e=this.buffer.tryReadBody(this.nextMessageLength);if(void 0===e)return void this.setPartialMessageTimer();this.clearPartialMessageTimer(),this.nextMessageLength=-1,this.readSemaphore.lock((async()=>{const t=void 0!==this.options.contentDecoder?await this.options.contentDecoder.decode(e):e,n=await this.options.contentTypeDecoder.decode(t,this.options);this.callback(n)})).catch((e=>{this.fireError(e)}))}}catch(e){this.fireError(e)}}clearPartialMessageTimer(){this.partialMessageTimer&&(this.partialMessageTimer.dispose(),this.partialMessageTimer=void 0)}setPartialMessageTimer(){this.clearPartialMessageTimer(),this._partialMessageTimeout<=0||(this.partialMessageTimer=(0,c.default)().timer.setTimeout(((e,t)=>{this.partialMessageTimer=void 0,e===this.messageToken&&(this.firePartialMessage({messageToken:e,waitingTime:t}),this.setPartialMessageTimer())}),this._partialMessageTimeout,this.messageToken,this._partialMessageTimeout))}}},3193:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t}),a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.WriteableStreamMessageWriter=t.AbstractMessageWriter=t.MessageWriter=void 0;const c=a(n(9590)),u=s(n(8585)),l=n(4323),d=n(2676);var f,h;!function(e){e.is=function(e){const t=e;return t&&u.func(t.dispose)&&u.func(t.onClose)&&u.func(t.onError)&&u.func(t.write)}}(f||(t.MessageWriter=f={}));class p{errorEmitter;closeEmitter;constructor(){this.errorEmitter=new d.Emitter,this.closeEmitter=new d.Emitter}dispose(){this.errorEmitter.dispose(),this.closeEmitter.dispose()}get onError(){return this.errorEmitter.event}fireError(e,t,n){this.errorEmitter.fire([this.asError(e),t,n])}get onClose(){return this.closeEmitter.event}fireClose(){this.closeEmitter.fire(void 0)}asError(e){return e instanceof Error?e:new Error(`Writer received error. Reason: ${u.string(e.message)?e.message:"unknown"}`)}}t.AbstractMessageWriter=p,function(e){e.fromOptions=function(e){return void 0===e||"string"==typeof e?{charset:e??"utf-8",contentTypeEncoder:(0,c.default)().applicationJson.encoder}:{charset:e.charset??"utf-8",contentEncoder:e.contentEncoder,contentTypeEncoder:e.contentTypeEncoder??(0,c.default)().applicationJson.encoder}}}(h||(h={})),t.WriteableStreamMessageWriter=class extends p{writable;options;errorCount;writeSemaphore;constructor(e,t){super(),this.writable=e,this.options=h.fromOptions(t),this.errorCount=0,this.writeSemaphore=new l.Semaphore(1),this.writable.onError((e=>this.fireError(e))),this.writable.onClose((()=>this.fireClose()))}async write(e){return this.writeSemaphore.lock((async()=>this.options.contentTypeEncoder.encode(e,this.options).then((e=>void 0!==this.options.contentEncoder?this.options.contentEncoder.encode(e):e)).then((t=>{const n=[];return n.push("Content-Length: ",t.byteLength.toString(),"\r\n"),n.push("\r\n"),this.doWrite(e,n,t)}),(e=>{throw this.fireError(e),e}))))}async doWrite(e,t,n){try{return await this.writable.write(t.join(""),"ascii"),this.writable.write(n)}catch(t){return this.handleError(t,e),Promise.reject(t)}}handleError(e,t){this.errorCount++,this.fireError(e,t,this.errorCount)}end(){this.writable.end()}}},6177:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.Message=t.NotificationType9=t.NotificationType8=t.NotificationType7=t.NotificationType6=t.NotificationType5=t.NotificationType4=t.NotificationType3=t.NotificationType2=t.NotificationType1=t.NotificationType0=t.NotificationType=t.RequestType9=t.RequestType8=t.RequestType7=t.RequestType6=t.RequestType5=t.RequestType4=t.RequestType3=t.RequestType2=t.RequestType1=t.RequestType=t.RequestType0=t.AbstractMessageSignature=t.ParameterStructures=t.ResponseError=t.ErrorCodes=void 0;const a=s(n(8585));var c,u;!function(e){e.ParseError=-32700,e.InvalidRequest=-32600,e.MethodNotFound=-32601,e.InvalidParams=-32602,e.InternalError=-32603,e.jsonrpcReservedErrorRangeStart=-32099,e.serverErrorStart=-32099,e.MessageWriteError=-32099,e.MessageReadError=-32098,e.PendingResponseRejected=-32097,e.ConnectionInactive=-32096,e.ServerNotInitialized=-32002,e.UnknownErrorCode=-32001,e.jsonrpcReservedErrorRangeEnd=-32e3,e.serverErrorEnd=-32e3}(c||(t.ErrorCodes=c={}));class l extends Error{code;data;constructor(e,t,n){super(t),this.code=a.number(e)?e:c.UnknownErrorCode,this.data=n,Object.setPrototypeOf(this,l.prototype)}toJson(){const e={code:this.code,message:this.message};return void 0!==this.data&&(e.data=this.data),e}}t.ResponseError=l;class d{kind;static auto=new d("auto");static byPosition=new d("byPosition");static byName=new d("byName");constructor(e){this.kind=e}static is(e){return e===d.auto||e===d.byName||e===d.byPosition}toString(){return this.kind}}t.ParameterStructures=d;class f{method;numberOfParams;constructor(e,t){this.method=e,this.numberOfParams=t}get parameterStructures(){return d.auto}}t.AbstractMessageSignature=f,t.RequestType0=class extends f{_;constructor(e){super(e,0)}},t.RequestType=class extends f{_parameterStructures;_;constructor(e,t=d.auto){super(e,1),this._parameterStructures=t}get parameterStructures(){return this._parameterStructures}},t.RequestType1=class extends f{_parameterStructures;_;constructor(e,t=d.auto){super(e,1),this._parameterStructures=t}get parameterStructures(){return this._parameterStructures}},t.RequestType2=class extends f{_;constructor(e){super(e,2)}},t.RequestType3=class extends f{_;constructor(e){super(e,3)}},t.RequestType4=class extends f{_;constructor(e){super(e,4)}},t.RequestType5=class extends f{_;constructor(e){super(e,5)}},t.RequestType6=class extends f{_;constructor(e){super(e,6)}},t.RequestType7=class extends f{_;constructor(e){super(e,7)}},t.RequestType8=class extends f{_;constructor(e){super(e,8)}},t.RequestType9=class extends f{_;constructor(e){super(e,9)}},t.NotificationType=class extends f{_parameterStructures;_;constructor(e,t=d.auto){super(e,1),this._parameterStructures=t}get parameterStructures(){return this._parameterStructures}},t.NotificationType0=class extends f{_;constructor(e){super(e,0)}},t.NotificationType1=class extends f{_parameterStructures;_;constructor(e,t=d.auto){super(e,1),this._parameterStructures=t}get parameterStructures(){return this._parameterStructures}},t.NotificationType2=class extends f{_;constructor(e){super(e,2)}},t.NotificationType3=class extends f{_;constructor(e){super(e,3)}},t.NotificationType4=class extends f{_;constructor(e){super(e,4)}},t.NotificationType5=class extends f{_;constructor(e){super(e,5)}},t.NotificationType6=class extends f{_;constructor(e){super(e,6)}},t.NotificationType7=class extends f{_;constructor(e){super(e,7)}},t.NotificationType8=class extends f{_;constructor(e){super(e,8)}},t.NotificationType9=class extends f{_;constructor(e){super(e,9)}},function(e){e.isRequest=function(e){const t=e;return t&&a.string(t.method)&&(a.string(t.id)||a.number(t.id))},e.isNotification=function(e){const t=e;return t&&a.string(t.method)&&void 0===e.id},e.isResponse=function(e){const t=e;return t&&(void 0!==t.result||!!t.error)&&(a.string(t.id)||a.number(t.id)||null===t.id)}}(u||(t.Message=u={}))},9590:(e,t)=>{"use strict";let n;function r(){if(void 0===n)throw new Error("No runtime abstraction layer installed");return n}Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.install=function(e){if(void 0===e)throw new Error("No runtime abstraction layer provided");n=e}}(r||(r={})),t.default=r},4323:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.Semaphore=void 0;const o=r(n(9590));t.Semaphore=class{_capacity;_active;_waiting;constructor(e=1){if(e<=0)throw new Error("Capacity must be greater than 0");this._capacity=e,this._active=0,this._waiting=[]}lock(e){return new Promise(((t,n)=>{this._waiting.push({thunk:e,resolve:t,reject:n}),this.runNext()}))}get active(){return this._active}runNext(){0!==this._waiting.length&&this._active!==this._capacity&&(0,o.default)().timer.setImmediate((()=>this.doRunNext()))}doRunNext(){if(0===this._waiting.length||this._active===this._capacity)return;const e=this._waiting.shift();if(this._active++,this._active>this._capacity)throw new Error("Too many thunks active");try{const t=e.thunk();t instanceof Promise?t.then((t=>{this._active--,e.resolve(t),this.runNext()}),(t=>{this._active--,e.reject(t),this.runNext()})):(this._active--,e.resolve(t),this.runNext())}catch(t){this._active--,e.reject(t),this.runNext()}}}},4996:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SharedArrayReceiverStrategy=t.SharedArraySenderStrategy=void 0;const r=n(9850);var o;!function(e){e.Continue=0,e.Cancelled=1}(o||(o={})),t.SharedArraySenderStrategy=class{buffers;constructor(){this.buffers=new Map}enableCancellation(e){if(null===e.id)return;const t=new SharedArrayBuffer(4);new Int32Array(t,0,1)[0]=o.Continue,this.buffers.set(e.id,t),e.$cancellationData=t}async sendCancellation(e,t){const n=this.buffers.get(t);if(void 0===n)return;const r=new Int32Array(n,0,1);Atomics.store(r,0,o.Cancelled)}cleanup(e){this.buffers.delete(e)}dispose(){this.buffers.clear()}};class i{data;constructor(e){this.data=new Int32Array(e,0,1)}get isCancellationRequested(){return Atomics.load(this.data,0)===o.Cancelled}get onCancellationRequested(){throw new Error("Cancellation over SharedArrayBuffer doesn't support cancellation events")}}class s{token;constructor(e){this.token=new i(e)}cancel(){}dispose(){}}t.SharedArrayReceiverStrategy=class{kind="request";createCancellationTokenSource(e){const t=e.$cancellationData;return void 0===t?new r.CancellationTokenSource:new s(t)}}},7123:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t}),a=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||o(t,e,n)},c=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.StreamMessageWriter=t.StreamMessageReader=t.SocketMessageWriter=t.SocketMessageReader=t.PortMessageWriter=t.PortMessageReader=t.IPCMessageWriter=t.IPCMessageReader=void 0,t.generateRandomPipeName=function(){if("win32"===process.platform)return`\\\\.\\pipe\\lsp-${(0,h.randomBytes)(16).toString("hex")}-sock`;let e=32;const t=f.realpathSync(C??d.tmpdir()),n=R.get(process.platform);if(void 0!==n&&(e=Math.min(n-t.length-9,e)),e<16)throw new Error(`Unable to generate a random pipe name with ${e} characters.`);const r=(0,h.randomBytes)(Math.floor(e/2)).toString("hex");return l.join(t,`lsp-${r}.sock`)},t.createClientPipeTransport=function(e,t="utf-8"){let n;const r=new Promise(((e,t)=>{n=e}));return new Promise(((o,i)=>{const s=(0,p.createServer)((e=>{s.close(),n([new k(e,t),new _(e,t)])}));s.on("error",i),s.listen(e,(()=>{s.removeListener("error",i),o({onConnected:()=>r})}))}))},t.createServerPipeTransport=function(e,t="utf-8"){const n=(0,p.createConnection)(e);return[new k(n,t),new _(n,t)]},t.createClientSocketTransport=function(e,t="utf-8"){let n;const r=new Promise(((e,t)=>{n=e}));return new Promise(((o,i)=>{const s=(0,p.createServer)((e=>{s.close(),n([new k(e,t),new _(e,t)])}));s.on("error",i),s.listen(e,"127.0.0.1",(()=>{s.removeListener("error",i),o({onConnected:()=>r})}))}))},t.createServerSocketTransport=function(e,t="utf-8"){const n=(0,p.createConnection)(e,"127.0.0.1");return[new k(n,t),new _(n,t)]},t.createMessageConnection=function(e,t,n,r){n||(n=m.NullLogger);const o=function(e){const t=e;return void 0!==t.read&&void 0!==t.addListener}(e)?new T(e):e,i=function(e){const t=e;return void 0!==t.write&&void 0!==t.addListener}(t)?new S(t):t;return m.ConnectionStrategy.is(r)&&(r={connectionStrategy:r}),(0,m.createMessageConnection)(o,i,n,r)};const u=c(n(9571));u.default.install();const l=s(n(6928)),d=s(n(857)),f=s(n(9896)),h=n(6982),p=n(9278),m=n(3281);a(n(3281),t);class g extends m.AbstractMessageReader{process;constructor(e){super(),this.process=e;const t=this.process;t.on("error",(e=>this.fireError(e))),t.on("close",(()=>this.fireClose()))}listen(e){return this.process.on("message",e),m.Disposable.create((()=>this.process.off("message",e)))}}t.IPCMessageReader=g;class y extends m.AbstractMessageWriter{process;errorCount;constructor(e){super(),this.process=e,this.errorCount=0;const t=this.process;t.on("error",(e=>this.fireError(e))),t.on("close",(()=>this.fireClose))}write(e){try{return"function"==typeof this.process.send&&this.process.send(e,void 0,void 0,(t=>{t?(this.errorCount++,this.handleError(t,e)):this.errorCount=0})),Promise.resolve()}catch(t){return this.handleError(t,e),Promise.reject(t)}}handleError(e,t){this.errorCount++,this.fireError(e,t,this.errorCount)}end(){}}t.IPCMessageWriter=y;class b extends m.AbstractMessageReader{onData;constructor(e){super(),this.onData=new m.Emitter,e.on("close",(()=>this.fireClose)),e.on("error",(e=>this.fireError(e))),e.on("message",(e=>{this.onData.fire(e)}))}listen(e){return this.onData.event(e)}}t.PortMessageReader=b;class v extends m.AbstractMessageWriter{port;errorCount;constructor(e){super(),this.port=e,this.errorCount=0,e.on("close",(()=>this.fireClose())),e.on("error",(e=>this.fireError(e)))}write(e){try{return this.port.postMessage(e),Promise.resolve()}catch(t){return this.handleError(t,e),Promise.reject(t)}}handleError(e,t){this.errorCount++,this.fireError(e,t,this.errorCount)}end(){}}t.PortMessageWriter=v;class k extends m.ReadableStreamMessageReader{constructor(e,t="utf-8"){super((0,u.default)().stream.asReadableStream(e),t)}}t.SocketMessageReader=k;class _ extends m.WriteableStreamMessageWriter{socket;constructor(e,t){super((0,u.default)().stream.asWritableStream(e),t),this.socket=e}dispose(){super.dispose(),this.socket.destroy()}}t.SocketMessageWriter=_;class T extends m.ReadableStreamMessageReader{constructor(e,t){super((0,u.default)().stream.asReadableStream(e),t)}}t.StreamMessageReader=T;class S extends m.WriteableStreamMessageWriter{constructor(e,t){super((0,u.default)().stream.asWritableStream(e),t)}}t.StreamMessageWriter=S;const C=process.env.XDG_RUNTIME_DIR,R=new Map([["linux",107],["darwin",103]])},9571:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(9023),o=n(3281);class i extends o.AbstractMessageBuffer{static emptyBuffer=Buffer.allocUnsafe(0);constructor(e="utf-8"){super(e)}emptyBuffer(){return i.emptyBuffer}fromString(e,t){return Buffer.from(e,t)}toString(e,t){return e instanceof Buffer?e.toString(t):new r.TextDecoder(t).decode(e)}asNative(e,t){return void 0===t?e instanceof Buffer?e:Buffer.from(e):e instanceof Buffer?e.slice(0,t):Buffer.from(e,0,t)}allocNative(e){return Buffer.allocUnsafe(e)}}class s{stream;constructor(e){this.stream=e}onClose(e){return this.stream.on("close",e),o.Disposable.create((()=>this.stream.off("close",e)))}onError(e){return this.stream.on("error",e),o.Disposable.create((()=>this.stream.off("error",e)))}onEnd(e){return this.stream.on("end",e),o.Disposable.create((()=>this.stream.off("end",e)))}onData(e){return this.stream.on("data",e),o.Disposable.create((()=>this.stream.off("data",e)))}}class a{stream;constructor(e){this.stream=e}onClose(e){return this.stream.on("close",e),o.Disposable.create((()=>this.stream.off("close",e)))}onError(e){return this.stream.on("error",e),o.Disposable.create((()=>this.stream.off("error",e)))}onEnd(e){return this.stream.on("end",e),o.Disposable.create((()=>this.stream.off("end",e)))}write(e,t){return new Promise(((n,r)=>{const o=e=>{null==e?n():r(e)};"string"==typeof e?this.stream.write(e,t,o):this.stream.write(e,o)}))}end(){this.stream.end()}}const c=Object.freeze({messageBuffer:Object.freeze({create:e=>new i(e)}),applicationJson:Object.freeze({encoder:Object.freeze({name:"application/json",encode:(e,t)=>{try{return Promise.resolve(Buffer.from(JSON.stringify(e,void 0,0),t.charset))}catch(e){return Promise.reject(e)}}}),decoder:Object.freeze({name:"application/json",decode:(e,t)=>{try{return e instanceof Buffer?Promise.resolve(JSON.parse(e.toString(t.charset))):Promise.resolve(JSON.parse(new r.TextDecoder(t.charset).decode(e)))}catch(e){return Promise.reject(e)}}})}),stream:Object.freeze({asReadableStream:e=>new s(e),asWritableStream:e=>new a(e)}),console,timer:Object.freeze({setTimeout(e,t,...n){const r=setTimeout(e,t,...n);return{dispose:()=>clearTimeout(r)}},setImmediate(e,...t){const n=setImmediate(e,...t);return{dispose:()=>clearImmediate(n)}},setInterval(e,t,...n){const r=setInterval(e,t,...n);return{dispose:()=>clearInterval(r)}}})});function u(){return c}!function(e){e.install=function(){o.RAL.install(c)}}(u||(u={})),t.default=u},8766:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.LSPErrorCodes=t.createProtocolConnection=void 0,o(n(3281),t),o(n(4292),t),o(n(372),t),o(n(1560),t);var i,s=n(1580);Object.defineProperty(t,"createProtocolConnection",{enumerable:!0,get:function(){return s.createProtocolConnection}}),function(e){e.lspReservedErrorRangeStart=-32899,e.RequestFailed=-32803,e.ServerCancelled=-32802,e.ContentModified=-32801,e.RequestCancelled=-32800,e.lspReservedErrorRangeEnd=-32800}(i||(t.LSPErrorCodes=i={}))},1580:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createProtocolConnection=function(e,t,n,o){return r.ConnectionStrategy.is(o)&&(o={connectionStrategy:o}),(0,r.createMessageConnection)(e,t,n,o)};const r=n(3281)},372:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CM=t.ProtocolNotificationType=t.ProtocolNotificationType0=t.ProtocolRequestType=t.ProtocolRequestType0=t.RegistrationType=t.MessageDirection=void 0;const r=n(3281);var o,i;!function(e){e.clientToServer="clientToServer",e.serverToClient="serverToClient",e.both="both"}(o||(t.MessageDirection=o={})),t.RegistrationType=class{____;method;constructor(e){this.method=e}};class s extends r.RequestType0{__;___;____;_pr;constructor(e){super(e)}}t.ProtocolRequestType0=s;class a extends r.RequestType{__;___;____;_pr;constructor(e){super(e,r.ParameterStructures.byName)}}t.ProtocolRequestType=a;class c extends r.NotificationType0{___;____;constructor(e){super(e)}}t.ProtocolNotificationType0=c;class u extends r.NotificationType{___;____;constructor(e){super(e,r.ParameterStructures.byName)}}t.ProtocolNotificationType=u,function(e){e.create=function(e,t){return{client:e,server:t}}}(i||(t.CM=i={}))},8765:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CallHierarchyOutgoingCallsRequest=t.CallHierarchyIncomingCallsRequest=t.CallHierarchyPrepareRequest=void 0;const r=n(372);var o,i,s;!function(e){e.method="textDocument/prepareCallHierarchy",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolRequestType(e.method),e.capabilities=r.CM.create("textDocument.callHierarchy","callHierarchyProvider")}(o||(t.CallHierarchyPrepareRequest=o={})),function(e){e.method="callHierarchy/incomingCalls",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolRequestType(e.method)}(i||(t.CallHierarchyIncomingCallsRequest=i={})),function(e){e.method="callHierarchy/outgoingCalls",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolRequestType(e.method)}(s||(t.CallHierarchyOutgoingCallsRequest=s={}))},7672:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ColorPresentationRequest=t.DocumentColorRequest=void 0;const r=n(372);var o,i;!function(e){e.method="textDocument/documentColor",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolRequestType(e.method),e.capabilities=r.CM.create("textDocument.colorProvider","colorProvider")}(o||(t.DocumentColorRequest=o={})),function(e){e.method="textDocument/colorPresentation",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolRequestType(e.method)}(i||(t.ColorPresentationRequest=i={}))},1660:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ConfigurationRequest=void 0;const r=n(372);var o;!function(e){e.method="workspace/configuration",e.messageDirection=r.MessageDirection.serverToClient,e.type=new r.ProtocolRequestType(e.method),e.capabilities=r.CM.create("workspace.configuration",void 0)}(o||(t.ConfigurationRequest=o={}))},6914:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DeclarationRequest=void 0;const r=n(372);var o;!function(e){e.method="textDocument/declaration",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolRequestType(e.method),e.capabilities=r.CM.create("textDocument.declaration","declarationProvider")}(o||(t.DeclarationRequest=o={}))},6011:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.DiagnosticRefreshRequest=t.WorkspaceDiagnosticRequest=t.DocumentDiagnosticRequest=t.DocumentDiagnosticReportKind=t.DiagnosticServerCancellationData=void 0;const a=n(3281),c=s(n(8598)),u=n(372);var l,d,f,h,p;!function(e){e.is=function(e){const t=e;return t&&c.boolean(t.retriggerRequest)}}(l||(t.DiagnosticServerCancellationData=l={})),function(e){e.Full="full",e.Unchanged="unchanged"}(d||(t.DocumentDiagnosticReportKind=d={})),function(e){e.method="textDocument/diagnostic",e.messageDirection=u.MessageDirection.clientToServer,e.type=new u.ProtocolRequestType(e.method),e.partialResult=new a.ProgressType,e.capabilities=u.CM.create("textDocument.diagnostic","diagnosticProvider")}(f||(t.DocumentDiagnosticRequest=f={})),function(e){e.method="workspace/diagnostic",e.messageDirection=u.MessageDirection.clientToServer,e.type=new u.ProtocolRequestType(e.method),e.partialResult=new a.ProgressType,e.capabilities=u.CM.create("workspace.diagnostics","diagnosticProvider.workspaceDiagnostics")}(h||(t.WorkspaceDiagnosticRequest=h={})),function(e){e.method="workspace/diagnostic/refresh",e.messageDirection=u.MessageDirection.serverToClient,e.type=new u.ProtocolRequestType0(e.method),e.capabilities=u.CM.create("workspace.diagnostics.refreshSupport",void 0)}(p||(t.DiagnosticRefreshRequest=p={}))},9840:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WillDeleteFilesRequest=t.DidDeleteFilesNotification=t.DidRenameFilesNotification=t.WillRenameFilesRequest=t.DidCreateFilesNotification=t.WillCreateFilesRequest=t.FileOperationPatternKind=void 0;const r=n(372);var o,i,s,a,c,u,l;!function(e){e.file="file",e.folder="folder"}(o||(t.FileOperationPatternKind=o={})),function(e){e.method="workspace/willCreateFiles",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolRequestType(e.method),e.capabilities=r.CM.create("workspace.fileOperations.willCreate","workspace.fileOperations.willCreate")}(i||(t.WillCreateFilesRequest=i={})),function(e){e.method="workspace/didCreateFiles",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolNotificationType(e.method),e.capabilities=r.CM.create("workspace.fileOperations.didCreate","workspace.fileOperations.didCreate")}(s||(t.DidCreateFilesNotification=s={})),function(e){e.method="workspace/willRenameFiles",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolRequestType(e.method),e.capabilities=r.CM.create("workspace.fileOperations.willRename","workspace.fileOperations.willRename")}(a||(t.WillRenameFilesRequest=a={})),function(e){e.method="workspace/didRenameFiles",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolNotificationType(e.method),e.capabilities=r.CM.create("workspace.fileOperations.didRename","workspace.fileOperations.didRename")}(c||(t.DidRenameFilesNotification=c={})),function(e){e.method="workspace/didDeleteFiles",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolNotificationType(e.method),e.capabilities=r.CM.create("workspace.fileOperations.didDelete","workspace.fileOperations.didDelete")}(u||(t.DidDeleteFilesNotification=u={})),function(e){e.method="workspace/willDeleteFiles",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolRequestType(e.method),e.capabilities=r.CM.create("workspace.fileOperations.willDelete","workspace.fileOperations.willDelete")}(l||(t.WillDeleteFilesRequest=l={}))},2874:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FoldingRangeRefreshRequest=t.FoldingRangeRequest=void 0;const r=n(372);var o,i;!function(e){e.method="textDocument/foldingRange",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolRequestType(e.method),e.capabilities=r.CM.create("textDocument.foldingRange","foldingRangeProvider")}(o||(t.FoldingRangeRequest=o={})),function(e){e.method="workspace/foldingRange/refresh",e.messageDirection=r.MessageDirection.serverToClient,e.type=new r.ProtocolRequestType0(e.method),e.capabilities=r.CM.create("workspace.foldingRange.refreshSupport",void 0)}(i||(t.FoldingRangeRefreshRequest=i={}))},9574:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ImplementationRequest=void 0;const r=n(372);var o;!function(e){e.method="textDocument/implementation",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolRequestType(e.method),e.capabilities=r.CM.create("textDocument.implementation","implementationProvider")}(o||(t.ImplementationRequest=o={}))},7752:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.InlayHintRefreshRequest=t.InlayHintResolveRequest=t.InlayHintRequest=void 0;const r=n(372);var o,i,s;!function(e){e.method="textDocument/inlayHint",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolRequestType(e.method),e.capabilities=r.CM.create("textDocument.inlayHint","inlayHintProvider")}(o||(t.InlayHintRequest=o={})),function(e){e.method="inlayHint/resolve",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolRequestType(e.method),e.capabilities=r.CM.create("textDocument.inlayHint.resolveSupport","inlayHintProvider.resolveProvider")}(i||(t.InlayHintResolveRequest=i={})),function(e){e.method="workspace/inlayHint/refresh",e.messageDirection=r.MessageDirection.serverToClient,e.type=new r.ProtocolRequestType0(e.method),e.capabilities=r.CM.create("workspace.inlayHint.refreshSupport",void 0)}(s||(t.InlayHintRefreshRequest=s={}))},3307:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.InlineCompletionRequest=void 0;const r=n(372);var o;!function(e){e.method="textDocument/inlineCompletion",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolRequestType(e.method),e.capabilities=r.CM.create("textDocument.inlineCompletion","inlineCompletionProvider")}(o||(t.InlineCompletionRequest=o={}))},3124:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.InlineValueRefreshRequest=t.InlineValueRequest=void 0;const r=n(372);var o,i;!function(e){e.method="textDocument/inlineValue",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolRequestType(e.method),e.capabilities=r.CM.create("textDocument.inlineValue","inlineValueProvider")}(o||(t.InlineValueRequest=o={})),function(e){e.method="workspace/inlineValue/refresh",e.messageDirection=r.MessageDirection.serverToClient,e.type=new r.ProtocolRequestType0(e.method),e.capabilities=r.CM.create("workspace.inlineValue.refreshSupport",void 0)}(i||(t.InlineValueRefreshRequest=i={}))},1560:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.CodeActionRequest=t.DocumentSymbolRequest=t.DocumentHighlightRequest=t.ReferencesRequest=t.DefinitionRequest=t.SignatureHelpRequest=t.SignatureHelpTriggerKind=t.HoverRequest=t.CompletionResolveRequest=t.CompletionRequest=t.CompletionTriggerKind=t.PublishDiagnosticsNotification=t.WatchKind=t.GlobPattern=t.RelativePattern=t.FileChangeType=t.DidChangeWatchedFilesNotification=t.WillSaveTextDocumentWaitUntilRequest=t.WillSaveTextDocumentNotification=t.TextDocumentSaveReason=t.DidSaveTextDocumentNotification=t.DidCloseTextDocumentNotification=t.DidChangeTextDocumentNotification=t.TextDocumentContentChangeEvent=t.DidOpenTextDocumentNotification=t.TextDocumentSyncKind=t.TelemetryEventNotification=t.LogMessageNotification=t.ShowMessageRequest=t.ShowMessageNotification=t.MessageType=t.DidChangeConfigurationNotification=t.ExitNotification=t.ShutdownRequest=t.InitializedNotification=t.InitializeErrorCodes=t.InitializeRequest=t.WorkDoneProgressOptions=t.TextDocumentRegistrationOptions=t.StaticRegistrationOptions=t.PositionEncodingKind=t.RegularExpressionEngineKind=t.FailureHandlingKind=t.ResourceOperationKind=t.UnregistrationRequest=t.RegistrationRequest=t.DocumentSelector=t.NotebookCellTextDocumentFilter=t.NotebookDocumentFilter=t.TextDocumentFilter=void 0,t.UniquenessLevel=t.WillDeleteFilesRequest=t.DidDeleteFilesNotification=t.WillRenameFilesRequest=t.DidRenameFilesNotification=t.WillCreateFilesRequest=t.DidCreateFilesNotification=t.FileOperationPatternKind=t.LinkedEditingRangeRequest=t.ShowDocumentRequest=t.SemanticTokensRegistrationType=t.SemanticTokensRefreshRequest=t.SemanticTokensRangeRequest=t.SemanticTokensDeltaRequest=t.SemanticTokensRequest=t.TokenFormat=t.CallHierarchyPrepareRequest=t.CallHierarchyOutgoingCallsRequest=t.CallHierarchyIncomingCallsRequest=t.WorkDoneProgressCancelNotification=t.WorkDoneProgressCreateRequest=t.WorkDoneProgress=t.SelectionRangeRequest=t.DeclarationRequest=t.FoldingRangeRefreshRequest=t.FoldingRangeRequest=t.ColorPresentationRequest=t.DocumentColorRequest=t.ConfigurationRequest=t.DidChangeWorkspaceFoldersNotification=t.WorkspaceFoldersRequest=t.TypeDefinitionRequest=t.ImplementationRequest=t.ApplyWorkspaceEditRequest=t.ExecuteCommandRequest=t.PrepareRenameRequest=t.RenameRequest=t.PrepareSupportDefaultBehavior=t.DocumentOnTypeFormattingRequest=t.DocumentRangesFormattingRequest=t.DocumentRangeFormattingRequest=t.DocumentFormattingRequest=t.DocumentLinkResolveRequest=t.DocumentLinkRequest=t.CodeLensRefreshRequest=t.CodeLensResolveRequest=t.CodeLensRequest=t.WorkspaceSymbolResolveRequest=t.WorkspaceSymbolRequest=t.CodeActionResolveRequest=void 0,t.TextDocumentContentRefreshRequest=t.TextDocumentContentRequest=t.InlineCompletionRequest=t.DidCloseNotebookDocumentNotification=t.DidSaveNotebookDocumentNotification=t.DidChangeNotebookDocumentNotification=t.NotebookCellArrayChange=t.DidOpenNotebookDocumentNotification=t.NotebookDocumentSyncRegistrationType=t.NotebookDocument=t.NotebookCell=t.ExecutionSummary=t.NotebookCellKind=t.DiagnosticRefreshRequest=t.WorkspaceDiagnosticRequest=t.DocumentDiagnosticRequest=t.DocumentDiagnosticReportKind=t.DiagnosticServerCancellationData=t.InlayHintRefreshRequest=t.InlayHintResolveRequest=t.InlayHintRequest=t.InlineValueRefreshRequest=t.InlineValueRequest=t.TypeHierarchySupertypesRequest=t.TypeHierarchySubtypesRequest=t.TypeHierarchyPrepareRequest=t.MonikerRequest=t.MonikerKind=void 0;const a=n(372),c=n(4292),u=s(n(8598)),l=n(9574);Object.defineProperty(t,"ImplementationRequest",{enumerable:!0,get:function(){return l.ImplementationRequest}});const d=n(8461);Object.defineProperty(t,"TypeDefinitionRequest",{enumerable:!0,get:function(){return d.TypeDefinitionRequest}});const f=n(9935);Object.defineProperty(t,"WorkspaceFoldersRequest",{enumerable:!0,get:function(){return f.WorkspaceFoldersRequest}}),Object.defineProperty(t,"DidChangeWorkspaceFoldersNotification",{enumerable:!0,get:function(){return f.DidChangeWorkspaceFoldersNotification}});const h=n(1660);Object.defineProperty(t,"ConfigurationRequest",{enumerable:!0,get:function(){return h.ConfigurationRequest}});const p=n(7672);Object.defineProperty(t,"DocumentColorRequest",{enumerable:!0,get:function(){return p.DocumentColorRequest}}),Object.defineProperty(t,"ColorPresentationRequest",{enumerable:!0,get:function(){return p.ColorPresentationRequest}});const m=n(2874);Object.defineProperty(t,"FoldingRangeRequest",{enumerable:!0,get:function(){return m.FoldingRangeRequest}}),Object.defineProperty(t,"FoldingRangeRefreshRequest",{enumerable:!0,get:function(){return m.FoldingRangeRefreshRequest}});const g=n(6914);Object.defineProperty(t,"DeclarationRequest",{enumerable:!0,get:function(){return g.DeclarationRequest}});const y=n(3487);Object.defineProperty(t,"SelectionRangeRequest",{enumerable:!0,get:function(){return y.SelectionRangeRequest}});const b=n(2687);Object.defineProperty(t,"WorkDoneProgress",{enumerable:!0,get:function(){return b.WorkDoneProgress}}),Object.defineProperty(t,"WorkDoneProgressCreateRequest",{enumerable:!0,get:function(){return b.WorkDoneProgressCreateRequest}}),Object.defineProperty(t,"WorkDoneProgressCancelNotification",{enumerable:!0,get:function(){return b.WorkDoneProgressCancelNotification}});const v=n(8765);Object.defineProperty(t,"CallHierarchyIncomingCallsRequest",{enumerable:!0,get:function(){return v.CallHierarchyIncomingCallsRequest}}),Object.defineProperty(t,"CallHierarchyOutgoingCallsRequest",{enumerable:!0,get:function(){return v.CallHierarchyOutgoingCallsRequest}}),Object.defineProperty(t,"CallHierarchyPrepareRequest",{enumerable:!0,get:function(){return v.CallHierarchyPrepareRequest}});const k=n(2478);Object.defineProperty(t,"TokenFormat",{enumerable:!0,get:function(){return k.TokenFormat}}),Object.defineProperty(t,"SemanticTokensRequest",{enumerable:!0,get:function(){return k.SemanticTokensRequest}}),Object.defineProperty(t,"SemanticTokensDeltaRequest",{enumerable:!0,get:function(){return k.SemanticTokensDeltaRequest}}),Object.defineProperty(t,"SemanticTokensRangeRequest",{enumerable:!0,get:function(){return k.SemanticTokensRangeRequest}}),Object.defineProperty(t,"SemanticTokensRefreshRequest",{enumerable:!0,get:function(){return k.SemanticTokensRefreshRequest}}),Object.defineProperty(t,"SemanticTokensRegistrationType",{enumerable:!0,get:function(){return k.SemanticTokensRegistrationType}});const _=n(908);Object.defineProperty(t,"ShowDocumentRequest",{enumerable:!0,get:function(){return _.ShowDocumentRequest}});const T=n(5316);Object.defineProperty(t,"LinkedEditingRangeRequest",{enumerable:!0,get:function(){return T.LinkedEditingRangeRequest}});const S=n(9840);Object.defineProperty(t,"FileOperationPatternKind",{enumerable:!0,get:function(){return S.FileOperationPatternKind}}),Object.defineProperty(t,"DidCreateFilesNotification",{enumerable:!0,get:function(){return S.DidCreateFilesNotification}}),Object.defineProperty(t,"WillCreateFilesRequest",{enumerable:!0,get:function(){return S.WillCreateFilesRequest}}),Object.defineProperty(t,"DidRenameFilesNotification",{enumerable:!0,get:function(){return S.DidRenameFilesNotification}}),Object.defineProperty(t,"WillRenameFilesRequest",{enumerable:!0,get:function(){return S.WillRenameFilesRequest}}),Object.defineProperty(t,"DidDeleteFilesNotification",{enumerable:!0,get:function(){return S.DidDeleteFilesNotification}}),Object.defineProperty(t,"WillDeleteFilesRequest",{enumerable:!0,get:function(){return S.WillDeleteFilesRequest}});const C=n(9047);Object.defineProperty(t,"UniquenessLevel",{enumerable:!0,get:function(){return C.UniquenessLevel}}),Object.defineProperty(t,"MonikerKind",{enumerable:!0,get:function(){return C.MonikerKind}}),Object.defineProperty(t,"MonikerRequest",{enumerable:!0,get:function(){return C.MonikerRequest}});const R=n(645);Object.defineProperty(t,"TypeHierarchyPrepareRequest",{enumerable:!0,get:function(){return R.TypeHierarchyPrepareRequest}}),Object.defineProperty(t,"TypeHierarchySubtypesRequest",{enumerable:!0,get:function(){return R.TypeHierarchySubtypesRequest}}),Object.defineProperty(t,"TypeHierarchySupertypesRequest",{enumerable:!0,get:function(){return R.TypeHierarchySupertypesRequest}});const D=n(3124);Object.defineProperty(t,"InlineValueRequest",{enumerable:!0,get:function(){return D.InlineValueRequest}}),Object.defineProperty(t,"InlineValueRefreshRequest",{enumerable:!0,get:function(){return D.InlineValueRefreshRequest}});const w=n(7752);Object.defineProperty(t,"InlayHintRequest",{enumerable:!0,get:function(){return w.InlayHintRequest}}),Object.defineProperty(t,"InlayHintResolveRequest",{enumerable:!0,get:function(){return w.InlayHintResolveRequest}}),Object.defineProperty(t,"InlayHintRefreshRequest",{enumerable:!0,get:function(){return w.InlayHintRefreshRequest}});const x=n(6011);Object.defineProperty(t,"DiagnosticServerCancellationData",{enumerable:!0,get:function(){return x.DiagnosticServerCancellationData}}),Object.defineProperty(t,"DocumentDiagnosticReportKind",{enumerable:!0,get:function(){return x.DocumentDiagnosticReportKind}}),Object.defineProperty(t,"DocumentDiagnosticRequest",{enumerable:!0,get:function(){return x.DocumentDiagnosticRequest}}),Object.defineProperty(t,"WorkspaceDiagnosticRequest",{enumerable:!0,get:function(){return x.WorkspaceDiagnosticRequest}}),Object.defineProperty(t,"DiagnosticRefreshRequest",{enumerable:!0,get:function(){return x.DiagnosticRefreshRequest}});const P=n(3557);Object.defineProperty(t,"NotebookCellKind",{enumerable:!0,get:function(){return P.NotebookCellKind}}),Object.defineProperty(t,"ExecutionSummary",{enumerable:!0,get:function(){return P.ExecutionSummary}}),Object.defineProperty(t,"NotebookCell",{enumerable:!0,get:function(){return P.NotebookCell}}),Object.defineProperty(t,"NotebookDocument",{enumerable:!0,get:function(){return P.NotebookDocument}}),Object.defineProperty(t,"NotebookDocumentSyncRegistrationType",{enumerable:!0,get:function(){return P.NotebookDocumentSyncRegistrationType}}),Object.defineProperty(t,"DidOpenNotebookDocumentNotification",{enumerable:!0,get:function(){return P.DidOpenNotebookDocumentNotification}}),Object.defineProperty(t,"NotebookCellArrayChange",{enumerable:!0,get:function(){return P.NotebookCellArrayChange}}),Object.defineProperty(t,"DidChangeNotebookDocumentNotification",{enumerable:!0,get:function(){return P.DidChangeNotebookDocumentNotification}}),Object.defineProperty(t,"DidSaveNotebookDocumentNotification",{enumerable:!0,get:function(){return P.DidSaveNotebookDocumentNotification}}),Object.defineProperty(t,"DidCloseNotebookDocumentNotification",{enumerable:!0,get:function(){return P.DidCloseNotebookDocumentNotification}});const O=n(3307);Object.defineProperty(t,"InlineCompletionRequest",{enumerable:!0,get:function(){return O.InlineCompletionRequest}});const M=n(4289);var N,E,q,A,j,I,L,F,$,W,V,U,H,B,z,K,J,G,X,Z,Y,Q,ee,te,ne,re,oe,ie,se,ae,ce,ue,le,de,fe,he,pe,me,ge,ye,be,ve,ke,_e,Te,Se,Ce,Re,De,we,xe,Pe,Oe,Me,Ne,Ee,qe,Ae,je,Ie,Le,Fe,$e,We,Ve,Ue,He;Object.defineProperty(t,"TextDocumentContentRequest",{enumerable:!0,get:function(){return M.TextDocumentContentRequest}}),Object.defineProperty(t,"TextDocumentContentRefreshRequest",{enumerable:!0,get:function(){return M.TextDocumentContentRefreshRequest}}),function(e){e.is=function(e){const t=e;return u.string(t)||u.string(t.language)||u.string(t.scheme)||pe.is(t.pattern)}}(N||(t.TextDocumentFilter=N={})),function(e){e.is=function(e){const t=e;return u.objectLiteral(t)&&(u.string(t.notebookType)||u.string(t.scheme)||u.string(t.pattern))}}(E||(t.NotebookDocumentFilter=E={})),function(e){e.is=function(e){const t=e;return u.objectLiteral(t)&&(u.string(t.notebook)||E.is(t.notebook))&&(void 0===t.language||u.string(t.language))}}(q||(t.NotebookCellTextDocumentFilter=q={})),function(e){e.is=function(e){if(!Array.isArray(e))return!1;for(const t of e)if(!u.string(t)&&!N.is(t)&&!q.is(t))return!1;return!0}}(A||(t.DocumentSelector=A={})),function(e){e.method="client/registerCapability",e.messageDirection=a.MessageDirection.serverToClient,e.type=new a.ProtocolRequestType(e.method)}(j||(t.RegistrationRequest=j={})),function(e){e.method="client/unregisterCapability",e.messageDirection=a.MessageDirection.serverToClient,e.type=new a.ProtocolRequestType(e.method)}(I||(t.UnregistrationRequest=I={})),function(e){e.Create="create",e.Rename="rename",e.Delete="delete"}(L||(t.ResourceOperationKind=L={})),function(e){e.Abort="abort",e.Transactional="transactional",e.TextOnlyTransactional="textOnlyTransactional",e.Undo="undo"}(F||(t.FailureHandlingKind=F={})),function(e){e.ES2020="ES2020"}($||(t.RegularExpressionEngineKind=$={})),function(e){e.UTF8="utf-8",e.UTF16="utf-16",e.UTF32="utf-32"}(W||(t.PositionEncodingKind=W={})),function(e){e.hasId=function(e){const t=e;return t&&u.string(t.id)&&t.id.length>0}}(V||(t.StaticRegistrationOptions=V={})),function(e){e.is=function(e){const t=e;return t&&(null===t.documentSelector||A.is(t.documentSelector))}}(U||(t.TextDocumentRegistrationOptions=U={})),function(e){e.is=function(e){const t=e;return u.objectLiteral(t)&&(void 0===t.workDoneProgress||u.boolean(t.workDoneProgress))},e.hasWorkDoneProgress=function(e){const t=e;return t&&u.boolean(t.workDoneProgress)}}(H||(t.WorkDoneProgressOptions=H={})),function(e){e.method="initialize",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolRequestType(e.method)}(B||(t.InitializeRequest=B={})),function(e){e.unknownProtocolVersion=1}(z||(t.InitializeErrorCodes=z={})),function(e){e.method="initialized",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolNotificationType(e.method)}(K||(t.InitializedNotification=K={})),function(e){e.method="shutdown",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolRequestType0(e.method)}(J||(t.ShutdownRequest=J={})),function(e){e.method="exit",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolNotificationType0(e.method)}(G||(t.ExitNotification=G={})),function(e){e.method="workspace/didChangeConfiguration",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolNotificationType(e.method),e.capabilities=a.CM.create("workspace.didChangeConfiguration",void 0)}(X||(t.DidChangeConfigurationNotification=X={})),function(e){e.Error=1,e.Warning=2,e.Info=3,e.Log=4,e.Debug=5}(Z||(t.MessageType=Z={})),function(e){e.method="window/showMessage",e.messageDirection=a.MessageDirection.serverToClient,e.type=new a.ProtocolNotificationType(e.method),e.capabilities=a.CM.create("window.showMessage",void 0)}(Y||(t.ShowMessageNotification=Y={})),function(e){e.method="window/showMessageRequest",e.messageDirection=a.MessageDirection.serverToClient,e.type=new a.ProtocolRequestType(e.method),e.capabilities=a.CM.create("window.showMessage",void 0)}(Q||(t.ShowMessageRequest=Q={})),function(e){e.method="window/logMessage",e.messageDirection=a.MessageDirection.serverToClient,e.type=new a.ProtocolNotificationType(e.method)}(ee||(t.LogMessageNotification=ee={})),function(e){e.method="telemetry/event",e.messageDirection=a.MessageDirection.serverToClient,e.type=new a.ProtocolNotificationType(e.method)}(te||(t.TelemetryEventNotification=te={})),function(e){e.None=0,e.Full=1,e.Incremental=2}(ne||(t.TextDocumentSyncKind=ne={})),function(e){e.method="textDocument/didOpen",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolNotificationType(e.method),e.capabilities=a.CM.create("textDocument.synchronization","textDocumentSync.openClose")}(re||(t.DidOpenTextDocumentNotification=re={})),function(e){e.isIncremental=function(e){const t=e;return null!=t&&"string"==typeof t.text&&void 0!==t.range&&(void 0===t.rangeLength||"number"==typeof t.rangeLength)},e.isFull=function(e){const t=e;return null!=t&&"string"==typeof t.text&&void 0===t.range&&void 0===t.rangeLength}}(oe||(t.TextDocumentContentChangeEvent=oe={})),function(e){e.method="textDocument/didChange",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolNotificationType(e.method),e.capabilities=a.CM.create("textDocument.synchronization","textDocumentSync")}(ie||(t.DidChangeTextDocumentNotification=ie={})),function(e){e.method="textDocument/didClose",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolNotificationType(e.method),e.capabilities=a.CM.create("textDocument.synchronization","textDocumentSync.openClose")}(se||(t.DidCloseTextDocumentNotification=se={})),function(e){e.method="textDocument/didSave",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolNotificationType(e.method),e.capabilities=a.CM.create("textDocument.synchronization.didSave","textDocumentSync.save")}(ae||(t.DidSaveTextDocumentNotification=ae={})),function(e){e.Manual=1,e.AfterDelay=2,e.FocusOut=3}(ce||(t.TextDocumentSaveReason=ce={})),function(e){e.method="textDocument/willSave",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolNotificationType(e.method),e.capabilities=a.CM.create("textDocument.synchronization.willSave","textDocumentSync.willSave")}(ue||(t.WillSaveTextDocumentNotification=ue={})),function(e){e.method="textDocument/willSaveWaitUntil",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolRequestType(e.method),e.capabilities=a.CM.create("textDocument.synchronization.willSaveWaitUntil","textDocumentSync.willSaveWaitUntil")}(le||(t.WillSaveTextDocumentWaitUntilRequest=le={})),function(e){e.method="workspace/didChangeWatchedFiles",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolNotificationType(e.method),e.capabilities=a.CM.create("workspace.didChangeWatchedFiles",void 0)}(de||(t.DidChangeWatchedFilesNotification=de={})),function(e){e.Created=1,e.Changed=2,e.Deleted=3}(fe||(t.FileChangeType=fe={})),function(e){e.is=function(e){const t=e;return u.objectLiteral(t)&&(c.URI.is(t.baseUri)||c.WorkspaceFolder.is(t.baseUri))&&u.string(t.pattern)}}(he||(t.RelativePattern=he={})),function(e){e.is=function(e){const t=e;return u.string(t)||he.is(t)}}(pe||(t.GlobPattern=pe={})),function(e){e.Create=1,e.Change=2,e.Delete=4}(me||(t.WatchKind=me={})),function(e){e.method="textDocument/publishDiagnostics",e.messageDirection=a.MessageDirection.serverToClient,e.type=new a.ProtocolNotificationType(e.method),e.capabilities=a.CM.create("textDocument.publishDiagnostics",void 0)}(ge||(t.PublishDiagnosticsNotification=ge={})),function(e){e.Invoked=1,e.TriggerCharacter=2,e.TriggerForIncompleteCompletions=3}(ye||(t.CompletionTriggerKind=ye={})),function(e){e.method="textDocument/completion",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolRequestType(e.method),e.capabilities=a.CM.create("textDocument.completion","completionProvider")}(be||(t.CompletionRequest=be={})),function(e){e.method="completionItem/resolve",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolRequestType(e.method),e.capabilities=a.CM.create("textDocument.completion.completionItem.resolveSupport","completionProvider.resolveProvider")}(ve||(t.CompletionResolveRequest=ve={})),function(e){e.method="textDocument/hover",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolRequestType(e.method),e.capabilities=a.CM.create("textDocument.hover","hoverProvider")}(ke||(t.HoverRequest=ke={})),function(e){e.Invoked=1,e.TriggerCharacter=2,e.ContentChange=3}(_e||(t.SignatureHelpTriggerKind=_e={})),function(e){e.method="textDocument/signatureHelp",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolRequestType(e.method),e.capabilities=a.CM.create("textDocument.signatureHelp","signatureHelpProvider")}(Te||(t.SignatureHelpRequest=Te={})),function(e){e.method="textDocument/definition",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolRequestType(e.method),e.capabilities=a.CM.create("textDocument.definition","definitionProvider")}(Se||(t.DefinitionRequest=Se={})),function(e){e.method="textDocument/references",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolRequestType(e.method),e.capabilities=a.CM.create("textDocument.references","referencesProvider")}(Ce||(t.ReferencesRequest=Ce={})),function(e){e.method="textDocument/documentHighlight",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolRequestType(e.method),e.capabilities=a.CM.create("textDocument.documentHighlight","documentHighlightProvider")}(Re||(t.DocumentHighlightRequest=Re={})),function(e){e.method="textDocument/documentSymbol",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolRequestType(e.method),e.capabilities=a.CM.create("textDocument.documentSymbol","documentSymbolProvider")}(De||(t.DocumentSymbolRequest=De={})),function(e){e.method="textDocument/codeAction",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolRequestType(e.method),e.capabilities=a.CM.create("textDocument.codeAction","codeActionProvider")}(we||(t.CodeActionRequest=we={})),function(e){e.method="codeAction/resolve",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolRequestType(e.method),e.capabilities=a.CM.create("textDocument.codeAction.resolveSupport","codeActionProvider.resolveProvider")}(xe||(t.CodeActionResolveRequest=xe={})),function(e){e.method="workspace/symbol",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolRequestType(e.method),e.capabilities=a.CM.create("workspace.symbol","workspaceSymbolProvider")}(Pe||(t.WorkspaceSymbolRequest=Pe={})),function(e){e.method="workspaceSymbol/resolve",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolRequestType(e.method),e.capabilities=a.CM.create("workspace.symbol.resolveSupport","workspaceSymbolProvider.resolveProvider")}(Oe||(t.WorkspaceSymbolResolveRequest=Oe={})),function(e){e.method="textDocument/codeLens",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolRequestType(e.method),e.capabilities=a.CM.create("textDocument.codeLens","codeLensProvider")}(Me||(t.CodeLensRequest=Me={})),function(e){e.method="codeLens/resolve",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolRequestType(e.method),e.capabilities=a.CM.create("textDocument.codeLens.resolveSupport","codeLensProvider.resolveProvider")}(Ne||(t.CodeLensResolveRequest=Ne={})),function(e){e.method="workspace/codeLens/refresh",e.messageDirection=a.MessageDirection.serverToClient,e.type=new a.ProtocolRequestType0(e.method),e.capabilities=a.CM.create("workspace.codeLens",void 0)}(Ee||(t.CodeLensRefreshRequest=Ee={})),function(e){e.method="textDocument/documentLink",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolRequestType(e.method),e.capabilities=a.CM.create("textDocument.documentLink","documentLinkProvider")}(qe||(t.DocumentLinkRequest=qe={})),function(e){e.method="documentLink/resolve",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolRequestType(e.method),e.capabilities=a.CM.create("textDocument.documentLink","documentLinkProvider.resolveProvider")}(Ae||(t.DocumentLinkResolveRequest=Ae={})),function(e){e.method="textDocument/formatting",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolRequestType(e.method),e.capabilities=a.CM.create("textDocument.formatting","documentFormattingProvider")}(je||(t.DocumentFormattingRequest=je={})),function(e){e.method="textDocument/rangeFormatting",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolRequestType(e.method),e.capabilities=a.CM.create("textDocument.rangeFormatting","documentRangeFormattingProvider")}(Ie||(t.DocumentRangeFormattingRequest=Ie={})),function(e){e.method="textDocument/rangesFormatting",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolRequestType(e.method),e.capabilities=a.CM.create("textDocument.rangeFormatting.rangesSupport","documentRangeFormattingProvider.rangesSupport")}(Le||(t.DocumentRangesFormattingRequest=Le={})),function(e){e.method="textDocument/onTypeFormatting",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolRequestType(e.method),e.capabilities=a.CM.create("textDocument.onTypeFormatting","documentOnTypeFormattingProvider")}(Fe||(t.DocumentOnTypeFormattingRequest=Fe={})),function(e){e.Identifier=1}($e||(t.PrepareSupportDefaultBehavior=$e={})),function(e){e.method="textDocument/rename",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolRequestType(e.method),e.capabilities=a.CM.create("textDocument.rename","renameProvider")}(We||(t.RenameRequest=We={})),function(e){e.method="textDocument/prepareRename",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolRequestType(e.method),e.capabilities=a.CM.create("textDocument.rename.prepareSupport","renameProvider.prepareProvider")}(Ve||(t.PrepareRenameRequest=Ve={})),function(e){e.method="workspace/executeCommand",e.messageDirection=a.MessageDirection.clientToServer,e.type=new a.ProtocolRequestType(e.method),e.capabilities=a.CM.create("workspace.executeCommand","executeCommandProvider")}(Ue||(t.ExecuteCommandRequest=Ue={})),function(e){e.method="workspace/applyEdit",e.messageDirection=a.MessageDirection.serverToClient,e.type=new a.ProtocolRequestType("workspace/applyEdit"),e.capabilities=a.CM.create("workspace.applyEdit",void 0)}(He||(t.ApplyWorkspaceEditRequest=He={}))},5316:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.LinkedEditingRangeRequest=void 0;const r=n(372);var o;!function(e){e.method="textDocument/linkedEditingRange",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolRequestType(e.method),e.capabilities=r.CM.create("textDocument.linkedEditingRange","linkedEditingRangeProvider")}(o||(t.LinkedEditingRangeRequest=o={}))},9047:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MonikerRequest=t.MonikerKind=t.UniquenessLevel=void 0;const r=n(372);var o,i,s;!function(e){e.document="document",e.project="project",e.group="group",e.scheme="scheme",e.global="global"}(o||(t.UniquenessLevel=o={})),function(e){e.$import="import",e.$export="export",e.local="local"}(i||(t.MonikerKind=i={})),function(e){e.method="textDocument/moniker",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolRequestType(e.method),e.capabilities=r.CM.create("textDocument.moniker","monikerProvider")}(s||(t.MonikerRequest=s={}))},3557:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.DidCloseNotebookDocumentNotification=t.DidSaveNotebookDocumentNotification=t.DidChangeNotebookDocumentNotification=t.NotebookCellArrayChange=t.DidOpenNotebookDocumentNotification=t.NotebookDocumentSyncRegistrationType=t.NotebookDocument=t.NotebookCell=t.ExecutionSummary=t.NotebookCellKind=void 0;const a=n(4292),c=s(n(8598)),u=n(372);var l,d,f,h,p,m,g,y,b,v;!function(e){e.Markup=1,e.Code=2,e.is=function(e){return 1===e||2===e}}(l||(t.NotebookCellKind=l={})),function(e){e.create=function(e,t){const n={executionOrder:e};return!0!==t&&!1!==t||(n.success=t),n},e.is=function(e){const t=e;return c.objectLiteral(t)&&a.uinteger.is(t.executionOrder)&&(void 0===t.success||c.boolean(t.success))},e.equals=function(e,t){return e===t||null!=e&&null!=t&&e.executionOrder===t.executionOrder&&e.success===t.success}}(d||(t.ExecutionSummary=d={})),function(e){function t(e,n){if(e===n)return!0;if(null==e||null==n)return!1;if(typeof e!=typeof n)return!1;if("object"!=typeof e)return!1;const r=Array.isArray(e),o=Array.isArray(n);if(r!==o)return!1;if(r&&o){if(e.length!==n.length)return!1;for(let r=0;r<e.length;r++)if(!t(e[r],n[r]))return!1}if(c.objectLiteral(e)&&c.objectLiteral(n)){const r=Object.keys(e),o=Object.keys(n);if(r.length!==o.length)return!1;if(r.sort(),o.sort(),!t(r,o))return!1;for(let o=0;o<r.length;o++){const i=r[o];if(!t(e[i],n[i]))return!1}}return!0}e.create=function(e,t){return{kind:e,document:t}},e.is=function(e){const t=e;return c.objectLiteral(t)&&l.is(t.kind)&&a.DocumentUri.is(t.document)&&(void 0===t.metadata||c.objectLiteral(t.metadata))},e.diff=function(e,n){const r=new Set;return e.document!==n.document&&r.add("document"),e.kind!==n.kind&&r.add("kind"),e.executionSummary!==n.executionSummary&&r.add("executionSummary"),void 0===e.metadata&&void 0===n.metadata||t(e.metadata,n.metadata)||r.add("metadata"),void 0===e.executionSummary&&void 0===n.executionSummary||d.equals(e.executionSummary,n.executionSummary)||r.add("executionSummary"),r}}(f||(t.NotebookCell=f={})),function(e){e.create=function(e,t,n,r){return{uri:e,notebookType:t,version:n,cells:r}},e.is=function(e){const t=e;return c.objectLiteral(t)&&c.string(t.uri)&&a.integer.is(t.version)&&c.typedArray(t.cells,f.is)}}(h||(t.NotebookDocument=h={})),function(e){e.method="notebookDocument/sync",e.messageDirection=u.MessageDirection.clientToServer,e.type=new u.RegistrationType(e.method)}(p||(t.NotebookDocumentSyncRegistrationType=p={})),function(e){e.method="notebookDocument/didOpen",e.messageDirection=u.MessageDirection.clientToServer,e.type=new u.ProtocolNotificationType(e.method),e.registrationMethod=p.method}(m||(t.DidOpenNotebookDocumentNotification=m={})),function(e){e.is=function(e){const t=e;return c.objectLiteral(t)&&a.uinteger.is(t.start)&&a.uinteger.is(t.deleteCount)&&(void 0===t.cells||c.typedArray(t.cells,f.is))},e.create=function(e,t,n){const r={start:e,deleteCount:t};return void 0!==n&&(r.cells=n),r}}(g||(t.NotebookCellArrayChange=g={})),function(e){e.method="notebookDocument/didChange",e.messageDirection=u.MessageDirection.clientToServer,e.type=new u.ProtocolNotificationType(e.method),e.registrationMethod=p.method}(y||(t.DidChangeNotebookDocumentNotification=y={})),function(e){e.method="notebookDocument/didSave",e.messageDirection=u.MessageDirection.clientToServer,e.type=new u.ProtocolNotificationType(e.method),e.registrationMethod=p.method}(b||(t.DidSaveNotebookDocumentNotification=b={})),function(e){e.method="notebookDocument/didClose",e.messageDirection=u.MessageDirection.clientToServer,e.type=new u.ProtocolNotificationType(e.method),e.registrationMethod=p.method}(v||(t.DidCloseNotebookDocumentNotification=v={}))},2687:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WorkDoneProgressCancelNotification=t.WorkDoneProgressCreateRequest=t.WorkDoneProgress=void 0;const r=n(3281),o=n(372);var i,s,a;!function(e){e.type=new r.ProgressType,e.is=function(t){return t===e.type}}(i||(t.WorkDoneProgress=i={})),function(e){e.method="window/workDoneProgress/create",e.messageDirection=o.MessageDirection.serverToClient,e.type=new o.ProtocolRequestType(e.method),e.capabilities=o.CM.create("window.workDoneProgress",void 0)}(s||(t.WorkDoneProgressCreateRequest=s={})),function(e){e.method="window/workDoneProgress/cancel",e.messageDirection=o.MessageDirection.clientToServer,e.type=new o.ProtocolNotificationType(e.method)}(a||(t.WorkDoneProgressCancelNotification=a={}))},3487:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SelectionRangeRequest=void 0;const r=n(372);var o;!function(e){e.method="textDocument/selectionRange",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolRequestType(e.method),e.capabilities=r.CM.create("textDocument.selectionRange","selectionRangeProvider")}(o||(t.SelectionRangeRequest=o={}))},2478:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SemanticTokensRefreshRequest=t.SemanticTokensRangeRequest=t.SemanticTokensDeltaRequest=t.SemanticTokensRequest=t.SemanticTokensRegistrationType=t.TokenFormat=void 0;const r=n(372);var o,i,s,a,c,u;!function(e){e.Relative="relative"}(o||(t.TokenFormat=o={})),function(e){e.method="textDocument/semanticTokens",e.type=new r.RegistrationType(e.method)}(i||(t.SemanticTokensRegistrationType=i={})),function(e){e.method="textDocument/semanticTokens/full",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolRequestType(e.method),e.registrationMethod=i.method,e.capabilities=r.CM.create("textDocument.semanticTokens","semanticTokensProvider")}(s||(t.SemanticTokensRequest=s={})),function(e){e.method="textDocument/semanticTokens/full/delta",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolRequestType(e.method),e.registrationMethod=i.method,e.capabilities=r.CM.create("textDocument.semanticTokens.requests.full.delta","semanticTokensProvider.full.delta")}(a||(t.SemanticTokensDeltaRequest=a={})),function(e){e.method="textDocument/semanticTokens/range",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolRequestType(e.method),e.registrationMethod=i.method,e.capabilities=r.CM.create("textDocument.semanticTokens.requests.range","semanticTokensProvider.range")}(c||(t.SemanticTokensRangeRequest=c={})),function(e){e.method="workspace/semanticTokens/refresh",e.messageDirection=r.MessageDirection.serverToClient,e.type=new r.ProtocolRequestType0(e.method),e.capabilities=r.CM.create("workspace.semanticTokens.refreshSupport",void 0)}(u||(t.SemanticTokensRefreshRequest=u={}))},908:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ShowDocumentRequest=void 0;const r=n(372);var o;!function(e){e.method="window/showDocument",e.messageDirection=r.MessageDirection.serverToClient,e.type=new r.ProtocolRequestType(e.method),e.capabilities=r.CM.create("window.showDocument.support",void 0)}(o||(t.ShowDocumentRequest=o={}))},4289:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TextDocumentContentRefreshRequest=t.TextDocumentContentRequest=void 0;const r=n(372);var o,i;!function(e){e.method="workspace/textDocumentContent",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolRequestType(e.method),e.capabilities=r.CM.create("workspace.textDocumentContent","workspace.textDocumentContent")}(o||(t.TextDocumentContentRequest=o={})),function(e){e.method="workspace/textDocumentContent/refresh",e.messageDirection=r.MessageDirection.serverToClient,e.type=new r.ProtocolRequestType(e.method)}(i||(t.TextDocumentContentRefreshRequest=i={}))},8461:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TypeDefinitionRequest=void 0;const r=n(372);var o;!function(e){e.method="textDocument/typeDefinition",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolRequestType(e.method),e.capabilities=r.CM.create("textDocument.typeDefinition","typeDefinitionProvider")}(o||(t.TypeDefinitionRequest=o={}))},645:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TypeHierarchySubtypesRequest=t.TypeHierarchySupertypesRequest=t.TypeHierarchyPrepareRequest=void 0;const r=n(372);var o,i,s;!function(e){e.method="textDocument/prepareTypeHierarchy",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolRequestType(e.method),e.capabilities=r.CM.create("textDocument.typeHierarchy","typeHierarchyProvider")}(o||(t.TypeHierarchyPrepareRequest=o={})),function(e){e.method="typeHierarchy/supertypes",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolRequestType(e.method)}(i||(t.TypeHierarchySupertypesRequest=i={})),function(e){e.method="typeHierarchy/subtypes",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolRequestType(e.method)}(s||(t.TypeHierarchySubtypesRequest=s={}))},9935:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DidChangeWorkspaceFoldersNotification=t.WorkspaceFoldersRequest=void 0;const r=n(372);var o,i;!function(e){e.method="workspace/workspaceFolders",e.messageDirection=r.MessageDirection.serverToClient,e.type=new r.ProtocolRequestType0(e.method),e.capabilities=r.CM.create("workspace.workspaceFolders","workspace.workspaceFolders")}(o||(t.WorkspaceFoldersRequest=o={})),function(e){e.method="workspace/didChangeWorkspaceFolders",e.messageDirection=r.MessageDirection.clientToServer,e.type=new r.ProtocolNotificationType(e.method),e.capabilities=r.CM.create(void 0,"workspace.workspaceFolders.changeNotifications")}(i||(t.DidChangeWorkspaceFoldersNotification=i={}))},8598:(e,t)=>{"use strict";function n(e){return"string"==typeof e||e instanceof String}function r(e){return Array.isArray(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.boolean=function(e){return!0===e||!1===e},t.string=n,t.number=function(e){return"number"==typeof e||e instanceof Number},t.error=function(e){return e instanceof Error},t.func=function(e){return"function"==typeof e},t.array=r,t.stringArray=function(e){return r(e)&&e.every((e=>n(e)))},t.typedArray=function(e,t){return Array.isArray(e)&&e.every(t)},t.objectLiteral=function(e){return null!==e&&"object"==typeof e}},7354:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.createProtocolConnection=function(e,t,n,r){return(0,i.createMessageConnection)(e,t,n,r)};const i=n(7123);o(n(7123),t),o(n(8766),t)},2861:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t}),a=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||o(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.ProposedFeatures=t.NotebookDocuments=t.TextDocuments=t.SemanticTokensBuilder=void 0;const c=n(2655);Object.defineProperty(t,"SemanticTokensBuilder",{enumerable:!0,get:function(){return c.SemanticTokensBuilder}});const u=s(n(1276)),l=s(n(9202));a(n(8766),t);const d=n(1662);Object.defineProperty(t,"TextDocuments",{enumerable:!0,get:function(){return d.TextDocuments}});const f=n(20);var h;Object.defineProperty(t,"NotebookDocuments",{enumerable:!0,get:function(){return f.NotebookDocuments}}),a(n(7874),t),function(e){e.all={__brand:"features",workspace:l.TextDocumentContentFeature,languages:u.InlineCompletionFeature}}(h||(t.ProposedFeatures=h={}))},3918:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CallHierarchyFeature=void 0;const r=n(8766);t.CallHierarchyFeature=e=>class extends e{get callHierarchy(){return{onPrepare:e=>this.connection.onRequest(r.CallHierarchyPrepareRequest.type,((t,n)=>e(t,n,this.attachWorkDoneProgress(t),void 0))),onIncomingCalls:e=>{const t=r.CallHierarchyIncomingCallsRequest.type;return this.connection.onRequest(t,((n,r)=>e(n,r,this.attachWorkDoneProgress(n),this.attachPartialResultProgress(t,n))))},onOutgoingCalls:e=>{const t=r.CallHierarchyOutgoingCallsRequest.type;return this.connection.onRequest(t,((n,r)=>e(n,r,this.attachWorkDoneProgress(n),this.attachPartialResultProgress(t,n))))}}}}},8491:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.ConfigurationFeature=void 0;const a=n(8766),c=s(n(8867));t.ConfigurationFeature=e=>class extends e{getConfiguration(e){return e?c.string(e)?this._getConfiguration({section:e}):this._getConfiguration(e):this._getConfiguration({})}_getConfiguration(e){const t={items:Array.isArray(e)?e:[e]};return this.connection.sendRequest(a.ConfigurationRequest.type,t).then((t=>Array.isArray(t)?Array.isArray(e)?t:t[0]:Array.isArray(e)?[]:null))}}},493:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DiagnosticFeature=void 0;const r=n(8766);t.DiagnosticFeature=e=>class extends e{get diagnostics(){return{refresh:()=>this.connection.sendRequest(r.DiagnosticRefreshRequest.type),on:e=>this.connection.onRequest(r.DocumentDiagnosticRequest.type,((t,n)=>e(t,n,this.attachWorkDoneProgress(t),this.attachPartialResultProgress(r.DocumentDiagnosticRequest.partialResult,t)))),onWorkspace:e=>this.connection.onRequest(r.WorkspaceDiagnosticRequest.type,((t,n)=>e(t,n,this.attachWorkDoneProgress(t),this.attachPartialResultProgress(r.WorkspaceDiagnosticRequest.partialResult,t))))}}}},2697:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FileOperationsFeature=void 0;const r=n(8766);t.FileOperationsFeature=e=>class extends e{onDidCreateFiles(e){return this.connection.onNotification(r.DidCreateFilesNotification.type,(t=>e(t)))}onDidRenameFiles(e){return this.connection.onNotification(r.DidRenameFilesNotification.type,(t=>e(t)))}onDidDeleteFiles(e){return this.connection.onNotification(r.DidDeleteFilesNotification.type,(t=>e(t)))}onWillCreateFiles(e){return this.connection.onRequest(r.WillCreateFilesRequest.type,((t,n)=>e(t,n)))}onWillRenameFiles(e){return this.connection.onRequest(r.WillRenameFilesRequest.type,((t,n)=>e(t,n)))}onWillDeleteFiles(e){return this.connection.onRequest(r.WillDeleteFilesRequest.type,((t,n)=>e(t,n)))}}},6007:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FoldingRangeFeature=void 0;const r=n(8766);t.FoldingRangeFeature=e=>class extends e{get foldingRange(){return{refresh:()=>this.connection.sendRequest(r.FoldingRangeRefreshRequest.type),on:e=>{const t=r.FoldingRangeRequest.type;return this.connection.onRequest(t,((n,r)=>e(n,r,this.attachWorkDoneProgress(n),this.attachPartialResultProgress(t,n))))}}}}},4635:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.InlayHintFeature=void 0;const r=n(8766);t.InlayHintFeature=e=>class extends e{get inlayHint(){return{refresh:()=>this.connection.sendRequest(r.InlayHintRefreshRequest.type),on:e=>this.connection.onRequest(r.InlayHintRequest.type,((t,n)=>e(t,n,this.attachWorkDoneProgress(t)))),resolve:e=>this.connection.onRequest(r.InlayHintResolveRequest.type,((t,n)=>e(t,n)))}}}},1276:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.InlineCompletionFeature=void 0;const r=n(8766);t.InlineCompletionFeature=e=>class extends e{get inlineCompletion(){return{on:e=>this.connection.onRequest(r.InlineCompletionRequest.type,((t,n)=>e(t,n,this.attachWorkDoneProgress(t))))}}}},1815:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.InlineValueFeature=void 0;const r=n(8766);t.InlineValueFeature=e=>class extends e{get inlineValue(){return{refresh:()=>this.connection.sendRequest(r.InlineValueRefreshRequest.type),on:e=>this.connection.onRequest(r.InlineValueRequest.type,((t,n)=>e(t,n,this.attachWorkDoneProgress(t))))}}}},8517:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.LinkedEditingRangeFeature=void 0;const r=n(8766);t.LinkedEditingRangeFeature=e=>class extends e{onLinkedEditingRange(e){return this.connection.onRequest(r.LinkedEditingRangeRequest.type,((t,n)=>e(t,n,this.attachWorkDoneProgress(t),void 0)))}}},2936:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MonikerFeature=void 0;const r=n(8766);t.MonikerFeature=e=>class extends e{get moniker(){return{on:e=>{const t=r.MonikerRequest.type;return this.connection.onRequest(t,((n,r)=>e(n,r,this.attachWorkDoneProgress(n),this.attachPartialResultProgress(t,n))))}}}}},20:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.NotebookDocuments=t.NotebookSyncFeature=void 0;const r=n(8766),o=n(1662);t.NotebookSyncFeature=e=>class extends e{get synchronization(){return{onDidOpenNotebookDocument:e=>this.connection.onNotification(r.DidOpenNotebookDocumentNotification.type,(t=>e(t))),onDidChangeNotebookDocument:e=>this.connection.onNotification(r.DidChangeNotebookDocumentNotification.type,(t=>e(t))),onDidSaveNotebookDocument:e=>this.connection.onNotification(r.DidSaveNotebookDocumentNotification.type,(t=>e(t))),onDidCloseNotebookDocument:e=>this.connection.onNotification(r.DidCloseNotebookDocumentNotification.type,(t=>e(t)))}}};class i{static NULL_DISPOSE=Object.freeze({dispose:()=>{}});openHandler;changeHandler;closeHandler;onDidOpenTextDocument(e){return this.openHandler=e,r.Disposable.create((()=>{this.openHandler=void 0}))}openTextDocument(e){return this.openHandler&&this.openHandler(e)}onDidChangeTextDocument(e){return this.changeHandler=e,r.Disposable.create((()=>{this.changeHandler=e}))}changeTextDocument(e){return this.changeHandler&&this.changeHandler(e)}onDidCloseTextDocument(e){return this.closeHandler=e,r.Disposable.create((()=>{this.closeHandler=void 0}))}closeTextDocument(e){return this.closeHandler&&this.closeHandler(e)}onWillSaveTextDocument(){return i.NULL_DISPOSE}onWillSaveTextDocumentWaitUntil(){return i.NULL_DISPOSE}onDidSaveTextDocument(){return i.NULL_DISPOSE}}t.NotebookDocuments=class{notebookDocuments;notebookCellMap;_onDidOpen;_onDidSave;_onDidChange;_onDidClose;_cellTextDocuments;constructor(e){e instanceof o.TextDocuments?this._cellTextDocuments=e:this._cellTextDocuments=new o.TextDocuments(e),this.notebookDocuments=new Map,this.notebookCellMap=new Map,this._onDidOpen=new r.Emitter,this._onDidChange=new r.Emitter,this._onDidSave=new r.Emitter,this._onDidClose=new r.Emitter}get cellTextDocuments(){return this._cellTextDocuments}getCellTextDocument(e){return this._cellTextDocuments.get(e.document)}getNotebookDocument(e){return this.notebookDocuments.get(e)}getNotebookCell(e){const t=this.notebookCellMap.get(e);return t&&t[0]}findNotebookDocumentForCell(e){const t="string"==typeof e?e:e.document,n=this.notebookCellMap.get(t);return n&&n[1]}get onDidOpen(){return this._onDidOpen.event}get onDidSave(){return this._onDidSave.event}get onDidChange(){return this._onDidChange.event}get onDidClose(){return this._onDidClose.event}listen(e){const t=new i,n=[];return n.push(this.cellTextDocuments.listen(t)),n.push(e.notebooks.synchronization.onDidOpenNotebookDocument((async e=>{this.notebookDocuments.set(e.notebookDocument.uri,e.notebookDocument);for(const n of e.cellTextDocuments)await t.openTextDocument({textDocument:n});this.updateCellMap(e.notebookDocument),this._onDidOpen.fire(e.notebookDocument)}))),n.push(e.notebooks.synchronization.onDidChangeNotebookDocument((async e=>{const n=this.notebookDocuments.get(e.notebookDocument.uri);if(void 0===n)return;n.version=e.notebookDocument.version;const r=n.metadata;let o=!1;const i=e.change;void 0!==i.metadata&&(o=!0,n.metadata=i.metadata);const s=[],a=[],c=[],u=[];if(void 0!==i.cells){const e=i.cells;if(void 0!==e.structure){const r=e.structure.array;if(n.cells.splice(r.start,r.deleteCount,...void 0!==r.cells?r.cells:[]),void 0!==e.structure.didOpen)for(const n of e.structure.didOpen)await t.openTextDocument({textDocument:n}),s.push(n.uri);if(e.structure.didClose)for(const n of e.structure.didClose)await t.closeTextDocument({textDocument:n}),a.push(n.uri)}if(void 0!==e.data){const t=new Map(e.data.map((e=>[e.document,e])));for(let e=0;e<=n.cells.length;e++){const r=t.get(n.cells[e].document);if(void 0!==r){const o=n.cells.splice(e,1,r);if(c.push({old:o[0],new:r}),t.delete(r.document),0===t.size)break}}}if(void 0!==e.textContent)for(const n of e.textContent)await t.changeTextDocument({textDocument:n.document,contentChanges:n.changes}),u.push(n.document.uri)}this.updateCellMap(n);const l={notebookDocument:n};o&&(l.metadata={old:r,new:n.metadata});const d=[];for(const e of s)d.push(this.getNotebookCell(e));const f=[];for(const e of a)f.push(this.getNotebookCell(e));const h=[];for(const e of u)h.push(this.getNotebookCell(e));(d.length>0||f.length>0||c.length>0||h.length>0)&&(l.cells={added:d,removed:f,changed:{data:c,textContent:h}}),void 0===l.metadata&&void 0===l.cells||this._onDidChange.fire(l)}))),n.push(e.notebooks.synchronization.onDidSaveNotebookDocument((e=>{const t=this.notebookDocuments.get(e.notebookDocument.uri);void 0!==t&&this._onDidSave.fire(t)}))),n.push(e.notebooks.synchronization.onDidCloseNotebookDocument((async e=>{const n=this.notebookDocuments.get(e.notebookDocument.uri);if(void 0!==n){this._onDidClose.fire(n);for(const n of e.cellTextDocuments)await t.closeTextDocument({textDocument:n});this.notebookDocuments.delete(e.notebookDocument.uri);for(const e of n.cells)this.notebookCellMap.delete(e.document)}}))),r.Disposable.create((()=>{n.forEach((e=>e.dispose()))}))}updateCellMap(e){for(const t of e.cells)this.notebookCellMap.set(t.document,[t,e])}}},2938:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ProgressFeature=void 0,t.attachWorkDone=function(e,t){if(void 0===t||void 0===t.workDoneToken)return new a;const n=t.workDoneToken;return delete t.workDoneToken,new i(e,n)},t.attachPartialResult=function(e,t){if(void 0===t||void 0===t.partialResultToken)return;const n=t.partialResultToken;return delete t.partialResultToken,new l(e,n)};const r=n(8766),o=n(6116);class i{_connection;_token;static Instances=new Map;constructor(e,t){this._connection=e,this._token=t,i.Instances.set(this._token,this)}begin(e,t,n,o){const i={kind:"begin",title:e,message:n,cancellable:o};"number"==typeof t&&(i.percentage=Math.round(t)),this._connection.sendProgress(r.WorkDoneProgress.type,this._token,i)}report(e,t){const n={kind:"report"};"number"==typeof e?(n.percentage=Math.round(e),void 0!==t&&(n.message=t)):n.message=e,this._connection.sendProgress(r.WorkDoneProgress.type,this._token,n)}done(){i.Instances.delete(this._token),this._connection.sendProgress(r.WorkDoneProgress.type,this._token,{kind:"end"})}}class s extends i{_source;constructor(e,t){super(e,t),this._source=new r.CancellationTokenSource}get token(){return this._source.token}done(){this._source.dispose(),super.done()}cancel(){this._source.cancel()}}class a{constructor(){}begin(){}report(){}done(){}}class c extends a{_source;constructor(){super(),this._source=new r.CancellationTokenSource}get token(){return this._source.token}done(){this._source.dispose()}cancel(){this._source.cancel()}}var u;t.ProgressFeature=e=>class extends e{_progressSupported;constructor(){super(),this._progressSupported=!1}initialize(e){super.initialize(e),!0===e?.window?.workDoneProgress&&(this._progressSupported=!0,this.connection.onNotification(r.WorkDoneProgressCancelNotification.type,(e=>{const t=i.Instances.get(e.token);(t instanceof s||t instanceof c)&&t.cancel()})))}attachWorkDoneProgress(e){return void 0===e?new a:new i(this.connection,e)}createWorkDoneProgress(){if(this._progressSupported){const e=(0,o.generateUuid)();return this.connection.sendRequest(r.WorkDoneProgressCreateRequest.type,{token:e}).then((()=>new s(this.connection,e)))}return Promise.resolve(new c)}},function(e){e.type=new r.ProgressType}(u||(u={}));class l{_connection;_token;constructor(e,t){this._connection=e,this._token=t}report(e){this._connection.sendProgress(u.type,this._token,e)}}},2655:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SemanticTokensBuilder=t.SemanticTokensDiff=t.SemanticTokensFeature=void 0;const r=n(8766);t.SemanticTokensFeature=e=>class extends e{get semanticTokens(){return{refresh:()=>this.connection.sendRequest(r.SemanticTokensRefreshRequest.type),on:e=>{const t=r.SemanticTokensRequest.type;return this.connection.onRequest(t,((n,r)=>e(n,r,this.attachWorkDoneProgress(n),this.attachPartialResultProgress(t,n))))},onDelta:e=>{const t=r.SemanticTokensDeltaRequest.type;return this.connection.onRequest(t,((n,r)=>e(n,r,this.attachWorkDoneProgress(n),this.attachPartialResultProgress(t,n))))},onRange:e=>{const t=r.SemanticTokensRangeRequest.type;return this.connection.onRequest(t,((n,r)=>e(n,r,this.attachWorkDoneProgress(n),this.attachPartialResultProgress(t,n))))}}}};class o{originalSequence;modifiedSequence;constructor(e,t){this.originalSequence=e,this.modifiedSequence=t}computeDiff(){const e=this.originalSequence.length,t=this.modifiedSequence.length;let n=0;for(;n<t&&n<e&&this.originalSequence[n]===this.modifiedSequence[n];)n++;if(n<t&&n<e){let r=e-1,o=t-1;for(;r>=n&&o>=n&&this.originalSequence[r]===this.modifiedSequence[o];)r--,o--;(r<n||o<n)&&(r++,o++);const i=r-n+1,s=this.modifiedSequence.slice(n,o+1);return 1===s.length&&s[0]===this.originalSequence[r]?[{start:n,deleteCount:i-1}]:[{start:n,deleteCount:i,data:s}]}return n<t?[{start:n,deleteCount:0,data:this.modifiedSequence.slice(n)}]:n<e?[{start:n,deleteCount:e-n}]:[]}}t.SemanticTokensDiff=o;class i{_id;_prevLine;_prevChar;_dataIsSortedAndDeltaEncoded;_data;_dataNonDelta;_dataLen;_prevData;constructor(){this._prevData=void 0,this.initialize()}initialize(){this._id=Date.now(),this._prevLine=0,this._prevChar=0,this._data=[],this._dataNonDelta=[],this._dataLen=0,this._dataIsSortedAndDeltaEncoded=!0}push(e,t,n,r,o){this._dataIsSortedAndDeltaEncoded&&(e<this._prevLine||e===this._prevLine&&t<this._prevChar)&&(this._dataIsSortedAndDeltaEncoded=!1,this._dataNonDelta=i._deltaDecode(this._data));let s=e,a=t;this._dataIsSortedAndDeltaEncoded&&this._dataLen>0&&(s-=this._prevLine,0===s&&(a-=this._prevChar));const c=this._dataIsSortedAndDeltaEncoded?this._data:this._dataNonDelta;c[this._dataLen++]=s,c[this._dataLen++]=a,c[this._dataLen++]=n,c[this._dataLen++]=r,c[this._dataLen++]=o,this._prevLine=e,this._prevChar=t}get id(){return this._id.toString()}static _deltaDecode(e){const t=e.length/5|0;let n=0,r=0;const o=[];for(let i=0;i<t;i++){const t=5*i;let s=e[t],a=e[t+1];0===s?(s=n,a+=r):s+=n;const c=e[t+2],u=e[t+3],l=e[t+4];o[t+0]=s,o[t+1]=a,o[t+2]=c,o[t+3]=u,o[t+4]=l,n=s,r=a}return o}static _sortAndDeltaEncode(e){const t=[],n=e.length/5|0;for(let e=0;e<n;e++)t[e]=e;t.sort(((t,n)=>{const r=e[5*t],o=e[5*n];return r===o?e[5*t+1]-e[5*n+1]:r-o}));const r=[];let o=0,i=0;for(let s=0;s<n;s++){const n=5*t[s],a=e[n+0],c=e[n+1],u=e[n+2],l=e[n+3],d=e[n+4],f=a-o,h=0===f?c-i:c,p=5*s;r[p+0]=f,r[p+1]=h,r[p+2]=u,r[p+3]=l,r[p+4]=d,o=a,i=c}return r}getFinalDataDelta(){return this._dataIsSortedAndDeltaEncoded?this._data:i._sortAndDeltaEncode(this._dataNonDelta)}previousResult(e){this.id===e&&(this._prevData=this.getFinalDataDelta()),this.initialize()}build(){return this._prevData=void 0,{resultId:this.id,data:this.getFinalDataDelta()}}canBuildEdits(){return void 0!==this._prevData}buildEdits(){return void 0!==this._prevData?{resultId:this.id,edits:new o(this._prevData,this.getFinalDataDelta()).computeDiff()}:this.build()}}t.SemanticTokensBuilder=i},7874:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t._NotebooksImpl=t._LanguagesImpl=t.BulkUnregistration=t.BulkRegistration=t.ErrorMessageTracker=void 0,t.combineConsoleFeatures=$,t.combineTelemetryFeatures=W,t.combineTracerFeatures=V,t.combineClientFeatures=U,t.combineWindowFeatures=H,t.combineWorkspaceFeatures=B,t.combineLanguagesFeatures=z,t.combineNotebooksFeatures=K,t.combineFeatures=function(e,t){function n(e,t,n){return e&&t?n(e,t):e||t}return{__brand:"features",console:n(e.console,t.console,$),tracer:n(e.tracer,t.tracer,V),telemetry:n(e.telemetry,t.telemetry,W),client:n(e.client,t.client,U),window:n(e.window,t.window,H),workspace:n(e.workspace,t.workspace,B),languages:n(e.languages,t.languages,z),notebooks:n(e.notebooks,t.notebooks,K)}},t.createConnection=function(e,t,n){const r=n&&n.console?new(n.console(D)):new D,o=e(r);r.rawAttach(o);const i=n&&n.tracer?new(n.tracer(q)):new q,s=n&&n.telemetry?new(n.telemetry(A)):new A,u=n&&n.client?new(n.client(N)):new N,d=n&&n.window?new(n.window(w)):new w,f=n&&n.workspace?new(n.workspace(E)):new E,h=n&&n.languages?new(n.languages(I)):new I,p=n&&n.notebooks?new(n.notebooks(F)):new F,m=[r,i,s,u,d,f,h,p];let g,y,b;const v={listen:()=>o.listen(),sendRequest:(e,...t)=>o.sendRequest(c.string(e)?e:e.method,...t),onRequest:(e,t)=>o.onRequest(e,t),sendNotification:(e,t)=>{const n=c.string(e)?e:e.method;return o.sendNotification(n,t)},onNotification:(e,t)=>o.onNotification(e,t),onProgress:o.onProgress,sendProgress:o.sendProgress,onInitialize:e=>(y=e,{dispose:()=>{y=void 0}}),onInitialized:e=>o.onNotification(a.InitializedNotification.type,e),onShutdown:e=>(g=e,{dispose:()=>{g=void 0}}),onExit:e=>(b=e,{dispose:()=>{b=void 0}}),get console(){return r},get telemetry(){return s},get tracer(){return i},get client(){return u},get window(){return d},get workspace(){return f},get languages(){return h},get notebooks(){return p},onDidChangeConfiguration:e=>o.onNotification(a.DidChangeConfigurationNotification.type,e),onDidChangeWatchedFiles:e=>o.onNotification(a.DidChangeWatchedFilesNotification.type,e),__textDocumentSync:void 0,onDidOpenTextDocument:e=>o.onNotification(a.DidOpenTextDocumentNotification.type,e),onDidChangeTextDocument:e=>o.onNotification(a.DidChangeTextDocumentNotification.type,e),onDidCloseTextDocument:e=>o.onNotification(a.DidCloseTextDocumentNotification.type,e),onWillSaveTextDocument:e=>o.onNotification(a.WillSaveTextDocumentNotification.type,e),onWillSaveTextDocumentWaitUntil:e=>o.onRequest(a.WillSaveTextDocumentWaitUntilRequest.type,e),onDidSaveTextDocument:e=>o.onNotification(a.DidSaveTextDocumentNotification.type,e),sendDiagnostics:e=>o.sendNotification(a.PublishDiagnosticsNotification.type,e),onHover:e=>o.onRequest(a.HoverRequest.type,((t,n)=>e(t,n,(0,l.attachWorkDone)(o,t),void 0))),onCompletion:e=>o.onRequest(a.CompletionRequest.type,((t,n)=>e(t,n,(0,l.attachWorkDone)(o,t),(0,l.attachPartialResult)(o,t)))),onCompletionResolve:e=>o.onRequest(a.CompletionResolveRequest.type,e),onSignatureHelp:e=>o.onRequest(a.SignatureHelpRequest.type,((t,n)=>e(t,n,(0,l.attachWorkDone)(o,t),void 0))),onDeclaration:e=>o.onRequest(a.DeclarationRequest.type,((t,n)=>e(t,n,(0,l.attachWorkDone)(o,t),(0,l.attachPartialResult)(o,t)))),onDefinition:e=>o.onRequest(a.DefinitionRequest.type,((t,n)=>e(t,n,(0,l.attachWorkDone)(o,t),(0,l.attachPartialResult)(o,t)))),onTypeDefinition:e=>o.onRequest(a.TypeDefinitionRequest.type,((t,n)=>e(t,n,(0,l.attachWorkDone)(o,t),(0,l.attachPartialResult)(o,t)))),onImplementation:e=>o.onRequest(a.ImplementationRequest.type,((t,n)=>e(t,n,(0,l.attachWorkDone)(o,t),(0,l.attachPartialResult)(o,t)))),onReferences:e=>o.onRequest(a.ReferencesRequest.type,((t,n)=>e(t,n,(0,l.attachWorkDone)(o,t),(0,l.attachPartialResult)(o,t)))),onDocumentHighlight:e=>o.onRequest(a.DocumentHighlightRequest.type,((t,n)=>e(t,n,(0,l.attachWorkDone)(o,t),(0,l.attachPartialResult)(o,t)))),onDocumentSymbol:e=>o.onRequest(a.DocumentSymbolRequest.type,((t,n)=>e(t,n,(0,l.attachWorkDone)(o,t),(0,l.attachPartialResult)(o,t)))),onWorkspaceSymbol:e=>o.onRequest(a.WorkspaceSymbolRequest.type,((t,n)=>e(t,n,(0,l.attachWorkDone)(o,t),(0,l.attachPartialResult)(o,t)))),onWorkspaceSymbolResolve:e=>o.onRequest(a.WorkspaceSymbolResolveRequest.type,e),onCodeAction:e=>o.onRequest(a.CodeActionRequest.type,((t,n)=>e(t,n,(0,l.attachWorkDone)(o,t),(0,l.attachPartialResult)(o,t)))),onCodeActionResolve:e=>o.onRequest(a.CodeActionResolveRequest.type,((t,n)=>e(t,n))),onCodeLens:e=>o.onRequest(a.CodeLensRequest.type,((t,n)=>e(t,n,(0,l.attachWorkDone)(o,t),(0,l.attachPartialResult)(o,t)))),onCodeLensResolve:e=>o.onRequest(a.CodeLensResolveRequest.type,((t,n)=>e(t,n))),onDocumentFormatting:e=>o.onRequest(a.DocumentFormattingRequest.type,((t,n)=>e(t,n,(0,l.attachWorkDone)(o,t),void 0))),onDocumentRangeFormatting:e=>o.onRequest(a.DocumentRangeFormattingRequest.type,((t,n)=>e(t,n,(0,l.attachWorkDone)(o,t),void 0))),onDocumentOnTypeFormatting:e=>o.onRequest(a.DocumentOnTypeFormattingRequest.type,((t,n)=>e(t,n))),onRenameRequest:e=>o.onRequest(a.RenameRequest.type,((t,n)=>e(t,n,(0,l.attachWorkDone)(o,t),void 0))),onPrepareRename:e=>o.onRequest(a.PrepareRenameRequest.type,((t,n)=>e(t,n))),onDocumentLinks:e=>o.onRequest(a.DocumentLinkRequest.type,((t,n)=>e(t,n,(0,l.attachWorkDone)(o,t),(0,l.attachPartialResult)(o,t)))),onDocumentLinkResolve:e=>o.onRequest(a.DocumentLinkResolveRequest.type,((t,n)=>e(t,n))),onDocumentColor:e=>o.onRequest(a.DocumentColorRequest.type,((t,n)=>e(t,n,(0,l.attachWorkDone)(o,t),(0,l.attachPartialResult)(o,t)))),onColorPresentation:e=>o.onRequest(a.ColorPresentationRequest.type,((t,n)=>e(t,n,(0,l.attachWorkDone)(o,t),(0,l.attachPartialResult)(o,t)))),onFoldingRanges:e=>o.onRequest(a.FoldingRangeRequest.type,((t,n)=>e(t,n,(0,l.attachWorkDone)(o,t),(0,l.attachPartialResult)(o,t)))),onSelectionRanges:e=>o.onRequest(a.SelectionRangeRequest.type,((t,n)=>e(t,n,(0,l.attachWorkDone)(o,t),(0,l.attachPartialResult)(o,t)))),onExecuteCommand:e=>o.onRequest(a.ExecuteCommandRequest.type,((t,n)=>e(t,n,(0,l.attachWorkDone)(o,t),void 0))),dispose:()=>o.dispose()};for(const e of m)e.attach(v);return o.onRequest(a.InitializeRequest.type,(e=>{t.initialize(e),c.string(e.trace)&&(i.trace=a.Trace.fromString(e.trace));for(const t of m)t.initialize(e.capabilities);if(y){const t=y(e,(new a.CancellationTokenSource).token,(0,l.attachWorkDone)(o,e),void 0);return(n=t,n instanceof Promise?n:c.thenable(n)?new Promise(((e,t)=>{n.then((t=>e(t)),(e=>t(e)))})):Promise.resolve(n)).then((e=>{if(e instanceof a.ResponseError)return e;let t=e;t||(t={capabilities:{}});let n=t.capabilities;n||(n={},t.capabilities=n),void 0===n.textDocumentSync||null===n.textDocumentSync?n.textDocumentSync=c.number(v.__textDocumentSync)?v.__textDocumentSync:a.TextDocumentSyncKind.None:c.number(n.textDocumentSync)||c.number(n.textDocumentSync.change)||(n.textDocumentSync.change=c.number(v.__textDocumentSync)?v.__textDocumentSync:a.TextDocumentSyncKind.None);for(const e of m)e.fillServerCapabilities(n);return t}))}{const e={capabilities:{textDocumentSync:a.TextDocumentSyncKind.None}};for(const t of m)t.fillServerCapabilities(e.capabilities);return e}var n})),o.onRequest(a.ShutdownRequest.type,(()=>(t.shutdownReceived=!0,g?g((new a.CancellationTokenSource).token):void 0))),o.onNotification(a.ExitNotification.type,(()=>{try{if(b)return b()}finally{t.shutdownReceived?t.exit(0):t.exit(1)}})),o.onNotification(a.SetTraceNotification.type,(e=>{i.trace=a.Trace.fromString(e.value)})),v};const a=n(8766),c=s(n(8867)),u=s(n(6116)),l=n(2938),d=n(8491),f=n(2112),h=n(3918),p=n(2655),m=n(8817),g=n(2697),y=n(8517),b=n(5026),v=n(1815),k=n(6007),_=n(4635),T=n(493),S=n(20),C=n(2936);function R(e){if(null!==e)return e}t.ErrorMessageTracker=class{_messages;constructor(){this._messages=Object.create(null)}add(e){let t=this._messages[e];t||(t=0),t++,this._messages[e]=t}sendErrors(e){Object.keys(this._messages).forEach((t=>{e.window.showErrorMessage(t)}))}};class D{_rawConnection;_connection;constructor(){}rawAttach(e){this._rawConnection=e}attach(e){this._connection=e}get connection(){if(!this._connection)throw new Error("Remote is not attached to a connection yet.");return this._connection}fillServerCapabilities(e){}initialize(e){}error(e){this.send(a.MessageType.Error,e)}warn(e){this.send(a.MessageType.Warning,e)}info(e){this.send(a.MessageType.Info,e)}log(e){this.send(a.MessageType.Log,e)}debug(e){this.send(a.MessageType.Debug,e)}send(e,t){this._rawConnection&&this._rawConnection.sendNotification(a.LogMessageNotification.type,{type:e,message:t}).catch((()=>{(0,a.RAL)().console.error("Sending log message failed")}))}}const w=(0,m.ShowDocumentFeature)((0,l.ProgressFeature)(class{_connection;constructor(){}attach(e){this._connection=e}get connection(){if(!this._connection)throw new Error("Remote is not attached to a connection yet.");return this._connection}initialize(e){}fillServerCapabilities(e){}showErrorMessage(e,...t){const n={type:a.MessageType.Error,message:e,actions:t};return this.connection.sendRequest(a.ShowMessageRequest.type,n).then(R)}showWarningMessage(e,...t){const n={type:a.MessageType.Warning,message:e,actions:t};return this.connection.sendRequest(a.ShowMessageRequest.type,n).then(R)}showInformationMessage(e,...t){const n={type:a.MessageType.Info,message:e,actions:t};return this.connection.sendRequest(a.ShowMessageRequest.type,n).then(R)}}));var x,P;!function(e){e.create=function(){return new O}}(x||(t.BulkRegistration=x={}));class O{_registrations=[];_registered=new Set;add(e,t){const n=c.string(e)?e:e.method;if(this._registered.has(n))throw new Error(`${n} is already added to this registration`);const r=u.generateUuid();this._registrations.push({id:r,method:n,registerOptions:t||{}}),this._registered.add(n)}asRegistrationParams(){return{registrations:this._registrations}}}!function(e){e.create=function(){return new M(void 0,[])}}(P||(t.BulkUnregistration=P={}));class M{_connection;_unregistrations=new Map;constructor(e,t){this._connection=e,t.forEach((e=>{this._unregistrations.set(e.method,e)}))}get isAttached(){return!!this._connection}attach(e){this._connection=e}add(e){this._unregistrations.set(e.method,e)}dispose(){const e=[];for(const t of this._unregistrations.values())e.push(t);const t={unregisterations:e};this._connection.sendRequest(a.UnregistrationRequest.type,t).catch((()=>{this._connection.console.info("Bulk unregistration failed.")}))}disposeSingle(e){const t=c.string(e)?e:e.method,n=this._unregistrations.get(t);if(!n)return!1;const r={unregisterations:[n]};return this._connection.sendRequest(a.UnregistrationRequest.type,r).then((()=>{this._unregistrations.delete(t)}),(e=>{this._connection.console.info(`Un-registering request handler for ${n.id} failed.`)})),!0}}class N{_connection;attach(e){this._connection=e}get connection(){if(!this._connection)throw new Error("Remote is not attached to a connection yet.");return this._connection}initialize(e){}fillServerCapabilities(e){}register(e,t,n){return e instanceof O?this.registerMany(e):e instanceof M?this.registerSingle1(e,t,n):this.registerSingle2(e,t)}registerSingle1(e,t,n){const r=c.string(t)?t:t.method,o=u.generateUuid(),i={registrations:[{id:o,method:r,registerOptions:n||{}}]};return e.isAttached||e.attach(this.connection),this.connection.sendRequest(a.RegistrationRequest.type,i).then((t=>(e.add({id:o,method:r}),e)),(e=>(this.connection.console.info(`Registering request handler for ${r} failed.`),Promise.reject(e))))}registerSingle2(e,t){const n=c.string(e)?e:e.method,r=u.generateUuid(),o={registrations:[{id:r,method:n,registerOptions:t||{}}]};return this.connection.sendRequest(a.RegistrationRequest.type,o).then((e=>a.Disposable.create((()=>{this.unregisterSingle(r,n).catch((()=>{this.connection.console.info(`Un-registering capability with id ${r} failed.`)}))}))),(e=>(this.connection.console.info(`Registering request handler for ${n} failed.`),Promise.reject(e))))}unregisterSingle(e,t){const n={unregisterations:[{id:e,method:t}]};return this.connection.sendRequest(a.UnregistrationRequest.type,n).catch((()=>{this.connection.console.info(`Un-registering request handler for ${e} failed.`)}))}registerMany(e){const t=e.asRegistrationParams();return this.connection.sendRequest(a.RegistrationRequest.type,t).then((()=>new M(this._connection,t.registrations.map((e=>({id:e.id,method:e.method}))))),(e=>(this.connection.console.info("Bulk registration failed."),Promise.reject(e))))}}const E=(0,g.FileOperationsFeature)((0,f.WorkspaceFoldersFeature)((0,d.ConfigurationFeature)(class{_connection;constructor(){}attach(e){this._connection=e}get connection(){if(!this._connection)throw new Error("Remote is not attached to a connection yet.");return this._connection}initialize(e){}fillServerCapabilities(e){}applyEdit(e){const t=(n=e)&&n.edit?e:{edit:e};var n;return this.connection.sendRequest(a.ApplyWorkspaceEditRequest.type,t)}})));class q{_trace;_connection;constructor(){this._trace=a.Trace.Off}attach(e){this._connection=e}get connection(){if(!this._connection)throw new Error("Remote is not attached to a connection yet.");return this._connection}initialize(e){}fillServerCapabilities(e){}set trace(e){this._trace=e}log(e,t){this._trace!==a.Trace.Off&&this.connection.sendNotification(a.LogTraceNotification.type,{message:e,verbose:this._trace===a.Trace.Verbose?t:void 0}).catch((()=>{}))}}class A{_connection;constructor(){}attach(e){this._connection=e}get connection(){if(!this._connection)throw new Error("Remote is not attached to a connection yet.");return this._connection}initialize(e){}fillServerCapabilities(e){}logEvent(e){this.connection.sendNotification(a.TelemetryEventNotification.type,e).catch((()=>{this.connection.console.log("Sending TelemetryEventNotification failed")}))}}class j{_connection;constructor(){}attach(e){this._connection=e}get connection(){if(!this._connection)throw new Error("Remote is not attached to a connection yet.");return this._connection}initialize(e){}fillServerCapabilities(e){}attachWorkDoneProgress(e){return(0,l.attachWorkDone)(this.connection,e)}attachPartialResultProgress(e,t){return(0,l.attachPartialResult)(this.connection,t)}}t._LanguagesImpl=j;const I=(0,k.FoldingRangeFeature)((0,C.MonikerFeature)((0,T.DiagnosticFeature)((0,_.InlayHintFeature)((0,v.InlineValueFeature)((0,b.TypeHierarchyFeature)((0,y.LinkedEditingRangeFeature)((0,p.SemanticTokensFeature)((0,h.CallHierarchyFeature)(j)))))))));class L{_connection;constructor(){}attach(e){this._connection=e}get connection(){if(!this._connection)throw new Error("Remote is not attached to a connection yet.");return this._connection}initialize(e){}fillServerCapabilities(e){}attachWorkDoneProgress(e){return(0,l.attachWorkDone)(this.connection,e)}attachPartialResultProgress(e,t){return(0,l.attachPartialResult)(this.connection,t)}}t._NotebooksImpl=L;const F=(0,S.NotebookSyncFeature)(L);function $(e,t){return function(n){return t(e(n))}}function W(e,t){return function(n){return t(e(n))}}function V(e,t){return function(n){return t(e(n))}}function U(e,t){return function(n){return t(e(n))}}function H(e,t){return function(n){return t(e(n))}}function B(e,t){return function(n){return t(e(n))}}function z(e,t){return function(n){return t(e(n))}}function K(e,t){return function(n){return t(e(n))}}},8817:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ShowDocumentFeature=void 0;const r=n(8766);t.ShowDocumentFeature=e=>class extends e{showDocument(e){return this.connection.sendRequest(r.ShowDocumentRequest.type,e)}}},9202:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TextDocumentContentFeature=void 0;const r=n(8766);t.TextDocumentContentFeature=e=>class extends e{get textDocumentContent(){return{refresh:e=>this.connection.sendRequest(r.TextDocumentContentRefreshRequest.type,{uri:e}),on:e=>this.connection.onRequest(r.TextDocumentContentRequest.type,((t,n)=>e(t,n)))}}}},1662:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TextDocuments=void 0;const r=n(8766);t.TextDocuments=class{_configuration;_syncedDocuments;_onDidChangeContent;_onDidOpen;_onDidClose;_onDidSave;_onWillSave;_willSaveWaitUntil;constructor(e){this._configuration=e,this._syncedDocuments=new Map,this._onDidChangeContent=new r.Emitter,this._onDidOpen=new r.Emitter,this._onDidClose=new r.Emitter,this._onDidSave=new r.Emitter,this._onWillSave=new r.Emitter}get onDidOpen(){return this._onDidOpen.event}get onDidChangeContent(){return this._onDidChangeContent.event}get onWillSave(){return this._onWillSave.event}onWillSaveWaitUntil(e){this._willSaveWaitUntil=e}get onDidSave(){return this._onDidSave.event}get onDidClose(){return this._onDidClose.event}get(e){return this._syncedDocuments.get(e)}all(){return Array.from(this._syncedDocuments.values())}keys(){return Array.from(this._syncedDocuments.keys())}listen(e){e.__textDocumentSync=r.TextDocumentSyncKind.Incremental;const t=[];return t.push(e.onDidOpenTextDocument((e=>{const t=e.textDocument,n=this._configuration.create(t.uri,t.languageId,t.version,t.text);this._syncedDocuments.set(t.uri,n);const r=Object.freeze({document:n});this._onDidOpen.fire(r),this._onDidChangeContent.fire(r)}))),t.push(e.onDidChangeTextDocument((e=>{const t=e.textDocument,n=e.contentChanges;if(0===n.length)return;const{version:r}=t;if(null==r)throw new Error(`Received document change event for ${t.uri} without valid version identifier`);let o=this._syncedDocuments.get(t.uri);void 0!==o&&(o=this._configuration.update(o,n,r),this._syncedDocuments.set(t.uri,o),this._onDidChangeContent.fire(Object.freeze({document:o})))}))),t.push(e.onDidCloseTextDocument((e=>{const t=this._syncedDocuments.get(e.textDocument.uri);void 0!==t&&(this._syncedDocuments.delete(e.textDocument.uri),this._onDidClose.fire(Object.freeze({document:t})))}))),t.push(e.onWillSaveTextDocument((e=>{const t=this._syncedDocuments.get(e.textDocument.uri);void 0!==t&&this._onWillSave.fire(Object.freeze({document:t,reason:e.reason}))}))),t.push(e.onWillSaveTextDocumentWaitUntil(((e,t)=>{const n=this._syncedDocuments.get(e.textDocument.uri);return void 0!==n&&this._willSaveWaitUntil?this._willSaveWaitUntil(Object.freeze({document:n,reason:e.reason}),t):[]}))),t.push(e.onDidSaveTextDocument((e=>{const t=this._syncedDocuments.get(e.textDocument.uri);void 0!==t&&this._onDidSave.fire(Object.freeze({document:t}))}))),r.Disposable.create((()=>{t.forEach((e=>e.dispose()))}))}}},5026:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TypeHierarchyFeature=void 0;const r=n(8766);t.TypeHierarchyFeature=e=>class extends e{get typeHierarchy(){return{onPrepare:e=>this.connection.onRequest(r.TypeHierarchyPrepareRequest.type,((t,n)=>e(t,n,this.attachWorkDoneProgress(t),void 0))),onSupertypes:e=>{const t=r.TypeHierarchySupertypesRequest.type;return this.connection.onRequest(t,((n,r)=>e(n,r,this.attachWorkDoneProgress(n),this.attachPartialResultProgress(t,n))))},onSubtypes:e=>{const t=r.TypeHierarchySubtypesRequest.type;return this.connection.onRequest(t,((n,r)=>e(n,r,this.attachWorkDoneProgress(n),this.attachPartialResultProgress(t,n))))}}}}},8867:(e,t)=>{"use strict";function n(e){return"string"==typeof e||e instanceof String}function r(e){return"function"==typeof e}function o(e){return Array.isArray(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.boolean=function(e){return!0===e||!1===e},t.string=n,t.number=function(e){return"number"==typeof e||e instanceof Number},t.error=function(e){return e instanceof Error},t.func=r,t.array=o,t.stringArray=function(e){return o(e)&&e.every((e=>n(e)))},t.typedArray=function(e,t){return Array.isArray(e)&&e.every(t)},t.thenable=function(e){return e&&r(e.then)}},6116:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.empty=void 0,t.v4=o,t.isUUID=s,t.parse=function(e){if(!s(e))throw new Error("invalid uuid");return new n(e)},t.generateUuid=function(){return o().asHex()};class n{_value;constructor(e){this._value=e}asHex(){return this._value}equals(e){return this.asHex()===e.asHex()}}class r extends n{static _chars=["0","1","2","3","4","5","6","6","7","8","9","a","b","c","d","e","f"];static _timeHighBits=["8","9","a","b"];static _oneOf(e){return e[Math.floor(e.length*Math.random())]}static _randomHex(){return r._oneOf(r._chars)}constructor(){super([r._randomHex(),r._randomHex(),r._randomHex(),r._randomHex(),r._randomHex(),r._randomHex(),r._randomHex(),r._randomHex(),"-",r._randomHex(),r._randomHex(),r._randomHex(),r._randomHex(),"-","4",r._randomHex(),r._randomHex(),r._randomHex(),"-",r._oneOf(r._timeHighBits),r._randomHex(),r._randomHex(),r._randomHex(),"-",r._randomHex(),r._randomHex(),r._randomHex(),r._randomHex(),r._randomHex(),r._randomHex(),r._randomHex(),r._randomHex(),r._randomHex(),r._randomHex(),r._randomHex(),r._randomHex()].join(""))}}function o(){return new r}t.empty=new n("00000000-0000-0000-0000-000000000000");const i=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;function s(e){return i.test(e)}},2112:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WorkspaceFoldersFeature=void 0;const r=n(8766);t.WorkspaceFoldersFeature=e=>class extends e{_onDidChangeWorkspaceFolders;_unregistration;_notificationIsAutoRegistered;constructor(){super(),this._notificationIsAutoRegistered=!1}initialize(e){super.initialize(e);const t=e.workspace;t&&t.workspaceFolders&&(this._onDidChangeWorkspaceFolders=new r.Emitter,this.connection.onNotification(r.DidChangeWorkspaceFoldersNotification.type,(e=>{this._onDidChangeWorkspaceFolders.fire(e.event)})))}fillServerCapabilities(e){super.fillServerCapabilities(e);const t=e.workspace?.workspaceFolders?.changeNotifications;this._notificationIsAutoRegistered=!0===t||"string"==typeof t}getWorkspaceFolders(){return this.connection.sendRequest(r.WorkspaceFoldersRequest.type)}get onDidChangeWorkspaceFolders(){if(!this._onDidChangeWorkspaceFolders)throw new Error("Client doesn't support sending workspace folder change events.");return this._notificationIsAutoRegistered||this._unregistration||(this._unregistration=this.connection.client.register(r.DidChangeWorkspaceFoldersNotification.type)),this._onDidChangeWorkspaceFolders.event}}},3911:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.FileSystem=void 0,t.uriToFilePath=function(e){const t=a.parse(e);if("file:"!==t.protocol||!t.path)return;const n=t.path.split("/");for(let e=0,t=n.length;e<t;e++)n[e]=decodeURIComponent(n[e]);if("win32"===process.platform&&n.length>1){const e=n[0],t=n[1];0===e.length&&t.length>1&&":"===t[1]&&n.shift()}return c.normalize(n.join("/"))},t.resolve=f,t.resolveGlobalNodePath=h,t.resolveGlobalYarnPath=function(e){let t="yarn";const n={encoding:"utf8"};d()&&(t="yarn.cmd",n.shell=!0);const r=()=>{};try{process.on("SIGPIPE",r);const o=(0,l.spawnSync)(t,["global","dir","--json"],n),i=o.stdout;if(!i)return void(e&&(e("'yarn global dir' didn't return a value."),o.stderr&&e(o.stderr)));const s=i.trim().split(/\r?\n/);for(const e of s)try{const t=JSON.parse(e);if("log"===t.type)return c.join(t.data,"node_modules")}catch(e){}return}catch(e){return}finally{process.removeListener("SIGPIPE",r)}},t.resolveModulePath=function(e,t,n,r){return n?(c.isAbsolute(n)||(n=c.join(e,n)),f(t,n,n,r).then((e=>p.isParent(n,e)?e:Promise.reject(new Error(`Failed to load ${t} from node path location.`)))).then(void 0,(n=>f(t,h(r),e,r)))):f(t,h(r),e,r)};const a=s(n(7016)),c=s(n(6928)),u=s(n(9896)),l=n(5317);function d(){return"win32"===process.platform}function f(e,t,n,r){const o="NODE_PATH",i=["var p = process;","p.on('message',function(m){","if(m.c==='e'){","p.exit(0);","}","else if(m.c==='rs'){","try{","var r=require.resolve(m.a);","p.send({c:'r',s:true,r:r});","}","catch(err){","p.send({c:'r',s:false});","}","}","});"].join("");return new Promise(((s,a)=>{const d=process.env,f=Object.create(null);Object.keys(d).forEach((e=>f[e]=d[e])),t&&u.existsSync(t)&&(f[o]?f[o]=t+c.delimiter+f[o]:f[o]=t,r&&r(`NODE_PATH value is: ${f[o]}`)),f.ELECTRON_RUN_AS_NODE="1";try{const t=(0,l.fork)("",[],{cwd:n,env:f,execArgv:["-e",i]});if(void 0===t.pid)return void a(new Error(`Starting process to resolve node module  ${e} failed`));t.on("error",(e=>{a(e)})),t.on("message",(n=>{"r"===n.c&&(t.send({c:"e"}),n.s?s(n.r):a(new Error(`Failed to resolve module: ${e}`)))}));const r={c:"rs",a:e};t.send(r)}catch(e){a(e)}}))}function h(e){let t="npm";const n=Object.create(null);Object.keys(process.env).forEach((e=>n[e]=process.env[e])),n.NO_UPDATE_NOTIFIER="true";const r={encoding:"utf8",env:n};d()&&(t="npm.cmd",r.shell=!0);const o=()=>{};try{process.on("SIGPIPE",o);const n=(0,l.spawnSync)(t,["config","get","prefix"],r).stdout;if(!n)return void(e&&e("'npm config get prefix' didn't return a value."));const i=n.trim();return e&&e(`'npm config get prefix' value is: ${i}`),i.length>0?d()?c.join(i,"node_modules"):c.join(i,"lib","node_modules"):void 0}catch(e){return}finally{process.removeListener("SIGPIPE",o)}}var p;!function(e){let t;function n(){return void 0!==t||(t=!("win32"===process.platform||u.existsSync(__filename.toUpperCase())&&u.existsSync(__filename.toLowerCase()))),t}e.isCaseSensitive=n,e.isParent=function(e,t){return n()?0===c.normalize(t).indexOf(c.normalize(e)):0===c.normalize(t).toLowerCase().indexOf(c.normalize(e).toLowerCase())}}(p||(t.FileSystem=p={}))},1327:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t}),a=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||o(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.Files=void 0,t.createConnection=function(e,t,n,r){let o,i,s,a;return void 0!==e&&"features"===e.__brand&&(o=e,e=t,t=n,n=r),f.ConnectionStrategy.is(e)||f.ConnectionOptions.is(e)?a=e:(i=e,s=t,a=n),function(e,t,n,r){let o=!1;if(!e&&!t&&process.argv.length>2){let n,r;const i=process.argv.slice(2);for(let s=0;s<i.length;s++){const a=i[s];if("--node-ipc"===a){e=new f.IPCMessageReader(process),t=new f.IPCMessageWriter(process);break}if("--stdio"===a){o=!0,e=process.stdin,t=process.stdout;break}if("--socket"===a){n=parseInt(i[s+1]);break}if("--pipe"===a){r=i[s+1];break}{const e=a.split("=");if("--socket"===e[0]){n=parseInt(e[1]);break}if("--pipe"===e[0]){r=e[1];break}}}if(n){const r=(0,f.createServerSocketTransport)(n);e=r[0],t=r[1]}else if(r){const n=(0,f.createServerPipeTransport)(r);e=n[0],t=n[1]}}const i="Use arguments of createConnection or set command line parameters: '--node-ipc', '--stdio' or '--socket={number}'";if(!e)throw new Error("Connection input stream is not set. "+i);if(!t)throw new Error("Connection output stream is not set. "+i);if(u.func(e.read)&&u.func(e.on)){const t=e;t.on("end",(()=>{m(),process.exit(y?0:1)})),t.on("close",(()=>{m(),process.exit(y?0:1)}))}return(0,l.createConnection)((r=>{const i=(0,f.createProtocolConnection)(e,t,r,n);return o&&function(e){function t(e){return e.map((e=>"string"==typeof e?e:(0,c.inspect)(e))).join(" ")}const n=new Map;console.assert=function(n,...r){if(!n)if(0===r.length)e.error("Assertion failed");else{const[n,...o]=r;e.error(`Assertion failed: ${n} ${t(o)}`)}},console.count=function(t="default"){const r=String(t);let o=n.get(r)??0;o+=1,n.set(r,o),e.log(`${r}: ${r}`)},console.countReset=function(e){void 0===e?n.clear():n.delete(String(e))},console.debug=function(...n){e.log(t(n))},console.dir=function(t,n){e.log((0,c.inspect)(t,n))},console.log=function(...n){e.log(t(n))},console.error=function(...n){e.error(t(n))},console.trace=function(...n){const r=(new Error).stack.replace(/(.+\n){2}/,"");let o="Trace";0!==n.length&&(o+=`: ${t(n)}`),e.log(`${o}\n${r}`)},console.warn=function(...n){e.warn(t(n))}}(r),i}),b,r)}(i,s,a,o)};const c=n(7975),u=s(n(8867)),l=n(7874),d=s(n(3911)),f=n(7354);var h;let p;function m(){if(void 0!==p)try{p.end()}catch(e){}}a(n(7354),t),a(n(2861),t),function(e){e.uriToFilePath=d.uriToFilePath,e.resolveGlobalNodePath=d.resolveGlobalNodePath,e.resolveGlobalYarnPath=d.resolveGlobalYarnPath,e.resolve=d.resolve,e.resolveModulePath=d.resolveModulePath}(h||(t.Files=h={}));let g,y=!1;!function(){const e="--clientProcessId";function t(e){try{const t=parseInt(e);isNaN(t)||(g=setInterval((()=>{try{process.kill(t,0)}catch(e){m(),process.exit(y?0:1)}}),3e3))}catch(e){}}for(let n=2;n<process.argv.length;n++){const r=process.argv[n];if(r===e&&n+1<process.argv.length)return void t(process.argv[n+1]);{const n=r.split("=");n[0]===e&&t(n[1])}}}();const b={initialize:e=>{const t=e.processId;u.number(t)&&void 0===g&&setInterval((()=>{try{process.kill(t,0)}catch(e){process.exit(y?0:1)}}),3e3)},get shutdownReceived(){return y},set shutdownReceived(e){y=e},exit:e=>{m(),process.exit(e)}}},4292:(e,t,n)=>{"use strict";var r,o,i,s,a,c,u,l,d,f,h,p,m,g,y,b,v,k,_,T,S,C,R,D,w,x,P,O,M,N,E,q,A,j,I,L,F,$,W,V,U,H,B,z,K,J,G,X,Z,Y,Q,ee,te,ne,re,oe,ie,se,ae,ce,ue,le,de,fe,he,pe,me,ge,ye,be,ve,ke,_e,Te,Se,Ce,Re,De,we,xe,Pe,Oe;n.r(t),n.d(t,{AnnotatedTextEdit:()=>R,ApplyKind:()=>H,ChangeAnnotation:()=>S,ChangeAnnotationIdentifier:()=>C,CodeAction:()=>ue,CodeActionContext:()=>ae,CodeActionKind:()=>ie,CodeActionTag:()=>ce,CodeActionTriggerKind:()=>se,CodeDescription:()=>v,CodeLens:()=>le,Color:()=>d,ColorInformation:()=>f,ColorPresentation:()=>h,Command:()=>_,CompletionItem:()=>z,CompletionItemKind:()=>F,CompletionItemLabelDetails:()=>B,CompletionItemTag:()=>W,CompletionList:()=>K,CreateFile:()=>w,DeleteFile:()=>P,Diagnostic:()=>k,DiagnosticRelatedInformation:()=>g,DiagnosticSeverity:()=>y,DiagnosticTag:()=>b,DocumentHighlight:()=>Q,DocumentHighlightKind:()=>Y,DocumentLink:()=>fe,DocumentSymbol:()=>oe,DocumentUri:()=>r,EOL:()=>qe,FoldingRange:()=>m,FoldingRangeKind:()=>p,FormattingOptions:()=>de,Hover:()=>G,InlayHint:()=>Se,InlayHintKind:()=>_e,InlayHintLabelPart:()=>Te,InlineCompletionContext:()=>Pe,InlineCompletionItem:()=>Re,InlineCompletionList:()=>De,InlineCompletionTriggerKind:()=>we,InlineValueContext:()=>ke,InlineValueEvaluatableExpression:()=>ve,InlineValueText:()=>ye,InlineValueVariableLookup:()=>be,InsertReplaceEdit:()=>V,InsertTextFormat:()=>$,InsertTextMode:()=>U,LanguageKind:()=>A,Location:()=>u,LocationLink:()=>l,MarkedString:()=>J,MarkupContent:()=>L,MarkupKind:()=>I,OptionalVersionedTextDocumentIdentifier:()=>q,ParameterInformation:()=>X,Position:()=>a,Range:()=>c,RenameFile:()=>x,SelectedCompletionInfo:()=>xe,SelectionRange:()=>he,SemanticTokenModifiers:()=>me,SemanticTokenTypes:()=>pe,SemanticTokens:()=>ge,SignatureInformation:()=>Z,SnippetTextEdit:()=>M,StringValue:()=>Ce,SymbolInformation:()=>ne,SymbolKind:()=>ee,SymbolTag:()=>te,TextDocument:()=>Ae,TextDocumentEdit:()=>D,TextDocumentIdentifier:()=>N,TextDocumentItem:()=>j,TextEdit:()=>T,URI:()=>o,VersionedTextDocumentIdentifier:()=>E,WorkspaceChange:()=>Ee,WorkspaceEdit:()=>O,WorkspaceFolder:()=>Oe,WorkspaceSymbol:()=>re,integer:()=>i,uinteger:()=>s}),function(e){e.is=function(e){return"string"==typeof e}}(r||(r={})),function(e){e.is=function(e){return"string"==typeof e}}(o||(o={})),function(e){e.MIN_VALUE=-2147483648,e.MAX_VALUE=2147483647,e.is=function(t){return"number"==typeof t&&e.MIN_VALUE<=t&&t<=e.MAX_VALUE}}(i||(i={})),function(e){e.MIN_VALUE=0,e.MAX_VALUE=2147483647,e.is=function(t){return"number"==typeof t&&e.MIN_VALUE<=t&&t<=e.MAX_VALUE}}(s||(s={})),function(e){e.create=function(e,t){return e===Number.MAX_VALUE&&(e=s.MAX_VALUE),t===Number.MAX_VALUE&&(t=s.MAX_VALUE),{line:e,character:t}},e.is=function(e){const t=e;return je.objectLiteral(t)&&je.uinteger(t.line)&&je.uinteger(t.character)}}(a||(a={})),function(e){e.create=function(e,t,n,r){if(je.uinteger(e)&&je.uinteger(t)&&je.uinteger(n)&&je.uinteger(r))return{start:a.create(e,t),end:a.create(n,r)};if(a.is(e)&&a.is(t))return{start:e,end:t};throw new Error(`Range#create called with invalid arguments[${e}, ${t}, ${n}, ${r}]`)},e.is=function(e){const t=e;return je.objectLiteral(t)&&a.is(t.start)&&a.is(t.end)}}(c||(c={})),function(e){e.create=function(e,t){return{uri:e,range:t}},e.is=function(e){const t=e;return je.objectLiteral(t)&&c.is(t.range)&&(je.string(t.uri)||je.undefined(t.uri))}}(u||(u={})),function(e){e.create=function(e,t,n,r){return{targetUri:e,targetRange:t,targetSelectionRange:n,originSelectionRange:r}},e.is=function(e){const t=e;return je.objectLiteral(t)&&c.is(t.targetRange)&&je.string(t.targetUri)&&c.is(t.targetSelectionRange)&&(c.is(t.originSelectionRange)||je.undefined(t.originSelectionRange))}}(l||(l={})),function(e){e.create=function(e,t,n,r){return{red:e,green:t,blue:n,alpha:r}},e.is=function(e){const t=e;return je.objectLiteral(t)&&je.numberRange(t.red,0,1)&&je.numberRange(t.green,0,1)&&je.numberRange(t.blue,0,1)&&je.numberRange(t.alpha,0,1)}}(d||(d={})),function(e){e.create=function(e,t){return{range:e,color:t}},e.is=function(e){const t=e;return je.objectLiteral(t)&&c.is(t.range)&&d.is(t.color)}}(f||(f={})),function(e){e.create=function(e,t,n){return{label:e,textEdit:t,additionalTextEdits:n}},e.is=function(e){const t=e;return je.objectLiteral(t)&&je.string(t.label)&&(je.undefined(t.textEdit)||T.is(t))&&(je.undefined(t.additionalTextEdits)||je.typedArray(t.additionalTextEdits,T.is))}}(h||(h={})),function(e){e.Comment="comment",e.Imports="imports",e.Region="region"}(p||(p={})),function(e){e.create=function(e,t,n,r,o,i){const s={startLine:e,endLine:t};return je.defined(n)&&(s.startCharacter=n),je.defined(r)&&(s.endCharacter=r),je.defined(o)&&(s.kind=o),je.defined(i)&&(s.collapsedText=i),s},e.is=function(e){const t=e;return je.objectLiteral(t)&&je.uinteger(t.startLine)&&je.uinteger(t.startLine)&&(je.undefined(t.startCharacter)||je.uinteger(t.startCharacter))&&(je.undefined(t.endCharacter)||je.uinteger(t.endCharacter))&&(je.undefined(t.kind)||je.string(t.kind))}}(m||(m={})),function(e){e.create=function(e,t){return{location:e,message:t}},e.is=function(e){const t=e;return je.defined(t)&&u.is(t.location)&&je.string(t.message)}}(g||(g={})),function(e){e.Error=1,e.Warning=2,e.Information=3,e.Hint=4}(y||(y={})),function(e){e.Unnecessary=1,e.Deprecated=2}(b||(b={})),function(e){e.is=function(e){const t=e;return je.objectLiteral(t)&&je.string(t.href)}}(v||(v={})),function(e){e.create=function(e,t,n,r,o,i){const s={range:e,message:t};return je.defined(n)&&(s.severity=n),je.defined(r)&&(s.code=r),je.defined(o)&&(s.source=o),je.defined(i)&&(s.relatedInformation=i),s},e.is=function(e){var t;const n=e;return je.defined(n)&&c.is(n.range)&&je.string(n.message)&&(je.number(n.severity)||je.undefined(n.severity))&&(je.integer(n.code)||je.string(n.code)||je.undefined(n.code))&&(je.undefined(n.codeDescription)||je.string(null===(t=n.codeDescription)||void 0===t?void 0:t.href))&&(je.string(n.source)||je.undefined(n.source))&&(je.undefined(n.relatedInformation)||je.typedArray(n.relatedInformation,g.is))}}(k||(k={})),function(e){e.create=function(e,t,...n){const r={title:e,command:t};return je.defined(n)&&n.length>0&&(r.arguments=n),r},e.is=function(e){const t=e;return je.defined(t)&&je.string(t.title)&&(void 0===t.tooltip||je.string(t.tooltip))&&je.string(t.command)}}(_||(_={})),function(e){e.replace=function(e,t){return{range:e,newText:t}},e.insert=function(e,t){return{range:{start:e,end:e},newText:t}},e.del=function(e){return{range:e,newText:""}},e.is=function(e){const t=e;return je.objectLiteral(t)&&je.string(t.newText)&&c.is(t.range)}}(T||(T={})),function(e){e.create=function(e,t,n){const r={label:e};return void 0!==t&&(r.needsConfirmation=t),void 0!==n&&(r.description=n),r},e.is=function(e){const t=e;return je.objectLiteral(t)&&je.string(t.label)&&(je.boolean(t.needsConfirmation)||void 0===t.needsConfirmation)&&(je.string(t.description)||void 0===t.description)}}(S||(S={})),function(e){e.is=function(e){const t=e;return je.string(t)}}(C||(C={})),function(e){e.replace=function(e,t,n){return{range:e,newText:t,annotationId:n}},e.insert=function(e,t,n){return{range:{start:e,end:e},newText:t,annotationId:n}},e.del=function(e,t){return{range:e,newText:"",annotationId:t}},e.is=function(e){const t=e;return T.is(t)&&(S.is(t.annotationId)||C.is(t.annotationId))}}(R||(R={})),function(e){e.create=function(e,t){return{textDocument:e,edits:t}},e.is=function(e){const t=e;return je.defined(t)&&q.is(t.textDocument)&&Array.isArray(t.edits)}}(D||(D={})),function(e){e.create=function(e,t,n){const r={kind:"create",uri:e};return void 0===t||void 0===t.overwrite&&void 0===t.ignoreIfExists||(r.options=t),void 0!==n&&(r.annotationId=n),r},e.is=function(e){const t=e;return t&&"create"===t.kind&&je.string(t.uri)&&(void 0===t.options||(void 0===t.options.overwrite||je.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||je.boolean(t.options.ignoreIfExists)))&&(void 0===t.annotationId||C.is(t.annotationId))}}(w||(w={})),function(e){e.create=function(e,t,n,r){const o={kind:"rename",oldUri:e,newUri:t};return void 0===n||void 0===n.overwrite&&void 0===n.ignoreIfExists||(o.options=n),void 0!==r&&(o.annotationId=r),o},e.is=function(e){const t=e;return t&&"rename"===t.kind&&je.string(t.oldUri)&&je.string(t.newUri)&&(void 0===t.options||(void 0===t.options.overwrite||je.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||je.boolean(t.options.ignoreIfExists)))&&(void 0===t.annotationId||C.is(t.annotationId))}}(x||(x={})),function(e){e.create=function(e,t,n){const r={kind:"delete",uri:e};return void 0===t||void 0===t.recursive&&void 0===t.ignoreIfNotExists||(r.options=t),void 0!==n&&(r.annotationId=n),r},e.is=function(e){const t=e;return t&&"delete"===t.kind&&je.string(t.uri)&&(void 0===t.options||(void 0===t.options.recursive||je.boolean(t.options.recursive))&&(void 0===t.options.ignoreIfNotExists||je.boolean(t.options.ignoreIfNotExists)))&&(void 0===t.annotationId||C.is(t.annotationId))}}(P||(P={})),function(e){e.is=function(e){const t=e;return t&&(void 0!==t.changes||void 0!==t.documentChanges)&&(void 0===t.documentChanges||t.documentChanges.every((e=>je.string(e.kind)?w.is(e)||x.is(e)||P.is(e):D.is(e))))}}(O||(O={}));class Me{constructor(e,t){this.edits=e,this.changeAnnotations=t}insert(e,t,n){let r,o;if(void 0===n?r=T.insert(e,t):C.is(n)?(o=n,r=R.insert(e,t,n)):(this.assertChangeAnnotations(this.changeAnnotations),o=this.changeAnnotations.manage(n),r=R.insert(e,t,o)),this.edits.push(r),void 0!==o)return o}replace(e,t,n){let r,o;if(void 0===n?r=T.replace(e,t):C.is(n)?(o=n,r=R.replace(e,t,n)):(this.assertChangeAnnotations(this.changeAnnotations),o=this.changeAnnotations.manage(n),r=R.replace(e,t,o)),this.edits.push(r),void 0!==o)return o}delete(e,t){let n,r;if(void 0===t?n=T.del(e):C.is(t)?(r=t,n=R.del(e,t)):(this.assertChangeAnnotations(this.changeAnnotations),r=this.changeAnnotations.manage(t),n=R.del(e,r)),this.edits.push(n),void 0!==r)return r}add(e){this.edits.push(e)}all(){return this.edits}clear(){this.edits.splice(0,this.edits.length)}assertChangeAnnotations(e){if(void 0===e)throw new Error("Text edit change is not configured to manage change annotations.")}}!function(e){e.is=function(e){const t=e;return je.objectLiteral(t)&&c.is(t.range)&&Ce.isSnippet(t.snippet)&&(void 0===t.annotationId||S.is(t.annotationId)||C.is(t.annotationId))}}(M||(M={}));class Ne{constructor(e){this._annotations=void 0===e?Object.create(null):e,this._counter=0,this._size=0}all(){return this._annotations}get size(){return this._size}manage(e,t){let n;if(C.is(e)?n=e:(n=this.nextId(),t=e),void 0!==this._annotations[n])throw new Error(`Id ${n} is already in use.`);if(void 0===t)throw new Error(`No annotation provided for id ${n}`);return this._annotations[n]=t,this._size++,n}nextId(){return this._counter++,this._counter.toString()}}class Ee{constructor(e){this._textEditChanges=Object.create(null),void 0!==e?(this._workspaceEdit=e,e.documentChanges?(this._changeAnnotations=new Ne(e.changeAnnotations),e.changeAnnotations=this._changeAnnotations.all(),e.documentChanges.forEach((e=>{if(D.is(e)){const t=new Me(e.edits,this._changeAnnotations);this._textEditChanges[e.textDocument.uri]=t}}))):e.changes&&Object.keys(e.changes).forEach((t=>{const n=new Me(e.changes[t]);this._textEditChanges[t]=n}))):this._workspaceEdit={}}get edit(){return this.initDocumentChanges(),void 0!==this._changeAnnotations&&(0===this._changeAnnotations.size?this._workspaceEdit.changeAnnotations=void 0:this._workspaceEdit.changeAnnotations=this._changeAnnotations.all()),this._workspaceEdit}getTextEditChange(e){if(q.is(e)){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");const t={uri:e.uri,version:e.version};let n=this._textEditChanges[t.uri];if(!n){const e=[],r={textDocument:t,edits:e};this._workspaceEdit.documentChanges.push(r),n=new Me(e,this._changeAnnotations),this._textEditChanges[t.uri]=n}return n}{if(this.initChanges(),void 0===this._workspaceEdit.changes)throw new Error("Workspace edit is not configured for normal text edit changes.");let t=this._textEditChanges[e];if(!t){const n=[];this._workspaceEdit.changes[e]=n,t=new Me(n),this._textEditChanges[e]=t}return t}}initDocumentChanges(){void 0===this._workspaceEdit.documentChanges&&void 0===this._workspaceEdit.changes&&(this._changeAnnotations=new Ne,this._workspaceEdit.documentChanges=[],this._workspaceEdit.changeAnnotations=this._changeAnnotations.all())}initChanges(){void 0===this._workspaceEdit.documentChanges&&void 0===this._workspaceEdit.changes&&(this._workspaceEdit.changes=Object.create(null))}createFile(e,t,n){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");let r,o,i;if(S.is(t)||C.is(t)?r=t:n=t,void 0===r?o=w.create(e,n):(i=C.is(r)?r:this._changeAnnotations.manage(r),o=w.create(e,n,i)),this._workspaceEdit.documentChanges.push(o),void 0!==i)return i}renameFile(e,t,n,r){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");let o,i,s;if(S.is(n)||C.is(n)?o=n:r=n,void 0===o?i=x.create(e,t,r):(s=C.is(o)?o:this._changeAnnotations.manage(o),i=x.create(e,t,r,s)),this._workspaceEdit.documentChanges.push(i),void 0!==s)return s}deleteFile(e,t,n){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");let r,o,i;if(S.is(t)||C.is(t)?r=t:n=t,void 0===r?o=P.create(e,n):(i=C.is(r)?r:this._changeAnnotations.manage(r),o=P.create(e,n,i)),this._workspaceEdit.documentChanges.push(o),void 0!==i)return i}}!function(e){e.create=function(e){return{uri:e}},e.is=function(e){const t=e;return je.defined(t)&&je.string(t.uri)}}(N||(N={})),function(e){e.create=function(e,t){return{uri:e,version:t}},e.is=function(e){const t=e;return je.defined(t)&&je.string(t.uri)&&je.integer(t.version)}}(E||(E={})),function(e){e.create=function(e,t){return{uri:e,version:t}},e.is=function(e){const t=e;return je.defined(t)&&je.string(t.uri)&&(null===t.version||je.integer(t.version))}}(q||(q={})),function(e){e.ABAP="abap",e.WindowsBat="bat",e.BibTeX="bibtex",e.Clojure="clojure",e.Coffeescript="coffeescript",e.C="c",e.CPP="cpp",e.CSharp="csharp",e.CSS="css",e.D="d",e.Delphi="pascal",e.Diff="diff",e.Dart="dart",e.Dockerfile="dockerfile",e.Elixir="elixir",e.Erlang="erlang",e.FSharp="fsharp",e.GitCommit="git-commit",e.GitRebase="rebase",e.Go="go",e.Groovy="groovy",e.Handlebars="handlebars",e.Haskell="haskell",e.HTML="html",e.Ini="ini",e.Java="java",e.JavaScript="javascript",e.JavaScriptReact="javascriptreact",e.JSON="json",e.LaTeX="latex",e.Less="less",e.Lua="lua",e.Makefile="makefile",e.Markdown="markdown",e.ObjectiveC="objective-c",e.ObjectiveCPP="objective-cpp",e.Pascal="pascal",e.Perl="perl",e.Perl6="perl6",e.PHP="php",e.Powershell="powershell",e.Pug="jade",e.Python="python",e.R="r",e.Razor="razor",e.Ruby="ruby",e.Rust="rust",e.SCSS="scss",e.SASS="sass",e.Scala="scala",e.ShaderLab="shaderlab",e.ShellScript="shellscript",e.SQL="sql",e.Swift="swift",e.TypeScript="typescript",e.TypeScriptReact="typescriptreact",e.TeX="tex",e.VisualBasic="vb",e.XML="xml",e.XSL="xsl",e.YAML="yaml"}(A||(A={})),function(e){e.create=function(e,t,n,r){return{uri:e,languageId:t,version:n,text:r}},e.is=function(e){const t=e;return je.defined(t)&&je.string(t.uri)&&je.string(t.languageId)&&je.integer(t.version)&&je.string(t.text)}}(j||(j={})),function(e){e.PlainText="plaintext",e.Markdown="markdown",e.is=function(t){const n=t;return n===e.PlainText||n===e.Markdown}}(I||(I={})),function(e){e.is=function(e){const t=e;return je.objectLiteral(e)&&I.is(t.kind)&&je.string(t.value)}}(L||(L={})),function(e){e.Text=1,e.Method=2,e.Function=3,e.Constructor=4,e.Field=5,e.Variable=6,e.Class=7,e.Interface=8,e.Module=9,e.Property=10,e.Unit=11,e.Value=12,e.Enum=13,e.Keyword=14,e.Snippet=15,e.Color=16,e.File=17,e.Reference=18,e.Folder=19,e.EnumMember=20,e.Constant=21,e.Struct=22,e.Event=23,e.Operator=24,e.TypeParameter=25}(F||(F={})),function(e){e.PlainText=1,e.Snippet=2}($||($={})),function(e){e.Deprecated=1}(W||(W={})),function(e){e.create=function(e,t,n){return{newText:e,insert:t,replace:n}},e.is=function(e){const t=e;return t&&je.string(t.newText)&&c.is(t.insert)&&c.is(t.replace)}}(V||(V={})),function(e){e.asIs=1,e.adjustIndentation=2}(U||(U={})),function(e){e.Replace=1,e.Merge=2}(H||(H={})),function(e){e.is=function(e){const t=e;return t&&(je.string(t.detail)||void 0===t.detail)&&(je.string(t.description)||void 0===t.description)}}(B||(B={})),function(e){e.create=function(e){return{label:e}}}(z||(z={})),function(e){e.create=function(e,t){return{items:e||[],isIncomplete:!!t}}}(K||(K={})),function(e){e.fromPlainText=function(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")},e.is=function(e){const t=e;return je.string(t)||je.objectLiteral(t)&&je.string(t.language)&&je.string(t.value)}}(J||(J={})),function(e){e.is=function(e){const t=e;return!!t&&je.objectLiteral(t)&&(L.is(t.contents)||J.is(t.contents)||je.typedArray(t.contents,J.is))&&(void 0===e.range||c.is(e.range))}}(G||(G={})),function(e){e.create=function(e,t){return t?{label:e,documentation:t}:{label:e}}}(X||(X={})),function(e){e.create=function(e,t,...n){const r={label:e};return je.defined(t)&&(r.documentation=t),je.defined(n)?r.parameters=n:r.parameters=[],r}}(Z||(Z={})),function(e){e.Text=1,e.Read=2,e.Write=3}(Y||(Y={})),function(e){e.create=function(e,t){const n={range:e};return je.number(t)&&(n.kind=t),n}}(Q||(Q={})),function(e){e.File=1,e.Module=2,e.Namespace=3,e.Package=4,e.Class=5,e.Method=6,e.Property=7,e.Field=8,e.Constructor=9,e.Enum=10,e.Interface=11,e.Function=12,e.Variable=13,e.Constant=14,e.String=15,e.Number=16,e.Boolean=17,e.Array=18,e.Object=19,e.Key=20,e.Null=21,e.EnumMember=22,e.Struct=23,e.Event=24,e.Operator=25,e.TypeParameter=26}(ee||(ee={})),function(e){e.Deprecated=1}(te||(te={})),function(e){e.create=function(e,t,n,r,o){const i={name:e,kind:t,location:{uri:r,range:n}};return o&&(i.containerName=o),i}}(ne||(ne={})),function(e){e.create=function(e,t,n,r){return void 0!==r?{name:e,kind:t,location:{uri:n,range:r}}:{name:e,kind:t,location:{uri:n}}}}(re||(re={})),function(e){e.create=function(e,t,n,r,o,i){const s={name:e,detail:t,kind:n,range:r,selectionRange:o};return void 0!==i&&(s.children=i),s},e.is=function(e){const t=e;return t&&je.string(t.name)&&je.number(t.kind)&&c.is(t.range)&&c.is(t.selectionRange)&&(void 0===t.detail||je.string(t.detail))&&(void 0===t.deprecated||je.boolean(t.deprecated))&&(void 0===t.children||Array.isArray(t.children))&&(void 0===t.tags||Array.isArray(t.tags))}}(oe||(oe={})),function(e){e.Empty="",e.QuickFix="quickfix",e.Refactor="refactor",e.RefactorExtract="refactor.extract",e.RefactorInline="refactor.inline",e.RefactorMove="refactor.move",e.RefactorRewrite="refactor.rewrite",e.Source="source",e.SourceOrganizeImports="source.organizeImports",e.SourceFixAll="source.fixAll",e.Notebook="notebook"}(ie||(ie={})),function(e){e.Invoked=1,e.Automatic=2}(se||(se={})),function(e){e.create=function(e,t,n){const r={diagnostics:e};return null!=t&&(r.only=t),null!=n&&(r.triggerKind=n),r},e.is=function(e){const t=e;return je.defined(t)&&je.typedArray(t.diagnostics,k.is)&&(void 0===t.only||je.typedArray(t.only,je.string))&&(void 0===t.triggerKind||t.triggerKind===se.Invoked||t.triggerKind===se.Automatic)}}(ae||(ae={})),function(e){e.LLMGenerated=1,e.is=function(t){return je.defined(t)&&t===e.LLMGenerated}}(ce||(ce={})),function(e){e.create=function(e,t,n){const r={title:e};let o=!0;return"string"==typeof t?(o=!1,r.kind=t):_.is(t)?r.command=t:r.edit=t,o&&void 0!==n&&(r.kind=n),r},e.is=function(e){const t=e;return t&&je.string(t.title)&&(void 0===t.diagnostics||je.typedArray(t.diagnostics,k.is))&&(void 0===t.kind||je.string(t.kind))&&(void 0!==t.edit||void 0!==t.command)&&(void 0===t.command||_.is(t.command))&&(void 0===t.isPreferred||je.boolean(t.isPreferred))&&(void 0===t.edit||O.is(t.edit))&&(void 0===t.tags||je.typedArray(t.tags,ce.is))}}(ue||(ue={})),function(e){e.create=function(e,t){const n={range:e};return je.defined(t)&&(n.data=t),n},e.is=function(e){const t=e;return je.defined(t)&&c.is(t.range)&&(je.undefined(t.command)||_.is(t.command))}}(le||(le={})),function(e){e.create=function(e,t){return{tabSize:e,insertSpaces:t}},e.is=function(e){const t=e;return je.defined(t)&&je.uinteger(t.tabSize)&&je.boolean(t.insertSpaces)}}(de||(de={})),function(e){e.create=function(e,t,n){return{range:e,target:t,data:n}},e.is=function(e){const t=e;return je.defined(t)&&c.is(t.range)&&(je.undefined(t.target)||je.string(t.target))}}(fe||(fe={})),function(e){e.create=function(e,t){return{range:e,parent:t}},e.is=function(t){const n=t;return je.objectLiteral(n)&&c.is(n.range)&&(void 0===n.parent||e.is(n.parent))}}(he||(he={})),function(e){e.namespace="namespace",e.type="type",e.class="class",e.enum="enum",e.interface="interface",e.struct="struct",e.typeParameter="typeParameter",e.parameter="parameter",e.variable="variable",e.property="property",e.enumMember="enumMember",e.event="event",e.function="function",e.method="method",e.macro="macro",e.keyword="keyword",e.modifier="modifier",e.comment="comment",e.string="string",e.number="number",e.regexp="regexp",e.operator="operator",e.decorator="decorator",e.label="label"}(pe||(pe={})),function(e){e.declaration="declaration",e.definition="definition",e.readonly="readonly",e.static="static",e.deprecated="deprecated",e.abstract="abstract",e.async="async",e.modification="modification",e.documentation="documentation",e.defaultLibrary="defaultLibrary"}(me||(me={})),function(e){e.is=function(e){const t=e;return je.objectLiteral(t)&&(void 0===t.resultId||"string"==typeof t.resultId)&&Array.isArray(t.data)&&(0===t.data.length||"number"==typeof t.data[0])}}(ge||(ge={})),function(e){e.create=function(e,t){return{range:e,text:t}},e.is=function(e){const t=e;return null!=t&&c.is(t.range)&&je.string(t.text)}}(ye||(ye={})),function(e){e.create=function(e,t,n){return{range:e,variableName:t,caseSensitiveLookup:n}},e.is=function(e){const t=e;return null!=t&&c.is(t.range)&&je.boolean(t.caseSensitiveLookup)&&(je.string(t.variableName)||void 0===t.variableName)}}(be||(be={})),function(e){e.create=function(e,t){return{range:e,expression:t}},e.is=function(e){const t=e;return null!=t&&c.is(t.range)&&(je.string(t.expression)||void 0===t.expression)}}(ve||(ve={})),function(e){e.create=function(e,t){return{frameId:e,stoppedLocation:t}},e.is=function(e){const t=e;return je.defined(t)&&c.is(e.stoppedLocation)}}(ke||(ke={})),function(e){e.Type=1,e.Parameter=2,e.is=function(e){return 1===e||2===e}}(_e||(_e={})),function(e){e.create=function(e){return{value:e}},e.is=function(e){const t=e;return je.objectLiteral(t)&&(void 0===t.tooltip||je.string(t.tooltip)||L.is(t.tooltip))&&(void 0===t.location||u.is(t.location))&&(void 0===t.command||_.is(t.command))}}(Te||(Te={})),function(e){e.create=function(e,t,n){const r={position:e,label:t};return void 0!==n&&(r.kind=n),r},e.is=function(e){const t=e;return je.objectLiteral(t)&&a.is(t.position)&&(je.string(t.label)||je.typedArray(t.label,Te.is))&&(void 0===t.kind||_e.is(t.kind))&&void 0===t.textEdits||je.typedArray(t.textEdits,T.is)&&(void 0===t.tooltip||je.string(t.tooltip)||L.is(t.tooltip))&&(void 0===t.paddingLeft||je.boolean(t.paddingLeft))&&(void 0===t.paddingRight||je.boolean(t.paddingRight))}}(Se||(Se={})),function(e){e.createSnippet=function(e){return{kind:"snippet",value:e}},e.isSnippet=function(e){const t=e;return je.objectLiteral(t)&&"snippet"===t.kind&&je.string(t.value)}}(Ce||(Ce={})),function(e){e.create=function(e,t,n,r){return{insertText:e,filterText:t,range:n,command:r}}}(Re||(Re={})),function(e){e.create=function(e){return{items:e}}}(De||(De={})),function(e){e.Invoked=1,e.Automatic=2}(we||(we={})),function(e){e.create=function(e,t){return{range:e,text:t}}}(xe||(xe={})),function(e){e.create=function(e,t){return{triggerKind:e,selectedCompletionInfo:t}}}(Pe||(Pe={})),function(e){e.is=function(e){const t=e;return je.objectLiteral(t)&&o.is(t.uri)&&je.string(t.name)}}(Oe||(Oe={}));const qe=["\n","\r\n","\r"];var Ae,je;!function(e){function t(e,n){if(e.length<=1)return e;const r=e.length/2|0,o=e.slice(0,r),i=e.slice(r);t(o,n),t(i,n);let s=0,a=0,c=0;for(;s<o.length&&a<i.length;){const t=n(o[s],i[a]);e[c++]=t<=0?o[s++]:i[a++]}for(;s<o.length;)e[c++]=o[s++];for(;a<i.length;)e[c++]=i[a++];return e}e.create=function(e,t,n,r){return new Ie(e,t,n,r)},e.is=function(e){const t=e;return!!(je.defined(t)&&je.string(t.uri)&&(je.undefined(t.languageId)||je.string(t.languageId))&&je.uinteger(t.lineCount)&&je.func(t.getText)&&je.func(t.positionAt)&&je.func(t.offsetAt))},e.applyEdits=function(e,n){let r=e.getText();const o=t(n,((e,t)=>{const n=e.range.start.line-t.range.start.line;return 0===n?e.range.start.character-t.range.start.character:n}));let i=r.length;for(let t=o.length-1;t>=0;t--){const n=o[t],s=e.offsetAt(n.range.start),a=e.offsetAt(n.range.end);if(!(a<=i))throw new Error("Overlapping edit");r=r.substring(0,s)+n.newText+r.substring(a,r.length),i=s}return r}}(Ae||(Ae={}));class Ie{constructor(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){const t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content}update(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0}getLineOffsets(){if(void 0===this._lineOffsets){const e=[],t=this._content;let n=!0;for(let r=0;r<t.length;r++){n&&(e.push(r),n=!1);const o=t.charAt(r);n="\r"===o||"\n"===o,"\r"===o&&r+1<t.length&&"\n"===t.charAt(r+1)&&r++}n&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);const t=this.getLineOffsets();let n=0,r=t.length;if(0===r)return a.create(0,e);for(;n<r;){const o=Math.floor((n+r)/2);t[o]>e?r=o:n=o+1}const o=n-1;return a.create(o,e-t[o])}offsetAt(e){const t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;const n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)}get lineCount(){return this.getLineOffsets().length}}!function(e){const t=Object.prototype.toString;e.defined=function(e){return void 0!==e},e.undefined=function(e){return void 0===e},e.boolean=function(e){return!0===e||!1===e},e.string=function(e){return"[object String]"===t.call(e)},e.number=function(e){return"[object Number]"===t.call(e)},e.numberRange=function(e,n,r){return"[object Number]"===t.call(e)&&n<=e&&e<=r},e.integer=function(e){return"[object Number]"===t.call(e)&&-2147483648<=e&&e<=2147483647},e.uinteger=function(e){return"[object Number]"===t.call(e)&&0<=e&&e<=2147483647},e.func=function(e){return"[object Function]"===t.call(e)},e.objectLiteral=function(e){return null!==e&&"object"==typeof e},e.typedArray=function(e,t){return Array.isArray(e)&&e.every(t)}}(je||(je={}))},7608:(e,t,n)=>{"use strict";var r;n.r(t),n.d(t,{URI:()=>o,Utils:()=>i}),(()=>{var e={975:e=>{function t(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function n(e,t){for(var n,r="",o=0,i=-1,s=0,a=0;a<=e.length;++a){if(a<e.length)n=e.charCodeAt(a);else{if(47===n)break;n=47}if(47===n){if(i===a-1||1===s);else if(i!==a-1&&2===s){if(r.length<2||2!==o||46!==r.charCodeAt(r.length-1)||46!==r.charCodeAt(r.length-2))if(r.length>2){var c=r.lastIndexOf("/");if(c!==r.length-1){-1===c?(r="",o=0):o=(r=r.slice(0,c)).length-1-r.lastIndexOf("/"),i=a,s=0;continue}}else if(2===r.length||1===r.length){r="",o=0,i=a,s=0;continue}t&&(r.length>0?r+="/..":r="..",o=2)}else r.length>0?r+="/"+e.slice(i+1,a):r=e.slice(i+1,a),o=a-i-1;i=a,s=0}else 46===n&&-1!==s?++s:s=-1}return r}var r={resolve:function(){for(var e,r="",o=!1,i=arguments.length-1;i>=-1&&!o;i--){var s;i>=0?s=arguments[i]:(void 0===e&&(e=process.cwd()),s=e),t(s),0!==s.length&&(r=s+"/"+r,o=47===s.charCodeAt(0))}return r=n(r,!o),o?r.length>0?"/"+r:"/":r.length>0?r:"."},normalize:function(e){if(t(e),0===e.length)return".";var r=47===e.charCodeAt(0),o=47===e.charCodeAt(e.length-1);return 0!==(e=n(e,!r)).length||r||(e="."),e.length>0&&o&&(e+="/"),r?"/"+e:e},isAbsolute:function(e){return t(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var e,n=0;n<arguments.length;++n){var o=arguments[n];t(o),o.length>0&&(void 0===e?e=o:e+="/"+o)}return void 0===e?".":r.normalize(e)},relative:function(e,n){if(t(e),t(n),e===n)return"";if((e=r.resolve(e))===(n=r.resolve(n)))return"";for(var o=1;o<e.length&&47===e.charCodeAt(o);++o);for(var i=e.length,s=i-o,a=1;a<n.length&&47===n.charCodeAt(a);++a);for(var c=n.length-a,u=s<c?s:c,l=-1,d=0;d<=u;++d){if(d===u){if(c>u){if(47===n.charCodeAt(a+d))return n.slice(a+d+1);if(0===d)return n.slice(a+d)}else s>u&&(47===e.charCodeAt(o+d)?l=d:0===d&&(l=0));break}var f=e.charCodeAt(o+d);if(f!==n.charCodeAt(a+d))break;47===f&&(l=d)}var h="";for(d=o+l+1;d<=i;++d)d!==i&&47!==e.charCodeAt(d)||(0===h.length?h+="..":h+="/..");return h.length>0?h+n.slice(a+l):(a+=l,47===n.charCodeAt(a)&&++a,n.slice(a))},_makeLong:function(e){return e},dirname:function(e){if(t(e),0===e.length)return".";for(var n=e.charCodeAt(0),r=47===n,o=-1,i=!0,s=e.length-1;s>=1;--s)if(47===(n=e.charCodeAt(s))){if(!i){o=s;break}}else i=!1;return-1===o?r?"/":".":r&&1===o?"//":e.slice(0,o)},basename:function(e,n){if(void 0!==n&&"string"!=typeof n)throw new TypeError('"ext" argument must be a string');t(e);var r,o=0,i=-1,s=!0;if(void 0!==n&&n.length>0&&n.length<=e.length){if(n.length===e.length&&n===e)return"";var a=n.length-1,c=-1;for(r=e.length-1;r>=0;--r){var u=e.charCodeAt(r);if(47===u){if(!s){o=r+1;break}}else-1===c&&(s=!1,c=r+1),a>=0&&(u===n.charCodeAt(a)?-1==--a&&(i=r):(a=-1,i=c))}return o===i?i=c:-1===i&&(i=e.length),e.slice(o,i)}for(r=e.length-1;r>=0;--r)if(47===e.charCodeAt(r)){if(!s){o=r+1;break}}else-1===i&&(s=!1,i=r+1);return-1===i?"":e.slice(o,i)},extname:function(e){t(e);for(var n=-1,r=0,o=-1,i=!0,s=0,a=e.length-1;a>=0;--a){var c=e.charCodeAt(a);if(47!==c)-1===o&&(i=!1,o=a+1),46===c?-1===n?n=a:1!==s&&(s=1):-1!==n&&(s=-1);else if(!i){r=a+1;break}}return-1===n||-1===o||0===s||1===s&&n===o-1&&n===r+1?"":e.slice(n,o)},format:function(e){if(null===e||"object"!=typeof e)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return function(e,t){var n=t.dir||t.root,r=t.base||(t.name||"")+(t.ext||"");return n?n===t.root?n+r:n+"/"+r:r}(0,e)},parse:function(e){t(e);var n={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return n;var r,o=e.charCodeAt(0),i=47===o;i?(n.root="/",r=1):r=0;for(var s=-1,a=0,c=-1,u=!0,l=e.length-1,d=0;l>=r;--l)if(47!==(o=e.charCodeAt(l)))-1===c&&(u=!1,c=l+1),46===o?-1===s?s=l:1!==d&&(d=1):-1!==s&&(d=-1);else if(!u){a=l+1;break}return-1===s||-1===c||0===d||1===d&&s===c-1&&s===a+1?-1!==c&&(n.base=n.name=0===a&&i?e.slice(1,c):e.slice(a,c)):(0===a&&i?(n.name=e.slice(1,s),n.base=e.slice(1,c)):(n.name=e.slice(a,s),n.base=e.slice(a,c)),n.ext=e.slice(s,c)),a>0?n.dir=e.slice(0,a-1):i&&(n.dir="/"),n},sep:"/",delimiter:":",win32:null,posix:null};r.posix=r,e.exports=r}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};let i;if(n.r(o),n.d(o,{URI:()=>h,Utils:()=>w}),"object"==typeof process)i="win32"===process.platform;else if("object"==typeof navigator){let e=navigator.userAgent;i=e.indexOf("Windows")>=0}const s=/^\w[\w\d+.-]*$/,a=/^\//,c=/^\/\//;function u(e,t){if(!e.scheme&&t)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!s.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path)if(e.authority){if(!a.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(c.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}const l="",d="/",f=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class h{static isUri(e){return e instanceof h||!!e&&"string"==typeof e.authority&&"string"==typeof e.fragment&&"string"==typeof e.path&&"string"==typeof e.query&&"string"==typeof e.scheme&&"string"==typeof e.fsPath&&"function"==typeof e.with&&"function"==typeof e.toString}scheme;authority;path;query;fragment;constructor(e,t,n,r,o,i=!1){"object"==typeof e?(this.scheme=e.scheme||l,this.authority=e.authority||l,this.path=e.path||l,this.query=e.query||l,this.fragment=e.fragment||l):(this.scheme=function(e,t){return e||t?e:"file"}(e,i),this.authority=t||l,this.path=function(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==d&&(t=d+t):t=d}return t}(this.scheme,n||l),this.query=r||l,this.fragment=o||l,u(this,i))}get fsPath(){return v(this,!1)}with(e){if(!e)return this;let{scheme:t,authority:n,path:r,query:o,fragment:i}=e;return void 0===t?t=this.scheme:null===t&&(t=l),void 0===n?n=this.authority:null===n&&(n=l),void 0===r?r=this.path:null===r&&(r=l),void 0===o?o=this.query:null===o&&(o=l),void 0===i?i=this.fragment:null===i&&(i=l),t===this.scheme&&n===this.authority&&r===this.path&&o===this.query&&i===this.fragment?this:new m(t,n,r,o,i)}static parse(e,t=!1){const n=f.exec(e);return n?new m(n[2]||l,S(n[4]||l),S(n[5]||l),S(n[7]||l),S(n[9]||l),t):new m(l,l,l,l,l)}static file(e){let t=l;if(i&&(e=e.replace(/\\/g,d)),e[0]===d&&e[1]===d){const n=e.indexOf(d,2);-1===n?(t=e.substring(2),e=d):(t=e.substring(2,n),e=e.substring(n)||d)}return new m("file",t,e,l,l)}static from(e){const t=new m(e.scheme,e.authority,e.path,e.query,e.fragment);return u(t,!0),t}toString(e=!1){return k(this,e)}toJSON(){return this}static revive(e){if(e){if(e instanceof h)return e;{const t=new m(e);return t._formatted=e.external,t._fsPath=e._sep===p?e.fsPath:null,t}}return e}}const p=i?1:void 0;class m extends h{_formatted=null;_fsPath=null;get fsPath(){return this._fsPath||(this._fsPath=v(this,!1)),this._fsPath}toString(e=!1){return e?k(this,!0):(this._formatted||(this._formatted=k(this,!1)),this._formatted)}toJSON(){const e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=p),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}}const g={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function y(e,t,n){let r,o=-1;for(let i=0;i<e.length;i++){const s=e.charCodeAt(i);if(s>=97&&s<=122||s>=65&&s<=90||s>=48&&s<=57||45===s||46===s||95===s||126===s||t&&47===s||n&&91===s||n&&93===s||n&&58===s)-1!==o&&(r+=encodeURIComponent(e.substring(o,i)),o=-1),void 0!==r&&(r+=e.charAt(i));else{void 0===r&&(r=e.substr(0,i));const t=g[s];void 0!==t?(-1!==o&&(r+=encodeURIComponent(e.substring(o,i)),o=-1),r+=t):-1===o&&(o=i)}}return-1!==o&&(r+=encodeURIComponent(e.substring(o))),void 0!==r?r:e}function b(e){let t;for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);35===r||63===r?(void 0===t&&(t=e.substr(0,n)),t+=g[r]):void 0!==t&&(t+=e[n])}return void 0!==t?t:e}function v(e,t){let n;return n=e.authority&&e.path.length>1&&"file"===e.scheme?`//${e.authority}${e.path}`:47===e.path.charCodeAt(0)&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&58===e.path.charCodeAt(2)?t?e.path.substr(1):e.path[1].toLowerCase()+e.path.substr(2):e.path,i&&(n=n.replace(/\//g,"\\")),n}function k(e,t){const n=t?b:y;let r="",{scheme:o,authority:i,path:s,query:a,fragment:c}=e;if(o&&(r+=o,r+=":"),(i||"file"===o)&&(r+=d,r+=d),i){let e=i.indexOf("@");if(-1!==e){const t=i.substr(0,e);i=i.substr(e+1),e=t.lastIndexOf(":"),-1===e?r+=n(t,!1,!1):(r+=n(t.substr(0,e),!1,!1),r+=":",r+=n(t.substr(e+1),!1,!0)),r+="@"}i=i.toLowerCase(),e=i.lastIndexOf(":"),-1===e?r+=n(i,!1,!0):(r+=n(i.substr(0,e),!1,!0),r+=i.substr(e))}if(s){if(s.length>=3&&47===s.charCodeAt(0)&&58===s.charCodeAt(2)){const e=s.charCodeAt(1);e>=65&&e<=90&&(s=`/${String.fromCharCode(e+32)}:${s.substr(3)}`)}else if(s.length>=2&&58===s.charCodeAt(1)){const e=s.charCodeAt(0);e>=65&&e<=90&&(s=`${String.fromCharCode(e+32)}:${s.substr(2)}`)}r+=n(s,!0,!1)}return a&&(r+="?",r+=n(a,!1,!1)),c&&(r+="#",r+=t?c:y(c,!1,!1)),r}function _(e){try{return decodeURIComponent(e)}catch{return e.length>3?e.substr(0,3)+_(e.substr(3)):e}}const T=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function S(e){return e.match(T)?e.replace(T,(e=>_(e))):e}var C=n(975);const R=C.posix||C,D="/";var w;!function(e){e.joinPath=function(e,...t){return e.with({path:R.join(e.path,...t)})},e.resolvePath=function(e,...t){let n=e.path,r=!1;n[0]!==D&&(n=D+n,r=!0);let o=R.resolve(n,...t);return r&&o[0]===D&&!e.authority&&(o=o.substring(1)),e.with({path:o})},e.dirname=function(e){if(0===e.path.length||e.path===D)return e;let t=R.dirname(e.path);return 1===t.length&&46===t.charCodeAt(0)&&(t=""),e.with({path:t})},e.basename=function(e){return R.basename(e.path)},e.extname=function(e){return R.extname(e.path)}}(w||(w={})),r=o})();const{URI:o,Utils:i}=r}};
//# sourceMappingURL=http://go/sourcemap/sourcemaps/3af362bc7c6ffdde67ee75328bc9be679d6f3a40/extensions/json-language-features/server\dist\node/875.jsonServerMain.js.map